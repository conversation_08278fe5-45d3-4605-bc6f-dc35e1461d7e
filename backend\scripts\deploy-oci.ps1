# Setup OCI CLI and Docker
$ErrorActionPreference = "Stop"

# Variables
$IMAGE_NAME = "medtrack-api"
$OCI_REGION = "your-oci-region"  # e.g. us-phoenix-1
$OCI_REGISTRY = "$OCI_REGION.ocir.io"
$OCI_NAMESPACE = "your-namespace"
$OCI_REPOSITORY = "$OCI_NAMESPACE/$IMAGE_NAME"
$VERSION = Get-Date -Format "yyyyMMdd-HHmm"

Write-Host "🚀 Starting deployment process..."

# Build the Docker image
Write-Host "📦 Building Docker image..."
docker build -t "$IMAGE_NAME`:$VERSION" -f Dockerfile .
if ($LASTEXITCODE -ne 0) { 
    Write-Error "Docker build failed"
    exit 1
}

# Tag the image for OCI Registry
Write-Host "🏷️ Tagging image for OCI Registry..."
docker tag "$IMAGE_NAME`:$VERSION" "$OCI_REGISTRY/$OCI_REPOSITORY`:$VERSION"
docker tag "$IMAGE_NAME`:$VERSION" "$OCI_REGISTRY/$OCI_REPOSITORY`:latest"

# Push to OCI Registry
Write-Host "⬆️ Pushing to OCI Registry..."
docker push "$OCI_REGISTRY/$OCI_REPOSITORY`:$VERSION"
docker push "$OCI_REGISTRY/$OCI_REPOSITORY`:latest"

# Deploy using OCI CLI
Write-Host "🔄 Updating Container Instance..."
oci container-instances container-instance update `
    --container-instance-id "your-container-instance-id" `
    --containers '[{
        "imageUrl": "'$OCI_REGISTRY/$OCI_REPOSITORY`:$VERSION'",
        "displayName": "medtrack-api",
        "environmentVariables": {
            "NODE_ENV": "production"
        }
    }]'

Write-Host "✅ Deployment completed!"
