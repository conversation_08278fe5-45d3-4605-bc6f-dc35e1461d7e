import { Repository } from 'typeorm';
import { User } from '../../entities/user.entity';
export declare enum AuditEventType {
    LOGIN_SUCCESS = "LOGIN_SUCCESS",
    LOGIN_FAILURE = "LOGIN_FAILURE",
    LOGOUT = "LOGOUT",
    PASSWORD_CHANGE = "PASSWORD_CHANGE",
    PASSWORD_RESET_REQUEST = "PASSWORD_RESET_REQUEST",
    PASSWORD_RESET_COMPLETE = "PASSWORD_RESET_COMPLETE",
    USER_CREATED = "USER_CREATED",
    USER_UPDATED = "USER_UPDATED",
    USER_DELETED = "USER_DELETED",
    ROLE_CHANGED = "ROLE_CHANGED",
    TOKEN_REFRESH = "TOKEN_REFRESH",
    TOKEN_REVOKED = "TOKEN_REVOKED"
}
export interface AuditLogEntry {
    id: string;
    eventType: AuditEventType;
    userId: string;
    userEmail: string;
    ipAddress: string;
    userAgent: string;
    eventData?: any;
    timestamp: Date;
}
export declare class AuditLogService {
    private userRepository;
    private readonly logger;
    constructor(userRepository: Repository<User>);
    log(eventType: AuditEventType, userId: string, ipAddress: string, userAgent: string, eventData?: any): Promise<void>;
    private isSecurityCriticalEvent;
    private handleSecurityCriticalEvent;
    getRecentFailedLogins(userId: string, minutes?: number): Promise<number>;
}
