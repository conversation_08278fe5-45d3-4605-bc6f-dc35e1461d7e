# Validate production environment script
$ErrorActionPreference = "Stop"

Write-Host "🔍 Validating Production Environment..." -ForegroundColor Cyan

# Check required environment variables
$requiredEnvVars = @(
    "POSTGRES_HOST",
    "POSTGRES_PORT",
    "POSTGRES_USER",
    "POSTGRES_PASSWORD",
    "POSTGRES_DB",
    "JWT_SECRET",
    "REDIS_HOST",
    "REDIS_PASSWORD",
    "NODE_ENV"
)

Write-Host "`n📋 Checking environment variables..."
foreach ($var in $requiredEnvVars) {
    if (-not [Environment]::GetEnvironmentVariable($var)) {
        Write-Host "❌ Missing required environment variable: $var" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ Found $var" -ForegroundColor Green
}

# Test database connection
Write-Host "`n🗄️ Testing database connection..."
try {
    $env:PGPASSWORD = [Environment]::GetEnvironmentVariable("POSTGRES_PASSWORD")
    $dbHost = [Environment]::GetEnvironmentVariable("POSTGRES_HOST")
    $dbPort = [Environment]::GetEnvironmentVariable("POSTGRES_PORT")
    $dbUser = [Environment]::GetEnvironmentVariable("POSTGRES_USER")
    $dbName = [Environment]::GetEnvironmentVariable("POSTGRES_DB")
    
    $result = psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -c "\conninfo"
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Database connection successful" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to connect to database" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Database connection error: $_" -ForegroundColor Red
    exit 1
}

# Verify Redis connection
Write-Host "`n📎 Testing Redis connection..."
try {
    $redisHost = [Environment]::GetEnvironmentVariable("REDIS_HOST")
    $redisPassword = [Environment]::GetEnvironmentVariable("REDIS_PASSWORD")
    
    $result = redis-cli -h $redisHost -a $redisPassword ping
    if ($result -eq "PONG") {
        Write-Host "✅ Redis connection successful" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to connect to Redis" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Redis connection error: $_" -ForegroundColor Red
    exit 1
}

# Check for required files
Write-Host "`n📁 Checking required files..."
$requiredFiles = @(
    "Dockerfile",
    ".env.production",
    "nest-cli.json",
    "tsconfig.json"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✅ Found $file" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing required file: $file" -ForegroundColor Red
        exit 1
    }
}

# Validate TypeScript configuration
Write-Host "`n⚙️ Validating TypeScript configuration..."
try {
    pnpm tsc --noEmit
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ TypeScript validation passed" -ForegroundColor Green
    } else {
        Write-Host "❌ TypeScript validation failed" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ TypeScript validation error: $_" -ForegroundColor Red
    exit 1
}

# Run tests
Write-Host "`n🧪 Running tests..."
try {
    pnpm test
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Tests passed" -ForegroundColor Green
    } else {
        Write-Host "❌ Tests failed" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Test execution error: $_" -ForegroundColor Red
    exit 1
}

# Check disk space
Write-Host "`n💾 Checking disk space..."
$drive = Get-CimInstance -ClassName Win32_LogicalDisk -Filter "DeviceID='C:'"
$freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
$totalSpaceGB = [math]::Round($drive.Size / 1GB, 2)
$freeSpacePercent = [math]::Round(($drive.FreeSpace / $drive.Size) * 100, 2)

if ($freeSpacePercent -lt 20) {
    Write-Host "⚠️ Warning: Low disk space! Only $freeSpaceGB GB ($freeSpacePercent%) available" -ForegroundColor Yellow
} else {
    Write-Host "✅ Sufficient disk space: $freeSpaceGB GB ($freeSpacePercent%) available" -ForegroundColor Green
}

Write-Host "`n✅ Production environment validation completed successfully!" -ForegroundColor Green
