import { Test, TestingModule } from '@nestjs/testing';
import { MaterialsService } from './materials.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Material } from '../../entities/materials.entity';
import { MaterialShare } from '../../entities/material_shares.entity';
import { ConfigService } from '@nestjs/config';

describe('MaterialsService', () => {
  let service: MaterialsService;

  const mockMaterialRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    delete: jest.fn(),
  };

  const mockMaterialShareRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn().mockImplementation((key: string) => {
      switch (key) {
        case 'AWS_S3_BUCKET':
          return 'test-bucket';
        default:
          return null;
      }
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MaterialsService,
        {
          provide: getRepositoryToken(Material),
          useValue: mockMaterialRepository,
        },
        {
          provide: getRepositoryToken(MaterialShare),
          useValue: mockMaterialShareRepository,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<MaterialsService>(MaterialsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});