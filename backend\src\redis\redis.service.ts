import { Injectable, Logger } from '@nestjs/common';
import Redis from 'ioredis';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class RedisService {
    private readonly redis: Redis;
    private readonly logger = new Logger(RedisService.name);
    private isConnected = false;
    private memoryStore: Map<string, { value: string; expiry?: number }> = new Map();

    constructor(private configService: ConfigService) {
        this.redis = new Redis({
            host: this.configService.get('REDIS_HOST', 'localhost'),
            port: this.configService.get('REDIS_PORT', 6379),
            password: this.configService.get('REDIS_PASSWORD'),
            db: this.configService.get('REDIS_DB', 0),
            retryStrategy: (times) => {
                const delay = Math.min(times * 50, 2000);
                return delay;
            },
            maxRetriesPerRequest: 3,
            enableOfflineQueue: false,
        });

        this.redis.on('connect', () => {
            this.isConnected = true;
            this.logger.log('Connected to Redis');
        });

        this.redis.on('error', (error) => {
            this.isConnected = false;
            this.logger.warn('Redis connection error, using in-memory fallback:', error.message);
        });

        // Clean up expired items from memory store periodically
        setInterval(() => {
            const now = Date.now();
            for (const [key, data] of this.memoryStore.entries()) {
                if (data.expiry && data.expiry < now) {
                    this.memoryStore.delete(key);
                }
            }
        }, 60000); // Check every minute
    }

    private async getFromMemory(key: string): Promise<string | null> {
        const data = this.memoryStore.get(key);
        if (!data) return null;
        if (data.expiry && data.expiry < Date.now()) {
            this.memoryStore.delete(key);
            return null;
        }
        return data.value;
    }

    private setInMemory(key: string, value: string, ttlSeconds?: number): void {
        const data: { value: string; expiry?: number } = { value };
        if (ttlSeconds) {
            data.expiry = Date.now() + (ttlSeconds * 1000);
        }
        this.memoryStore.set(key, data);
    }

    async get(key: string): Promise<string | null> {
        if (this.isConnected) {
            try {
                return await this.redis.get(key);
            } catch (error) {
                this.logger.warn('Redis get failed, falling back to memory:', error.message);
            }
        }
        return this.getFromMemory(key);
    }

    async set(key: string, value: string, ttlSeconds?: number): Promise<void> {
        this.setInMemory(key, value, ttlSeconds);
        if (this.isConnected) {
            try {
                if (ttlSeconds) {
                    await this.redis.setex(key, ttlSeconds, value);
                } else {
                    await this.redis.set(key, value);
                }
            } catch (error) {
                this.logger.warn('Redis set failed, using memory only:', error.message);
            }
        }
    }

    async del(key: string): Promise<void> {
        this.memoryStore.delete(key);
        if (this.isConnected) {
            try {
                await this.redis.del(key);
            } catch (error) {
                this.logger.warn('Redis del failed, using memory only:', error.message);
            }
        }
    }

    async exists(key: string): Promise<boolean> {
        if (this.isConnected) {
            try {
                const result = await this.redis.exists(key);
                return result === 1;
            } catch (error) {
                this.logger.warn('Redis exists failed, falling back to memory:', error.message);
            }
        }
        return this.memoryStore.has(key);
    }

    async increment(key: string): Promise<number> {
        if (this.isConnected) {
            try {
                return await this.redis.incr(key);
            } catch (error) {
                this.logger.warn('Redis increment failed, falling back to memory:', error.message);
            }
        }
        const current = await this.getFromMemory(key);
        const newValue = (parseInt(current || '0') + 1).toString();
        this.setInMemory(key, newValue);
        return parseInt(newValue);
    }

    async decrement(key: string): Promise<number> {
        return this.redis.decr(key);
    }

    async setHash(key: string, field: string, value: string): Promise<void> {
        if (this.isConnected) {
            try {
                await this.redis.hset(key, field, value);
            } catch (error) {
                this.logger.warn('Redis hset failed, using memory only:', error.message);
            }
        }
        const hashKey = `${key}:${field}`;
        this.setInMemory(hashKey, value);
    }

    async getHash(key: string, field: string): Promise<string | null> {
        if (this.isConnected) {
            try {
                return await this.redis.hget(key, field);
            } catch (error) {
                this.logger.warn('Redis hget failed, falling back to memory:', error.message);
            }
        }
        const hashKey = `${key}:${field}`;
        return this.getFromMemory(hashKey);
    }

    async getAllHash(key: string): Promise<Record<string, string>> {
        if (this.isConnected) {
            try {
                return await this.redis.hgetall(key);
            } catch (error) {
                this.logger.warn('Redis hgetall failed, falling back to memory:', error.message);
            }
        }
        const result: Record<string, string> = {};
        for (const [storeKey, data] of this.memoryStore.entries()) {
            if (storeKey.startsWith(`${key}:`)) {
                const field = storeKey.slice(key.length + 1);
                result[field] = data.value;
            }
        }
        return result;
    }

    async deleteHash(key: string, field: string): Promise<void> {
        const hashKey = `${key}:${field}`;
        this.memoryStore.delete(hashKey);
        if (this.isConnected) {
            try {
                await this.redis.hdel(key, field);
            } catch (error) {
                this.logger.warn('Redis hdel failed, using memory only:', error.message);
            }
        }
        if (!this.isConnected) {
            this.logger.warn('Redis not connected, skipping deleteHash operation');
            return;
        }
        await this.redis.hdel(key, field);
    }

    async setList(key: string, values: string[]): Promise<void> {
        await this.redis.rpush(key, ...values);
    }

    async getList(key: string, start: number, end: number): Promise<string[]> {
        if (!this.isConnected) {
            this.logger.warn('Redis not connected, returning empty array for getList operation');
            return [];
        }
        return this.redis.lrange(key, start, end);
    }

    async addToSet(key: string, value: string): Promise<void> {
        await this.redis.sadd(key, value);
    }

    async getSetMembers(key: string): Promise<string[]> {
        if (!this.isConnected) {
            this.logger.warn('Redis not connected, returning empty array for getSetMembers operation');
            return [];
        }
        return this.redis.smembers(key);
    }

    async removeFromSet(key: string, value: string): Promise<void> {
        await this.redis.srem(key, value);
    }

    async publish(channel: string, message: string): Promise<void> {
        await this.redis.publish(channel, message);
    }

    async subscribe(channel: string, callback: (message: string) => void): Promise<void> {
        if (!this.isConnected) {
            this.logger.warn('Redis not connected, skipping subscribe operation');
            return;
        }
        const subscriber = new Redis({
            host: this.configService.get('REDIS_HOST', 'localhost'),
            port: this.configService.get('REDIS_PORT', 6379),
            password: this.configService.get('REDIS_PASSWORD'),
            db: this.configService.get('REDIS_DB', 0),
        });

        await subscriber.subscribe(channel);
        subscriber.on('message', (ch: string, message: string) => {
            if (ch === channel) {
                callback(message);
            }
        });
    }

    async onApplicationShutdown(): Promise<void> {
        if (this.isConnected) {
            await this.redis.quit();
        }
    }
} 