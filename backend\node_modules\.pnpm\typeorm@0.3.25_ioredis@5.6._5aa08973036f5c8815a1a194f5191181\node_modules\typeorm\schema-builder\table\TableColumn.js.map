{"version": 3, "sources": ["../../src/schema-builder/table/TableColumn.ts"], "names": [], "mappings": ";;;AAEA;;GAEG;AACH,MAAa,WAAW;IAoJpB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,OAA4B;QAvJ/B,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;QA0BlD;;WAEG;QACH,eAAU,GAAY,KAAK,CAAA;QAE3B;;WAEG;QACH,gBAAW,GAAY,KAAK,CAAA;QAQ5B;;WAEG;QACH,cAAS,GAAY,KAAK,CAAA;QAE1B;;WAEG;QACH,aAAQ,GAAY,KAAK,CAAA;QAEzB;;WAEG;QACH,YAAO,GAAY,KAAK,CAAA;QAOxB;;;WAGG;QACH,WAAM,GAAW,EAAE,CAAA;QA8BnB;;;WAGG;QACH,aAAQ,GAAY,KAAK,CAAA;QAEzB;;WAEG;QACH,aAAQ,GAAY,KAAK,CAAA;QA+CrB,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;YACxB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,EAAE,CAAA;YAC9B,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,EAAE,CAAA;YAClC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;YAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;YAC9B,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;YAClC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;YAClC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;YAC1B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAA;YACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAA;YAChE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;YAC9B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;YAChC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,KAAK,CAAA;YAC7C,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,KAAK,CAAA;YAC/C,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAA;YACpD,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAA;YAClD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,KAAK,CAAA;YAC3C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAA;YACzC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,KAAK,CAAA;YACvC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;YAC9B,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;YACxB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;YAChC,IAAI,CAAC,wBAAwB,GAAG,OAAO,CAAC,wBAAwB,CAAA;YAChE,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAA;YACxC,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAA;YAC1C,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAA;YACpD,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;QAC5B,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK;QACD,OAAO,IAAI,WAAW,CAAqB;YACvC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,wBAAwB,EAAE,IAAI,CAAC,wBAAwB;YACvD,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,IAAI,EAAE,IAAI,CAAC,IAAI;SAClB,CAAC,CAAA;IACN,CAAC;CACJ;AA9ND,kCA8NC", "file": "TableColumn.js", "sourcesContent": ["import { TableColumnOptions } from \"../options/TableColumnOptions\"\n\n/**\n * Table's columns in the database represented in this class.\n */\nexport class TableColumn {\n    readonly \"@instanceof\" = Symbol.for(\"TableColumn\")\n\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Column name.\n     */\n    name: string\n\n    /**\n     * Column type.\n     */\n    type: string\n\n    /**\n     * Column's default value.\n     */\n    default?: any\n\n    /**\n     * ON UPDATE trigger. Works only for MySQL.\n     */\n    onUpdate?: string\n\n    /**\n     * Indicates if column is NULL, or is NOT NULL in the database.\n     */\n    isNullable: boolean = false\n\n    /**\n     * Indicates if column is auto-generated sequence.\n     */\n    isGenerated: boolean = false\n\n    /**\n     * Specifies generation strategy if this column will use auto increment.\n     * `rowid` option supported only in CockroachDB.\n     */\n    generationStrategy?: \"uuid\" | \"increment\" | \"rowid\" | \"identity\"\n\n    /**\n     * Indicates if column is a primary key.\n     */\n    isPrimary: boolean = false\n\n    /**\n     * Indicates if column has unique value.\n     */\n    isUnique: boolean = false\n\n    /**\n     * Indicates if column stores array.\n     */\n    isArray: boolean = false\n\n    /**\n     * Column's comment.\n     */\n    comment?: string\n\n    /**\n     * Column type's length. Used only on some column types.\n     * For example type = \"string\" and length = \"100\" means that ORM will create a column with type varchar(100).\n     */\n    length: string = \"\"\n\n    /**\n     * Column type's display width. Used only on some column types in MySQL.\n     * For example, INT(4) specifies an INT with a display width of four digits.\n     */\n    width?: number\n\n    /**\n     * Defines column character set.\n     */\n    charset?: string\n\n    /**\n     * Defines column collation.\n     */\n    collation?: string\n\n    /**\n     * The precision for a decimal (exact numeric) column (applies only for decimal column), which is the maximum\n     * number of digits that are stored for the values.\n     */\n    precision?: number | null\n\n    /**\n     * The scale for a decimal (exact numeric) column (applies only for decimal column), which represents the number\n     * of digits to the right of the decimal point and must not be greater than precision.\n     */\n    scale?: number\n\n    /**\n     * Puts ZEROFILL attribute on to numeric column. Works only for MySQL.\n     * If you specify ZEROFILL for a numeric column, MySQL automatically adds the UNSIGNED attribute to the column\n     */\n    zerofill: boolean = false\n\n    /**\n     * Puts UNSIGNED attribute on to numeric column. Works only for MySQL.\n     */\n    unsigned: boolean = false\n\n    /**\n     * Array of possible enumerated values.\n     */\n    enum?: string[]\n\n    /**\n     * Exact name of enum\n     */\n    enumName?: string\n\n    /**\n     * Name of the primary key constraint for primary column.\n     */\n    primaryKeyConstraintName?: string\n\n    /**\n     * Generated column expression.\n     */\n    asExpression?: string\n\n    /**\n     * Generated column type.\n     */\n    generatedType?: \"VIRTUAL\" | \"STORED\"\n\n    /**\n     * Identity column type. Supports only in Postgres 10+.\n     */\n    generatedIdentity?: \"ALWAYS\" | \"BY DEFAULT\"\n\n    /**\n     * Spatial Feature Type (Geometry, Point, Polygon, etc.)\n     */\n    spatialFeatureType?: string\n\n    /**\n     * SRID (Spatial Reference ID (EPSG code))\n     */\n    srid?: number\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(options?: TableColumnOptions) {\n        if (options) {\n            this.name = options.name\n            this.type = options.type || \"\"\n            this.length = options.length || \"\"\n            this.width = options.width\n            this.charset = options.charset\n            this.collation = options.collation\n            this.precision = options.precision\n            this.scale = options.scale\n            this.zerofill = options.zerofill || false\n            this.unsigned = this.zerofill ? true : options.unsigned || false\n            this.default = options.default\n            this.onUpdate = options.onUpdate\n            this.isNullable = options.isNullable || false\n            this.isGenerated = options.isGenerated || false\n            this.generationStrategy = options.generationStrategy\n            this.generatedIdentity = options.generatedIdentity\n            this.isPrimary = options.isPrimary || false\n            this.isUnique = options.isUnique || false\n            this.isArray = options.isArray || false\n            this.comment = options.comment\n            this.enum = options.enum\n            this.enumName = options.enumName\n            this.primaryKeyConstraintName = options.primaryKeyConstraintName\n            this.asExpression = options.asExpression\n            this.generatedType = options.generatedType\n            this.spatialFeatureType = options.spatialFeatureType\n            this.srid = options.srid\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Clones this column to a new column with exact same properties as this column has.\n     */\n    clone(): TableColumn {\n        return new TableColumn(<TableColumnOptions>{\n            name: this.name,\n            type: this.type,\n            length: this.length,\n            width: this.width,\n            charset: this.charset,\n            collation: this.collation,\n            precision: this.precision,\n            scale: this.scale,\n            zerofill: this.zerofill,\n            unsigned: this.unsigned,\n            enum: this.enum,\n            enumName: this.enumName,\n            primaryKeyConstraintName: this.primaryKeyConstraintName,\n            asExpression: this.asExpression,\n            generatedType: this.generatedType,\n            default: this.default,\n            onUpdate: this.onUpdate,\n            isNullable: this.isNullable,\n            isGenerated: this.isGenerated,\n            generationStrategy: this.generationStrategy,\n            generatedIdentity: this.generatedIdentity,\n            isPrimary: this.isPrimary,\n            isUnique: this.isUnique,\n            isArray: this.isArray,\n            comment: this.comment,\n            spatialFeatureType: this.spatialFeatureType,\n            srid: this.srid,\n        })\n    }\n}\n"], "sourceRoot": "../.."}