declare module '@tensorflow/tfjs-node' {
  export class LayersModel {
    predict(input: any): Promise<any>;
    save(path: string): Promise<void>;
  }
  export class Tensor {
    data(): Promise<any>;
  }
  export function loadLayersModel(path: string): Promise<LayersModel>;
  export function sequential(): any;
  export const layers: {
    dense: (config: any) => any;
    dropout: (config: any) => any;
  };
  export function tensor2d(data: any): Tensor;
} 