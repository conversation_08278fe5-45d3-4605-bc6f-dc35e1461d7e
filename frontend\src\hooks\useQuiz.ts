import { useState, useEffect, useCallback } from 'react';
import { quizStorage } from '../services/quizStorage';
import { v4 as uuidv4 } from 'uuid';
import { syncService } from '@/lib/offline/syncService';

interface QuizQuestion {
  id: string;
  question_text: string;
  options: string[];
  correct_answer: string;
  explanation?: string;
}

interface QuizState {
  id: string;
  unitId: string;
  userId: string;
  currentQuestionIndex: number;
  answers: Record<string, string>;
  startTime: Date;
  lastModified: Date;
  synced: boolean;
}

export function useQuiz(unitId: string, userId: string) {
  const [questions, setQuestions] = useState<QuizQuestion[]>([]);
  const [currentState, setCurrentState] = useState<QuizState | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isOffline, setIsOffline] = useState(!navigator.onLine);

  // Handle online/offline status
  useEffect(() => {
    const handleOnline = () => setIsOffline(false);
    const handleOffline = () => setIsOffline(true);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Load quiz state
  useEffect(() => {
    const loadQuizState = async () => {
      try {
        setIsLoading(true);
        
        // Try to load from IndexedDB first
        const savedState = await quizStorage.getQuizState(userId, unitId);
        
        if (savedState) {
          setCurrentState(savedState);
        } else {
          // Create new state if none exists
          const newState: QuizState = {
            id: uuidv4(),
            unitId,
            userId,
            currentQuestionIndex: 0,
            answers: {},
            startTime: new Date(),
            lastModified: new Date(),
            synced: false,
          };
          await quizStorage.saveQuizState(newState);
          setCurrentState(newState);
        }

        // Load questions
        const response = await fetch(`/api/quiz/unit/${unitId}`);
        if (!response.ok) throw new Error('Failed to load questions');
        const data = await response.json();
        setQuestions(data);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to load quiz'));
      } finally {
        setIsLoading(false);
      }
    };

    loadQuizState();
  }, [unitId, userId]);

  const answerQuestion = useCallback(async (questionId: string, answer: string) => {
    if (!currentState) return;

    const newState = {
      ...currentState,
      answers: {
        ...currentState.answers,
        [questionId]: answer,
      },
      lastModified: new Date(),
    };

    // Save to IndexedDB
    await quizStorage.saveQuizState(newState);
    setCurrentState(newState);

    // Save response
    const response = {
      id: uuidv4(),
      userId,
      questionId,
      selectedAnswer: answer,
      isCorrect: questions.find(q => q.id === questionId)?.correct_answer === answer,
      timestamp: new Date(),
      synced: false,
    };

    await quizStorage.saveQuizResponse(response);

    // Try to sync if online
    if (!isOffline) {
      try {
        const result = await fetch('/api/quiz/submit', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(response),
        });

        if (result.ok) {
          await quizStorage.markResponsesAsSynced([response.id]);
        }
      } catch (err) {
        console.error('Failed to sync response:', err);
      }
    }

    // Add to sync queue
    await syncService.addToOutbox(
      `/api/quiz/submit/${currentState.id}`,
      'POST',
      {
        questionId,
        answer,
        timestamp: new Date().toISOString(),
      }
    );
  }, [currentState, userId, questions, isOffline]);

  const moveToNextQuestion = useCallback(() => {
    if (!currentState) return;

    const newState = {
      ...currentState,
      currentQuestionIndex: currentState.currentQuestionIndex + 1,
      lastModified: new Date(),
    };

    quizStorage.saveQuizState(newState);
    setCurrentState(newState);
  }, [currentState]);

  const moveToPreviousQuestion = useCallback(() => {
    if (!currentState || currentState.currentQuestionIndex === 0) return;

    const newState = {
      ...currentState,
      currentQuestionIndex: currentState.currentQuestionIndex - 1,
      lastModified: new Date(),
    };

    quizStorage.saveQuizState(newState);
    setCurrentState(newState);
  }, [currentState]);

  const getCurrentQuestion = useCallback(() => {
    if (!currentState || !questions.length) return null;
    return questions[currentState.currentQuestionIndex];
  }, [currentState, questions]);

  const getProgress = useCallback(() => {
    if (!currentState || !questions.length) return 0;
    return (Object.keys(currentState.answers).length / questions.length) * 100;
  }, [currentState, questions]);

  const completeQuiz = async () => {
    if (!currentState) return;

    const updatedState = {
      ...currentState,
      lastModified: new Date(),
      synced: false,
    };

    // Save to IndexedDB
    await quizStorage.saveQuizState(updatedState);
    setCurrentState(updatedState);

    // Add to sync queue
    await syncService.addToOutbox(
      `/api/quiz/complete/${currentState.id}`,
      'POST',
      {
        answers: currentState.answers,
        completionTime: new Date().toISOString(),
      }
    );
  };

  return {
    questions,
    currentState,
    isLoading,
    error,
    isOffline,
    currentQuestion: getCurrentQuestion(),
    progress: getProgress(),
    answerQuestion,
    moveToNextQuestion,
    moveToPreviousQuestion,
    completeQuiz,
  };
} 