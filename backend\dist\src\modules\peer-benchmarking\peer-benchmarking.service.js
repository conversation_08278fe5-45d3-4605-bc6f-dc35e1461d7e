"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PeerBenchmarkingService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const topic_progress_entity_1 = require("../../entities/topic-progress.entity");
const study_session_entity_1 = require("../../entities/study-session.entity");
const user_entity_1 = require("../../entities/user.entity");
let PeerBenchmarkingService = class PeerBenchmarkingService {
    constructor(topicProgressRepository, studySessionRepository, userRepository) {
        this.topicProgressRepository = topicProgressRepository;
        this.studySessionRepository = studySessionRepository;
        this.userRepository = userRepository;
    }
    async getPeerStats(userId, topicId) {
        const userProgress = await this.topicProgressRepository.findOne({
            where: { user: { id: userId }, topic: { id: topicId } },
            relations: ['user', 'topic']
        });
        if (!userProgress) {
            return {
                user_progress: {
                    completion_percentage: 0,
                    time_spent_minutes: 0,
                    streak_days: 0
                },
                peer_averages: {
                    completion_percentage: 0,
                    time_spent_minutes: 0,
                    streak_days: 0
                },
                percentile: 0
            };
        }
        const peerProgress = await this.topicProgressRepository
            .createQueryBuilder('progress')
            .where('progress.topic.id = :topicId', { topicId })
            .andWhere('progress.user.id != :userId', { userId })
            .getMany();
        const stats = {
            user_progress: {
                completion_percentage: userProgress.completion_percentage || 0,
                time_spent_minutes: userProgress.time_spent_minutes || 0,
                streak_days: userProgress.streak_days || 0
            },
            peer_averages: {
                completion_percentage: this.calculateAverage(peerProgress, 'completion_percentage'),
                time_spent_minutes: this.calculateAverage(peerProgress, 'time_spent_minutes'),
                streak_days: this.calculateAverage(peerProgress, 'streak_days')
            },
            percentile: this.calculatePercentile(userProgress, peerProgress)
        };
        return stats;
    }
    async getLeaderboard(topicId, limit = 10) {
        return this.topicProgressRepository
            .createQueryBuilder('progress')
            .where('progress.topic.id = :topicId', { topicId })
            .orderBy('progress.completion_percentage', 'DESC')
            .addOrderBy('progress.streak_days', 'DESC')
            .take(limit)
            .leftJoinAndSelect('progress.user', 'user')
            .select([
            'progress.completion_percentage',
            'progress.streak_days',
            'progress.time_spent_minutes',
            'user.id',
            'user.username'
        ])
            .getMany();
    }
    async getStudyGroupStats(groupId) {
        const groupMembers = await this.userRepository
            .createQueryBuilder('user')
            .where('user.study_group_id = :groupId', { groupId })
            .getMany();
        const memberIds = groupMembers.map((member) => member.id);
        if (!memberIds.length) {
            return {
                average_completion: 0,
                average_streak: 0,
                total_study_time: 0,
                member_count: 0
            };
        }
        const groupProgress = await this.topicProgressRepository
            .createQueryBuilder('progress')
            .where('progress.user.id IN (:...memberIds)', { memberIds })
            .getMany();
        return {
            average_completion: this.calculateAverage(groupProgress, 'completion_percentage'),
            average_streak: this.calculateAverage(groupProgress, 'streak_days'),
            total_study_time: groupProgress.reduce((sum, p) => sum + (p.time_spent_minutes || 0), 0),
            member_count: groupMembers.length
        };
    }
    calculateAverage(progress, field) {
        if (!progress.length)
            return 0;
        const sum = progress.reduce((acc, p) => acc + (p[field] || 0), 0);
        return sum / progress.length;
    }
    calculatePercentile(userProgress, peerProgress) {
        if (!userProgress || !peerProgress.length)
            return 0;
        const allProgress = [...peerProgress, userProgress];
        const sortedProgress = allProgress.sort((a, b) => (b.completion_percentage || 0) - (a.completion_percentage || 0));
        const userIndex = sortedProgress.findIndex(p => p.id === userProgress.id);
        return ((userIndex + 1) / sortedProgress.length) * 100;
    }
};
exports.PeerBenchmarkingService = PeerBenchmarkingService;
exports.PeerBenchmarkingService = PeerBenchmarkingService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(topic_progress_entity_1.TopicProgress)),
    __param(1, (0, typeorm_1.InjectRepository)(study_session_entity_1.StudySession)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], PeerBenchmarkingService);
//# sourceMappingURL=peer-benchmarking.service.js.map