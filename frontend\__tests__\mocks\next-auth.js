import React from 'react';

// Create a mock SessionProvider component
export const SessionProvider = ({ children }) => {
  return <>{children}</>;
};

export function signIn() {
  return Promise.resolve({ error: null });
}

export function signOut() {
  return Promise.resolve(true);
}

export function useSession() {
  return {
    data: {
      user: {
        name: 'Test User',
        email: '<EMAIL>',
      },
    },
    status: 'authenticated',
  };
}

export function getServerSession() {
  return Promise.resolve({
    user: {
      name: 'Test User',
      email: '<EMAIL>',
    },
  });
}
