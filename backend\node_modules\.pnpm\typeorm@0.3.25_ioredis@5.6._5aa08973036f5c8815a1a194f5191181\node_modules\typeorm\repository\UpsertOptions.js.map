{"version": 3, "sources": ["../../src/repository/UpsertOptions.ts"], "names": [], "mappings": "", "file": "UpsertOptions.js", "sourcesContent": ["import { InsertOrUpdateOptions } from \"../query-builder/InsertOrUpdateOptions\"\nimport { UpsertType } from \"../driver/types/UpsertType\"\n\n/**\n * Special options passed to Repository#upsert\n */\nexport interface UpsertOptions<Entity> extends InsertOrUpdateOptions {\n    conflictPaths: string[] | { [P in keyof Entity]?: true }\n\n    /**\n     * If true, postgres will skip the update if no values would be changed (reduces writes)\n     */\n    skipUpdateIfNoValuesChanged?: boolean\n\n    /**\n     * Define the type of upsert to use (currently, CockroachDB only).\n     *\n     * If none provided, it will use the default for the database (first one in the list)\n     */\n    upsertType?: UpsertType\n}\n"], "sourceRoot": ".."}