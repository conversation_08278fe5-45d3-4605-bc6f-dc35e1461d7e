export interface SM2Card {
    ef: number; // Easiness factor
    interval: number; // Interval in days
    repetitions: number; // Number of repetitions
    quality: number; // Quality of response (0-5)
}

export class SpacedRepetitionAlgorithm {
    private static readonly MIN_EF = 1.3;
    private static readonly INITIAL_EF = 2.5;
    private static readonly INITIAL_INTERVAL = 1;
    private static readonly SECOND_INTERVAL = 6;

    static calculateNextReview(card: SM2Card): SM2Card {
        let { ef, interval, repetitions, quality } = card;

        // Calculate new easiness factor
        ef = Math.max(
            this.MIN_EF,
            ef + (0.1 - (5 - quality) * (0.08 + (5 - quality) * 0.02))
        );

        // Calculate new interval
        if (repetitions === 0) {
            interval = this.INITIAL_INTERVAL;
        } else if (repetitions === 1) {
            interval = this.SECOND_INTERVAL;
        } else {
            interval = Math.round(interval * ef);
        }

        // Increment repetitions
        repetitions += 1;

        return {
            ef,
            interval,
            repetitions,
            quality,
        };
    }

    static createNewCard(): SM2Card {
        return {
            ef: this.INITIAL_EF,
            interval: this.INITIAL_INTERVAL,
            repetitions: 0,
            quality: 0,
        };
    }

    static getNextReviewDate(card: SM2Card): Date {
        const nextReview = new Date();
        nextReview.setDate(nextReview.getDate() + card.interval);
        return nextReview;
    }

    static isDue(card: SM2Card): boolean {
        const now = new Date();
        const nextReview = this.getNextReviewDate(card);
        return now >= nextReview;
    }

    static getDaysUntilReview(card: SM2Card): number {
        const now = new Date();
        const nextReview = this.getNextReviewDate(card);
        const diffTime = nextReview.getTime() - now.getTime();
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }

    static getQualityFromRating(rating: number): number {
        // Convert 1-5 rating to 0-5 quality score
        return Math.max(0, Math.min(5, rating - 1));
    }
} 