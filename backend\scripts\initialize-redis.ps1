Write-Host "🔄 Initializing Redis..." -ForegroundColor Green

# Connect to Redis and set initial configuration
Write-Host "Connecting to Redis..." -ForegroundColor Cyan
$redisPing = docker-compose -f docker-compose.prod.yml exec -T redis redis-cli -a $env:REDIS_PASSWORD ping
if ($redisPing -eq "PONG") {
    Write-Host "✅ Redis connection successful" -ForegroundColor Green

    # Set initial Redis keys or configuration if needed
    Write-Host "Setting initial Redis configuration..." -ForegroundColor Cyan
    docker-compose -f docker-compose.prod.yml exec -T redis redis-cli -a $env:REDIS_PASSWORD CONFIG SET save "900 1 300 10 60 10000"
    Write-Host "✅ Redis initialization complete" -ForegroundColor Green
} else {
    Write-Host "❌ Redis connection failed" -ForegroundColor Red
    exit 1
}
