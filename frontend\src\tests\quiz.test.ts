/**
 * Quiz Module Tests
 * 
 * This file contains tests for the quiz module.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import api from '../services/api';

// Mock the API service
vi.mock('../services/api', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

// Create a simple quiz service for testing
const quizService = {
  async getQuizByUnitId(unitId: string) {
    try {
      const response = await api.get(`/quiz/unit/${unitId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching quiz:', error);
      // Return mock quiz data
      return {
        id: unitId,
        title: 'Mock Quiz',
        questions: [
          {
            id: '1',
            text: 'What is the capital of France?',
            options: [
              { id: '1', text: 'London' },
              { id: '2', text: 'Paris' },
              { id: '3', text: 'Berlin' },
              { id: '4', text: 'Madrid' },
            ],
            correctOptionId: '2',
          },
          {
            id: '2',
            text: 'What is the largest organ in the human body?',
            options: [
              { id: '1', text: 'Heart' },
              { id: '2', text: 'Liver' },
              { id: '3', text: 'Skin' },
              { id: '4', text: 'Brain' },
            ],
            correctOptionId: '3',
          },
        ],
      };
    }
  },
  
  async submitQuiz(answers: any) {
    try {
      const response = await api.post('/quiz/submit', answers);
      return response.data;
    } catch (error) {
      console.error('Error submitting quiz:', error);
      // Return mock result
      return {
        score: 80,
        totalQuestions: 5,
        correctAnswers: 4,
        feedback: 'Good job!',
      };
    }
  },
  
  async getQuizResults(userId: string, unitId: string) {
    try {
      const response = await api.get(`/quiz/results/${userId}/${unitId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching quiz results:', error);
      // Return mock results
      return {
        userId,
        unitId,
        attempts: 2,
        bestScore: 90,
        lastAttemptDate: new Date().toISOString(),
      };
    }
  }
};

describe('Quiz Module', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Get Quiz by Unit ID', () => {
    it('should fetch quiz from API when available', async () => {
      const mockQuiz = {
        id: '1',
        title: 'Test Quiz',
        questions: [
          {
            id: '1',
            text: 'Test Question',
            options: [
              { id: '1', text: 'Option 1' },
              { id: '2', text: 'Option 2' },
            ],
            correctOptionId: '2',
          },
        ],
      };
      
      // Mock successful API response
      (api.get as any).mockResolvedValueOnce({ data: mockQuiz });
      
      const quiz = await quizService.getQuizByUnitId('1');
      
      expect(api.get).toHaveBeenCalledWith('/quiz/unit/1');
      expect(quiz).toEqual(mockQuiz);
    });

    it('should return mock quiz when API fails', async () => {
      // Mock API failure
      (api.get as any).mockRejectedValueOnce(new Error('API error'));
      
      const quiz = await quizService.getQuizByUnitId('1');
      
      expect(api.get).toHaveBeenCalledWith('/quiz/unit/1');
      expect(quiz).toBeDefined();
      expect(quiz.questions).toBeDefined();
      expect(quiz.questions.length).toBeGreaterThan(0);
    });
  });

  describe('Submit Quiz', () => {
    it('should submit quiz via API when available', async () => {
      const answers = {
        unitId: '1',
        userId: '1',
        answers: [
          { questionId: '1', selectedOptionId: '2' },
          { questionId: '2', selectedOptionId: '3' },
        ],
      };
      
      const mockResponse = {
        score: 100,
        totalQuestions: 2,
        correctAnswers: 2,
        feedback: 'Perfect!',
      };
      
      // Mock successful API response
      (api.post as any).mockResolvedValueOnce({ data: mockResponse });
      
      const result = await quizService.submitQuiz(answers);
      
      expect(api.post).toHaveBeenCalledWith('/quiz/submit', answers);
      expect(result).toEqual(mockResponse);
    });

    it('should return mock result when API fails', async () => {
      const answers = {
        unitId: '1',
        userId: '1',
        answers: [
          { questionId: '1', selectedOptionId: '2' },
        ],
      };
      
      // Mock API failure
      (api.post as any).mockRejectedValueOnce(new Error('API error'));
      
      const result = await quizService.submitQuiz(answers);
      
      expect(api.post).toHaveBeenCalledWith('/quiz/submit', answers);
      expect(result).toBeDefined();
      expect(result.score).toBeDefined();
      expect(result.totalQuestions).toBeDefined();
      expect(result.correctAnswers).toBeDefined();
    });
  });

  describe('Get Quiz Results', () => {
    it('should fetch quiz results from API when available', async () => {
      const mockResults = {
        userId: '1',
        unitId: '1',
        attempts: 3,
        bestScore: 95,
        lastAttemptDate: '2025-04-28T12:00:00Z',
      };
      
      // Mock successful API response
      (api.get as any).mockResolvedValueOnce({ data: mockResults });
      
      const results = await quizService.getQuizResults('1', '1');
      
      expect(api.get).toHaveBeenCalledWith('/quiz/results/1/1');
      expect(results).toEqual(mockResults);
    });

    it('should return mock results when API fails', async () => {
      // Mock API failure
      (api.get as any).mockRejectedValueOnce(new Error('API error'));
      
      const results = await quizService.getQuizResults('1', '1');
      
      expect(api.get).toHaveBeenCalledWith('/quiz/results/1/1');
      expect(results).toBeDefined();
      expect(results.userId).toBe('1');
      expect(results.unitId).toBe('1');
      expect(results.attempts).toBeDefined();
      expect(results.bestScore).toBeDefined();
    });
  });
});
