Write-Host "🚀 Deploying to Railway..." -ForegroundColor Green

# Ensure Railway CLI is installed
Write-Host "Checking Railway CLI installation..." -ForegroundColor Cyan
if (-not (Get-Command railway -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Railway CLI is not installed. Please install it from https://railway.app" -ForegroundColor Red
    exit 1
}

# Login to Railway
Write-Host "Logging into Railway..." -ForegroundColor Cyan
railway login

# Deploy the project
Write-Host "Deploying project to Railway..." -ForegroundColor Cyan
railway up

Write-Host "✅ Deployment complete!" -ForegroundColor Green
