{"version": 3, "sources": ["../../src/util/TreeRepositoryUtils.ts"], "names": [], "mappings": ";;;AAIA;;;GAGG;AACH,MAAa,mBAAmB;IAC5B,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E,MAAM,CAAC,kBAAkB,CACrB,OAAsB,EACtB,QAAwB,EACxB,KAAa,EACb,UAAiB;QAEjB,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;YAChC,MAAM,UAAU,GAAG,QAAQ,CAAC,kBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;YAC9D,MAAM,gBAAgB,GAClB,UAAU,CAAC,gBAAgB,IAAI,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAA;YAC7D,wFAAwF;YACxF,MAAM,cAAc,GAChB,UAAU,CAAC,iBAAiB,IAAI,UAAU,CAAC,YAAY,CAAA;YAC3D,MAAM,oBAAoB,GACtB,gBAAgB,CAAC,iBAAiB;gBAClC,gBAAgB,CAAC,YAAY,CAAA;YACjC,MAAM,EAAE,GAAG,SAAS,CAAC,KAAK,GAAG,GAAG,GAAG,oBAAoB,CAAC,CAAA;YACxD,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,GAAG,GAAG,GAAG,cAAc,CAAC,CAAA;YACxD,OAAO;gBACH,EAAE,EAAE,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,oBAAoB,CAC9C,EAAE,EACF,gBAAgB,CACnB;gBACD,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,oBAAoB,CACpD,QAAQ,EACR,UAAU,CACb;aACJ,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,MAAM,CAAC,uBAAuB,CAC1B,QAAwB,EACxB,MAAW,EACX,QAAe,EACf,YAA0C,EAC1C,OAA6C;QAE7C,MAAM,aAAa,GAAG,QAAQ,CAAC,oBAAqB,CAAC,YAAY,CAAA;QACjE,IAAI,OAAO,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;YACtB,MAAM,CAAC,aAAa,CAAC,GAAG,EAAE,CAAA;YAC1B,OAAM;QACV,CAAC;QACD,MAAM,UAAU,GAAG,QAAQ,CAAC,kBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;QAC9D,MAAM,gBAAgB,GAClB,UAAU,CAAC,gBAAgB,IAAI,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAA;QAC7D,MAAM,cAAc,GAAG,gBAAgB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAC9D,MAAM,iBAAiB,GAAG,YAAY,CAAC,MAAM,CACzC,CAAC,WAAW,EAAE,EAAE,CAAC,WAAW,CAAC,QAAQ,KAAK,cAAc,CAC3D,CAAA;QACD,MAAM,QAAQ,GAAG,IAAI,GAAG,CACpB,iBAAiB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,CACzD,CAAA;QACD,MAAM,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAC/C,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CACxD,CAAA;QACD,MAAM,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,WAAgB,EAAE,EAAE;YAC/C,mBAAmB,CAAC,uBAAuB,CACvC,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,YAAY,EACZ;gBACI,GAAG,OAAO;gBACV,KAAK,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC;aAC3B,CACJ,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,MAAM,CAAC,qBAAqB,CACxB,QAAwB,EACxB,MAAW,EACX,QAAe,EACf,YAA0C;QAE1C,MAAM,cAAc,GAAG,QAAQ,CAAC,kBAAmB,CAAC,YAAY,CAAA;QAChE,MAAM,UAAU,GAAG,QAAQ,CAAC,kBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;QAC9D,MAAM,gBAAgB,GAClB,UAAU,CAAC,gBAAgB,IAAI,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAA;QAC7D,MAAM,QAAQ,GAAG,gBAAgB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QACxD,MAAM,iBAAiB,GAAG,YAAY,CAAC,IAAI,CACvC,CAAC,WAAW,EAAE,EAAE,CAAC,WAAW,CAAC,EAAE,KAAK,QAAQ,CAC/C,CAAA;QACD,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;YAC1C,IAAI,CAAC,iBAAiB;gBAAE,OAAO,KAAK,CAAA;YAEpC,OAAO,CACH,gBAAgB,CAAC,cAAc,CAAC,MAAM,CAAC;gBACvC,iBAAiB,CAAC,QAAQ,CAC7B,CAAA;QACL,CAAC,CAAC,CAAA;QACF,IAAI,YAAY,EAAE,CAAC;YACf,MAAM,CAAC,cAAc,CAAC,GAAG,YAAY,CAAA;YACrC,mBAAmB,CAAC,qBAAqB,CACrC,QAAQ,EACR,MAAM,CAAC,cAAc,CAAC,EACtB,QAAQ,EACR,YAAY,CACf,CAAA;QACL,CAAC;IACL,CAAC;CACJ;AA3GD,kDA2GC", "file": "TreeRepositoryUtils.js", "sourcesContent": ["import { EntityManager } from \"../entity-manager/EntityManager\"\nimport { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { FindTreesOptions } from \"../repository/FindTreesOptions\"\n\n/**\n * Provides utilities for manipulating tree structures.\n *\n */\nexport class TreeRepositoryUtils {\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    static createRelationMaps(\n        manager: EntityManager,\n        metadata: EntityMetadata,\n        alias: string,\n        rawResults: any[],\n    ): { id: any; parentId: any }[] {\n        return rawResults.map((rawResult) => {\n            const joinColumn = metadata.treeParentRelation!.joinColumns[0]\n            const referencedColumn =\n                joinColumn.referencedColumn ?? metadata.primaryColumns[0]\n            // fixes issue #2518, default to databaseName property when givenDatabaseName is not set\n            const joinColumnName =\n                joinColumn.givenDatabaseName || joinColumn.databaseName\n            const referencedColumnName =\n                referencedColumn.givenDatabaseName ||\n                referencedColumn.databaseName\n            const id = rawResult[alias + \"_\" + referencedColumnName]\n            const parentId = rawResult[alias + \"_\" + joinColumnName]\n            return {\n                id: manager.connection.driver.prepareHydratedValue(\n                    id,\n                    referencedColumn,\n                ),\n                parentId: manager.connection.driver.prepareHydratedValue(\n                    parentId,\n                    joinColumn,\n                ),\n            }\n        })\n    }\n\n    static buildChildrenEntityTree(\n        metadata: EntityMetadata,\n        entity: any,\n        entities: any[],\n        relationMaps: { id: any; parentId: any }[],\n        options: FindTreesOptions & { depth: number },\n    ): void {\n        const childProperty = metadata.treeChildrenRelation!.propertyName\n        if (options.depth === 0) {\n            entity[childProperty] = []\n            return\n        }\n        const joinColumn = metadata.treeParentRelation!.joinColumns[0]\n        const referencedColumn =\n            joinColumn.referencedColumn ?? metadata.primaryColumns[0]\n        const parentEntityId = referencedColumn.getEntityValue(entity)\n        const childRelationMaps = relationMaps.filter(\n            (relationMap) => relationMap.parentId === parentEntityId,\n        )\n        const childIds = new Set(\n            childRelationMaps.map((relationMap) => relationMap.id),\n        )\n        entity[childProperty] = entities.filter((entity) =>\n            childIds.has(referencedColumn.getEntityValue(entity)),\n        )\n        entity[childProperty].forEach((childEntity: any) => {\n            TreeRepositoryUtils.buildChildrenEntityTree(\n                metadata,\n                childEntity,\n                entities,\n                relationMaps,\n                {\n                    ...options,\n                    depth: options.depth - 1,\n                },\n            )\n        })\n    }\n\n    static buildParentEntityTree(\n        metadata: EntityMetadata,\n        entity: any,\n        entities: any[],\n        relationMaps: { id: any; parentId: any }[],\n    ): void {\n        const parentProperty = metadata.treeParentRelation!.propertyName\n        const joinColumn = metadata.treeParentRelation!.joinColumns[0]\n        const referencedColumn =\n            joinColumn.referencedColumn ?? metadata.primaryColumns[0]\n        const entityId = referencedColumn.getEntityValue(entity)\n        const parentRelationMap = relationMaps.find(\n            (relationMap) => relationMap.id === entityId,\n        )\n        const parentEntity = entities.find((entity) => {\n            if (!parentRelationMap) return false\n\n            return (\n                referencedColumn.getEntityValue(entity) ===\n                parentRelationMap.parentId\n            )\n        })\n        if (parentEntity) {\n            entity[parentProperty] = parentEntity\n            TreeRepositoryUtils.buildParentEntityTree(\n                metadata,\n                entity[parentProperty],\n                entities,\n                relationMaps,\n            )\n        }\n    }\n}\n"], "sourceRoot": ".."}