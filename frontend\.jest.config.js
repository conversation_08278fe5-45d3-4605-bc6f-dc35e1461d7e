module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/.next/',
    '<rootDir>/__tests__/mocks/'
  ],
  testMatch: [
    '<rootDir>/__tests__/simple.test.tsx',
    '<rootDir>/__tests__/integration/app.test.tsx'
  ],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^next/navigation$': '<rootDir>/__tests__/mocks/next-navigation.js',
    '^next-auth/react$': '<rootDir>/__tests__/mocks/next-auth.js',
    '^next-auth/next$': '<rootDir>/__tests__/mocks/next-auth.js',
    '^@/store/useAuthStore$': '<rootDir>/__tests__/mocks/useAuthStore.js',
  },
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { configFile: './babel.jest.config.js' }],
  },
  transformIgnorePatterns: [
    '/node_modules/(?!next-auth|jose|openid-client)/',
  ],
};
