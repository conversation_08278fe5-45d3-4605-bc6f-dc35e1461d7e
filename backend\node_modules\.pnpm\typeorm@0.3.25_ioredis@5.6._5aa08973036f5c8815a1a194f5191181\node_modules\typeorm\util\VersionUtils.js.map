{"version": 3, "sources": ["../../src/util/VersionUtils.ts"], "names": [], "mappings": ";;;AAAA,MAAa,YAAY;IACrB,MAAM,CAAC,gBAAgB,CACnB,OAA2B,EAC3B,aAAqB;QAErB,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAA;QAChB,CAAC;QAED,MAAM,EAAE,GAAG,YAAY,CAAC,OAAO,CAAC,CAAA;QAChC,MAAM,EAAE,GAAG,YAAY,CAAC,aAAa,CAAC,CAAA;QAEtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAChB,OAAO,IAAI,CAAA;YACf,CAAC;iBAAM,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvB,OAAO,KAAK,CAAA;YAChB,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAA;IACf,CAAC;CACJ;AAtBD,oCAsBC;AAED,SAAS,YAAY,CAAC,OAAe;IACjC,OAAO,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAA;AACjE,CAAC", "file": "VersionUtils.js", "sourcesContent": ["export class VersionUtils {\n    static isGreaterOrEqual(\n        version: string | undefined,\n        targetVersion: string,\n    ): boolean {\n        if (!version) {\n            return false\n        }\n\n        const v1 = parseVersion(version)\n        const v2 = parseVersion(targetVersion)\n\n        for (let i = 0; i < v1.length && i < v2.length; i++) {\n            if (v1[i] > v2[i]) {\n                return true\n            } else if (v1[i] < v2[i]) {\n                return false\n            }\n        }\n\n        return true\n    }\n}\n\nfunction parseVersion(version: string): number[] {\n    return version.split(\".\").map((value) => parseInt(value, 10))\n}\n"], "sourceRoot": ".."}