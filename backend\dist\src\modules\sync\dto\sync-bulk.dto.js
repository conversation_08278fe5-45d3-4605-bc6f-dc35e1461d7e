"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SyncBulkDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const quiz_attempt_sync_dto_1 = require("./quiz-attempt-sync.dto");
const progress_sync_dto_1 = require("./progress-sync.dto");
const swagger_1 = require("@nestjs/swagger");
class SyncBulkDto {
}
exports.SyncBulkDto = SyncBulkDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: [quiz_attempt_sync_dto_1.QuizAttemptSyncDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => quiz_attempt_sync_dto_1.QuizAttemptSyncDto),
    __metadata("design:type", Array)
], SyncBulkDto.prototype, "quizAttempts", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [progress_sync_dto_1.ProgressSyncDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => progress_sync_dto_1.ProgressSyncDto),
    __metadata("design:type", Array)
], SyncBulkDto.prototype, "progress", void 0);
//# sourceMappingURL=sync-bulk.dto.js.map