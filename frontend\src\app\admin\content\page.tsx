import { Plus, Search, Filter, FileText, Video, Image, BookOpen } from 'lucide-react';

export default function ContentPage() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Content Management</h1>
        <div className="flex gap-4">
          <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center">
            <Plus className="h-4 w-4 mr-2" />
            New Course
          </button>
          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center">
            <Plus className="h-4 w-4 mr-2" />
            Upload Material
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search content..."
            className="pl-10 pr-4 py-2 w-full rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
          <Filter className="h-4 w-4 mr-2" />
          Filters
        </button>
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {contentItems.map((item) => (
          <div key={item.id} className="bg-white rounded-lg shadow overflow-hidden">
            <div className="aspect-video bg-gray-100 relative">
              {item.type === 'video' && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <Video className="h-12 w-12 text-gray-400" />
                </div>
              )}
              {item.type === 'document' && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <FileText className="h-12 w-12 text-gray-400" />
                </div>
              )}
              {item.type === 'image' && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <Image className="h-12 w-12 text-gray-400" />
                </div>
              )}
              {item.type === 'course' && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <BookOpen className="h-12 w-12 text-gray-400" />
                </div>
              )}
            </div>
            <div className="p-4">
              <div className="flex items-center justify-between mb-2">
                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                  item.type === 'course' ? 'bg-blue-100 text-blue-800' :
                  item.type === 'video' ? 'bg-purple-100 text-purple-800' :
                  item.type === 'document' ? 'bg-green-100 text-green-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                </span>
                <span className="text-sm text-gray-500">{item.date}</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">{item.title}</h3>
              <p className="text-sm text-gray-600 mb-4">{item.description}</p>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <img
                    src={item.author.avatar}
                    alt={item.author.name}
                    className="h-6 w-6 rounded-full"
                  />
                  <span className="text-sm text-gray-600">{item.author.name}</span>
                </div>
                <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                  Edit
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

const contentItems = [
  {
    id: 1,
    title: 'Cardiology Fundamentals',
    description: 'Comprehensive course covering basic cardiology principles and practices.',
    type: 'course',
    date: 'Updated 2 days ago',
    author: {
      name: 'Dr. Sarah Johnson',
      avatar: 'https://ui-avatars.com/api/?name=Sarah+Johnson'
    }
  },
  {
    id: 2,
    title: 'Heart Anatomy Video',
    description: 'Detailed 3D visualization of the human heart structure.',
    type: 'video',
    date: 'Added 1 week ago',
    author: {
      name: 'Dr. Michael Chen',
      avatar: 'https://ui-avatars.com/api/?name=Michael+Chen'
    }
  },
  {
    id: 3,
    title: 'Neurology Study Guide',
    description: 'Complete study materials for neurology residents.',
    type: 'document',
    date: 'Updated 3 days ago',
    author: {
      name: 'Dr. Emily Rodriguez',
      avatar: 'https://ui-avatars.com/api/?name=Emily+Rodriguez'
    }
  },
  {
    id: 4,
    title: 'Brain MRI Atlas',
    description: 'Collection of annotated brain MRI images for study.',
    type: 'image',
    date: 'Added 2 weeks ago',
    author: {
      name: 'Dr. James Wilson',
      avatar: 'https://ui-avatars.com/api/?name=James+Wilson'
    }
  }
]; 