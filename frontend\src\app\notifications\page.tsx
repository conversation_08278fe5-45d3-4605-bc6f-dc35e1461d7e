'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/Button';
import useRequireAuth from '@/hooks/useRequireAuth';
import { Bell, Check, Trash } from 'lucide-react';

interface Notification {
  id: string;
  title: string;
  message: string;
  date: string;
  read: boolean;
  type: 'info' | 'success' | 'warning' | 'error';
}

export default function NotificationsPage() {
  const { isLoading } = useRequireAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all');

  useEffect(() => {
    // In a real app, you would fetch notifications from the API
    // For now, we'll use placeholder data
    const placeholderNotifications: Notification[] = [
      {
        id: '1',
        title: 'New course available',
        message: 'A new course on Cardiology has been added to your curriculum.',
        date: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
        read: false,
        type: 'info',
      },
      {
        id: '2',
        title: 'Quiz completed',
        message: 'You scored 85% on the Anatomy quiz. Great job!',
        date: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
        read: true,
        type: 'success',
      },
      {
        id: '3',
        title: 'Upcoming event',
        message: 'Reminder: Pathology lecture tomorrow at 10:00 AM in Room 101.',
        date: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
        read: false,
        type: 'warning',
      },
      {
        id: '4',
        title: 'Assignment due soon',
        message: 'Your Physiology assignment is due in 2 days. Make sure to submit it on time.',
        date: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString(), // 2 days ago
        read: false,
        type: 'error',
      },
      {
        id: '5',
        title: 'New study material',
        message: 'New study materials for Neurology have been uploaded.',
        date: new Date(Date.now() - 1000 * 60 * 60 * 72).toISOString(), // 3 days ago
        read: true,
        type: 'info',
      },
      {
        id: '6',
        title: 'Progress update',
        message: 'You\'ve completed 65% of the Anatomy course. Keep up the good work!',
        date: new Date(Date.now() - 1000 * 60 * 60 * 96).toISOString(), // 4 days ago
        read: true,
        type: 'success',
      },
    ];
    
    setNotifications(placeholderNotifications);
  }, []);

  const handleMarkAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, read: true } 
          : notification
      )
    );
  };

  const handleMarkAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  const handleDeleteNotification = (id: string) => {
    setNotifications(prev => 
      prev.filter(notification => notification.id !== id)
    );
  };

  const handleClearAll = () => {
    setNotifications([]);
  };

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'unread') return !notification.read;
    if (filter === 'read') return notification.read;
    return true;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'info':
        return <div className="h-3 w-3 rounded-full bg-blue-500"></div>;
      case 'success':
        return <div className="h-3 w-3 rounded-full bg-green-500"></div>;
      case 'warning':
        return <div className="h-3 w-3 rounded-full bg-yellow-500"></div>;
      case 'error':
        return <div className="h-3 w-3 rounded-full bg-red-500"></div>;
      default:
        return <div className="h-3 w-3 rounded-full bg-gray-500"></div>;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[80vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Notifications</h1>
        <div className="flex space-x-2">
          <Button 
            variant="secondary" 
            size="sm"
            onClick={handleMarkAllAsRead}
            disabled={!notifications.some(n => !n.read)}
          >
            <Check className="h-4 w-4 mr-1" />
            Mark All as Read
          </Button>
          <Button 
            variant="danger" 
            size="sm"
            onClick={handleClearAll}
            disabled={notifications.length === 0}
          >
            <Trash className="h-4 w-4 mr-1" />
            Clear All
          </Button>
        </div>
      </div>

      <div className="flex space-x-2 mb-4">
        <button
          className={`px-4 py-2 rounded-md ${
            filter === 'all' 
              ? 'bg-blue-100 text-blue-700 border border-blue-300' 
              : 'bg-gray-100 text-gray-700 border border-gray-200'
          }`}
          onClick={() => setFilter('all')}
        >
          All
        </button>
        <button
          className={`px-4 py-2 rounded-md ${
            filter === 'unread' 
              ? 'bg-blue-100 text-blue-700 border border-blue-300' 
              : 'bg-gray-100 text-gray-700 border border-gray-200'
          }`}
          onClick={() => setFilter('unread')}
        >
          Unread
        </button>
        <button
          className={`px-4 py-2 rounded-md ${
            filter === 'read' 
              ? 'bg-blue-100 text-blue-700 border border-blue-300' 
              : 'bg-gray-100 text-gray-700 border border-gray-200'
          }`}
          onClick={() => setFilter('read')}
        >
          Read
        </button>
      </div>

      {filteredNotifications.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <Bell className="h-12 w-12 mx-auto text-gray-400 mb-2" />
            <h3 className="text-lg font-medium text-gray-900">No notifications</h3>
            <p className="text-gray-500">
              {filter === 'all' 
                ? 'You don\'t have any notifications yet.' 
                : filter === 'unread' 
                  ? 'You don\'t have any unread notifications.' 
                  : 'You don\'t have any read notifications.'}
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredNotifications.map(notification => (
            <Card 
              key={notification.id} 
              className={`hover:shadow-md transition-shadow ${
                !notification.read ? 'border-l-4 border-l-blue-500' : ''
              }`}
            >
              <CardContent className="p-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0 mt-1">
                    {getNotificationIcon(notification.type)}
                  </div>
                  <div className="ml-3 flex-1">
                    <div className="flex items-center justify-between">
                      <h3 className={`text-lg font-medium ${!notification.read ? 'text-gray-900' : 'text-gray-700'}`}>
                        {notification.title}
                      </h3>
                      <span className="text-sm text-gray-500">
                        {formatDate(notification.date)}
                      </span>
                    </div>
                    <p className={`mt-1 ${!notification.read ? 'text-gray-700' : 'text-gray-500'}`}>
                      {notification.message}
                    </p>
                    <div className="mt-3 flex justify-end space-x-2">
                      {!notification.read && (
                        <Button 
                          variant="secondary" 
                          size="sm"
                          onClick={() => handleMarkAsRead(notification.id)}
                        >
                          <Check className="h-4 w-4 mr-1" />
                          Mark as Read
                        </Button>
                      )}
                      <Button 
                        variant="danger" 
                        size="sm"
                        onClick={() => handleDeleteNotification(notification.id)}
                      >
                        <Trash className="h-4 w-4 mr-1" />
                        Delete
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
