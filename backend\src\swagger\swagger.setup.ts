
// src/swagger/swagger.setup.ts
import { INestApplication } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

export function setupSwagger(app: INestApplication) {
  const config = new DocumentBuilder()
    .setTitle('MedTrack Hub API')
    .setDescription('API documentation for the MedTrack Hub medical education platform')
    .setVersion('1.0')
    .addTag('auth', 'Authentication endpoints')
    .addTag('users', 'User management endpoints')
    .addTag('materials', 'Study materials endpoints')
    .addTag('progress', 'Progress tracking endpoints')
    .addTag('quizzes', 'Quiz and assessment endpoints')
    .addTag('units', 'Learning units endpoints')
    .addTag('notifications', 'User notification endpoints')
    .addTag('feedback', 'User feedback endpoints')
    .addTag('health', 'System health endpoints')
    .addBearerAuth(
      { type: 'http', scheme: 'bearer', bearerFormat: 'JWT' },
      'access-token'
    )
    .setContact('API Support', 'https://example.com/support', '<EMAIL>')
    .setExternalDoc('Additional Documentation', 'https://example.com/docs')
    .build();

  const document = SwaggerModule.createDocument(app, config);

  // Add custom CSS for better UI
  const customOptions = {
    swaggerOptions: {
      persistAuthorization: true,
    },
    customSiteTitle: 'MedTrack Hub API Docs',
    customCss: '.swagger-ui .topbar { display: none }',
  };

  SwaggerModule.setup('api/docs', app, document, customOptions);
}
