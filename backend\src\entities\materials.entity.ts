import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { User } from './user.entity';
import { Unit } from './unit.entity';
import { MaterialShare } from './material_shares.entity';
import { Progress } from './progress.entity';

export enum MaterialType {
    EXAM_PREP = 'exam_prep',
    CLINICAL_UPDATE = 'clinical_update',
    GUIDELINE = 'guideline',
    CASE_STUDY = 'case_study',
    QUIZ = 'quiz',
    ARTICLE = 'article',
    VIDEO = 'video',
    PRESENTATION = 'presentation',
    WORKSHOP = 'workshop',
    CONFERENCE = 'conference',
    RESEARCH = 'research'
}

export enum ContentSource {
    KENYA_GUIDELINES = 'kenya_guidelines',
    KEMRI = 'kemri',
    WHO = 'who',
    LOCAL = 'local',
    INTERNATIONAL = 'international'
}

@Entity('materials')
export class Material {
    @PrimaryGeneratedColumn('uuid')
    id: string;    

    @Column()
    title: string;

    @Column('text')
    description: string;

    @Column({ type: 'enum', enum: MaterialType, nullable: true })
    type: MaterialType;

    @Column('text')
    content: string; 

    @Column()
    file_url: string;

    @Column()
    answer: string;

    @Column({ type: 'boolean', default: false })
    is_exam_focused: boolean;

    @Column({ type: 'boolean', default: false })
    is_clinical_update: boolean;

    @Column({ type: 'enum', enum: ContentSource, default: ContentSource.LOCAL })
    source: ContentSource;

    @Column({ type: 'boolean', default: false })
    is_cpd_eligible: boolean;

    @Column({ type: 'jsonb', nullable: true })
    metadata: {
        cpd_points?: number;
        difficulty?: string;
        target_audience?: string[];
        tags?: string[];
        references?: string[];
    };

    @Column({ nullable: true })
    category: string;

    @Column({ type: 'float', nullable: true })
    difficulty: number;

    @ManyToOne(() => User, (user: User) => user.created_materials)
    author: User;

    @ManyToOne(() => Unit, (unit: Unit) => unit.materials)
    unit: Unit;

    @ManyToOne(() => User)
    user: User;

    @OneToMany(() => MaterialShare, (share: MaterialShare) => share.material)
    shares: MaterialShare[];

    @OneToMany(() => Progress, (progress: Progress) => progress.material)
    progress: Progress[];
    
    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}