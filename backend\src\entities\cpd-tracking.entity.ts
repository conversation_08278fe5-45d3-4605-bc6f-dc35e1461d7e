import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from './user.entity';
import { Material } from './materials.entity';

export enum CPDActivityType {
    MATERIAL_COMPLETION = 'material_completion',
    QUIZ_COMPLETION = 'quiz_completion',
    CASE_STUDY = 'case_study',
    GUIDELINE_REVIEW = 'guideline_review',
    CLINICAL_UPDATE = 'clinical_update'
}

@Entity('cpd_cycles')
export class CPDCycle {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    start_date: Date;

    @Column()
    end_date: Date;

    @Column({ type: 'int', default: 0 })
    total_points: number;

    @Column({ type: 'int', default: 0 })
    required_points: number;

    @Column({ type: 'boolean', default: false })
    is_completed: boolean;

    @ManyToOne(() => User, user => user.cpd_cycles)
    user: User;

    @OneToMany(() => CPDActivity, activity => activity.cycle)
    activities: CPDActivity[];

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}

@Entity('cpd_activities')
export class CPDActivity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    title: string;

    @Column('text')
    description: string;

    @Column({ type: 'int' })
    points: number;

    @Column()
    activity_date: Date;

    @Column({ type: 'boolean', default: false })
    is_verified: boolean;

    @Column({ nullable: true })
    verification_notes: string;

    @ManyToOne(() => User, user => user.cpd_activities)
    user: User;

    @ManyToOne(() => CPDCycle, cycle => cycle.activities)
    cycle: CPDCycle;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}