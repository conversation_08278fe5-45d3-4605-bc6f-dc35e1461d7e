import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';
import * as speakeasy from 'speakeasy';
import * as qrcode from 'qrcode';
import * as crypto from 'crypto';
import { UserSecuritySettings, UserSession, SecurityEvent } from '../../entities/security.entity';
import { User } from '../../entities/user.entity';
import { SecuritySettingsDto, ChangePasswordDto } from '../../dto/auth/security.dto';
import { TokenBlacklistService } from './token-blacklist.service';

interface SecurityEventData {
  ipAddress?: string;
  deviceId?: string;
  userAgent?: string;
  timestamp?: Date;
  [key: string]: any;
}

@Injectable()
export class SecurityService {
  private readonly logger = new Logger(SecurityService.name);

  constructor(
    @InjectRepository(UserSecuritySettings)
    private securitySettingsRepo: Repository<UserSecuritySettings>,
    @InjectRepository(UserSession)
    private sessionRepo: Repository<UserSession>,
    @InjectRepository(SecurityEvent)
    private securityEventRepo: Repository<SecurityEvent>,
    @InjectRepository(User)
    private userRepo: Repository<User>,
    private configService: ConfigService,
    private tokenBlacklistService: TokenBlacklistService,
  ) {}

  private generateBackupCodes(count = 10): string[] {
    const codes: string[] = [];
    for (let i = 0; i < count; i++) {
      codes.push(Math.random().toString(36).substr(2, 10).toUpperCase());
    }
    return codes;
  }

  async setupTwoFactor(userId: string): Promise<{ secret: string; qrCode: string }> {
    const user = await this.userRepo.findOne({ where: { id: userId } });
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    const secret = speakeasy.generateSecret({
      name: `MedicalApp:${user.email}`,
      issuer: 'MedicalApp',
    });

    let settings = await this.securitySettingsRepo.findOne({
      where: { userId },
    });
    const settingsData = {
      userId,
      twoFactorSecret: secret.base32,
      backupCodes: this.generateBackupCodes(),
    };

    if (!settings) {
      const newSettings = this.securitySettingsRepo.create(settingsData);
      settings = await this.securitySettingsRepo.save(newSettings);
    } else {
      Object.assign(settings, settingsData);
      settings = await this.securitySettingsRepo.save(settings);
    }

    const otpauthUrl = speakeasy.otpauthURL({
      secret: secret.ascii,
      label: `MedicalApp:${user.email}`,
      issuer: 'MedicalApp',
      encoding: 'base32',
    });
    const qrCodeData = await qrcode.toDataURL(otpauthUrl);

    return {
      secret: secret.base32,
      qrCode: qrCodeData,
    };
  }

  async verifyTwoFactor(userId: string, token: string): Promise<boolean> {
    const settings = await this.securitySettingsRepo.findOne({
      where: { userId },
    });
    if (!settings?.twoFactorSecret) {
      throw new BadRequestException('2FA not set up');
    }

    return speakeasy.totp.verify({
      secret: settings.twoFactorSecret,
      encoding: 'base32',
      token,
    });
  }

  async changePassword(userId: string, dto: ChangePasswordDto): Promise<void> {
    const user = await this.userRepo.findOne({ where: { id: userId } });
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    const isValid = await bcrypt.compare(dto.currentPassword, user.password_hash);
    if (!isValid) {
      throw new UnauthorizedException('Current password is incorrect');
    }

    if (dto.newPassword !== dto.confirmPassword) {
      throw new BadRequestException('New passwords do not match');
    }

    const passwordHash = await bcrypt.hash(dto.newPassword, 12);
    user.password_hash = passwordHash;
    await this.userRepo.save(user);

    // Log the event
    await this.logSecurityEvent(userId, 'PASSWORD_CHANGE', {
      timestamp: new Date(),
    });
  }

  async updateSecuritySettings(userId: string, settingsDto: SecuritySettingsDto): Promise<void> {
    let settings = await this.securitySettingsRepo.findOne({
      where: { userId },
    });

    if (!settings) {
      const newSettings = this.securitySettingsRepo.create({
        userId,
        ...settingsDto,
      });
      settings = await this.securitySettingsRepo.save(newSettings);
    } else {
      Object.assign(settings, settingsDto);
      settings = await this.securitySettingsRepo.save(settings);
    }

    await this.logSecurityEvent(userId, 'SECURITY_SETTINGS_UPDATE', {
      changes: settingsDto,
      timestamp: new Date(),
    });
  }

  async getActiveSessions(userId: string): Promise<UserSession[]> {
    return this.sessionRepo.find({
      where: {
        userId,
        isActive: true,
      },
      order: {
        lastAccessed: 'DESC',
      },
    });
  }

  async revokeSession(userId: string, sessionId: string): Promise<void> {
    const session = await this.sessionRepo.findOne({
      where: {
        id: sessionId,
        userId,
      },
    });

    if (!session) {
      throw new BadRequestException('Session not found');
    }

    session.isActive = false;
    await this.sessionRepo.save(session);

    // Add the session's token to the blacklist if it exists
    if (session.token) {
      await this.tokenBlacklistService.addToBlacklist(session.token, 7 * 24 * 60 * 60); // 7 days
    }

    // Log the event
    await this.logSecurityEvent(userId, 'SESSION_REVOKED', {
      sessionId,
      deviceId: session.deviceId,
      timestamp: new Date(),
    });
  }

  async logSecurityEvent(userId: string, eventType: string, eventData: SecurityEventData): Promise<void> {
    const event = this.securityEventRepo.create({
      userId,
      eventType,
      eventData,
      ipAddress: eventData.ipAddress || 'unknown',
      deviceId: eventData.deviceId,
      userAgent: eventData.userAgent,
    });

    await this.securityEventRepo.save(event);
  }

  async getSecurityEvents(userId: string, limit = 50): Promise<SecurityEvent[]> {
    return this.securityEventRepo.find({
      where: { userId },
      order: { timestamp: 'DESC' },
      take: limit,
    });
  }

  private async generateVerificationToken(): Promise<string> {
    return crypto.randomBytes(32).toString('hex');
  }

  async sendVerificationEmail(user: User): Promise<void> {
    const verificationToken = await this.generateVerificationToken();
    const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    let settings = await this.securitySettingsRepo.findOne({
      where: { userId: user.id },
    });
    const verificationData = {
      userId: user.id,
      emailVerificationToken: verificationToken,
      emailVerificationExpires: verificationExpires,
      isEmailVerified: false,
    };

    if (!settings) {
      const newSettings = this.securitySettingsRepo.create(verificationData);
      settings = await this.securitySettingsRepo.save(newSettings);
    } else {
      Object.assign(settings, verificationData);
      settings = await this.securitySettingsRepo.save(settings);
    }

    if (process.env.NODE_ENV === 'development') {
      console.log(
        `Verification link for ${user.email}: http://localhost:3000/auth/verify-email?token=${verificationToken}`,
      );
    }
  }

  async verifyEmail(token: string): Promise<boolean> {
    const settings = await this.securitySettingsRepo.findOne({
      where: { emailVerificationToken: token },
    });

    if (!settings) {
      throw new UnauthorizedException('Invalid verification token');
    }

    if (!settings.emailVerificationExpires || settings.emailVerificationExpires < new Date()) {
      throw new UnauthorizedException('Verification token has expired');
    }

    // Mark email as verified
    settings.isEmailVerified = true;
    settings.emailVerificationToken = null;
    settings.emailVerificationExpires = null;
    await this.securitySettingsRepo.save(settings);

    return true;
  }

  async resendVerificationEmail(userId: string): Promise<void> {
    const user = await this.userRepo.findOne({ where: { id: userId } });
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    const settings = await this.securitySettingsRepo.findOne({
      where: { userId },
    });
    if (settings?.isEmailVerified) {
      throw new BadRequestException('Email is already verified');
    }

    await this.sendVerificationEmail(user);
  }

  async isEmailVerified(userId: string): Promise<boolean> {
    const settings = await this.securitySettingsRepo.findOne({
      where: { userId },
    });
    return settings?.isEmailVerified ?? false;
  }

  async getSecuritySettings(userId: string): Promise<UserSecuritySettings> {
    const settings = await this.securitySettingsRepo.findOne({
      where: { userId },
    });
    if (!settings) {
      throw new NotFoundException('Security settings not found');
    }
    return settings;
  }

  async generateNewBackupCodes(userId: string): Promise<string[]> {
    const settings = await this.securitySettingsRepo.findOne({
      where: { userId },
    });
    if (!settings) {
      throw new NotFoundException('Security settings not found');
    }
    const newCodes = this.generateBackupCodes();
    settings.backupCodes = newCodes;
    await this.securitySettingsRepo.save(settings);
    return newCodes;
  }

  async initiateAccountRecovery(email: string): Promise<void> {
    // TODO: Implement account recovery initiation
    console.log(`Initiating account recovery for ${email}`);
  }

  async verifyRecoveryRequest(token: string, answers: { [key: string]: string }): Promise<boolean> {
    // TODO: Implement recovery request verification
    console.log(`Verifying recovery request with token ${token} and answers ${JSON.stringify(answers)}`);
    return true; // or false based on logic
  }
}