from sqlalchemy import create_engine, text
import os
from dotenv import load_dotenv

load_dotenv()

def test_connection():
    try:
        # Get database URL from environment
        db_url = os.getenv("DATABASE_URL")
        if not db_url:
            print("ERROR: DATABASE_URL not found in environment")
            return False

        # Create engine and test connection
        engine = create_engine(db_url)
        with engine.connect() as conn:
            # Test basic query
            result = conn.execute(text("SELECT COUNT(*) FROM users")).scalar()
            print(f"Connection successful! Found {result} users in database.")
            
            # Test more detailed query
            users = conn.execute(text("SELECT id, email, username, role FROM users")).fetchall()
            print("\nUsers in database:")
            for user in users:
                print(f"- {user.username} ({user.email}) - Role: {user.role}")
        
        return True
    except Exception as e:
        print(f"ERROR: Failed to connect to database: {str(e)}")
        return False

if __name__ == "__main__":
    test_connection()
