'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import FileUpload from '@/components/FileUpload';
import useRequireAuth from '@/hooks/useRequireAuth';

export default function UploadMaterialPage() {
  const { isLoading } = useRequireAuth();
  const router = useRouter();
  const [uploadedMaterial, setUploadedMaterial] = useState<any>(null);

  const handleUploadComplete = (material: any) => {
    setUploadedMaterial(material);
    
    // Redirect to the material page after a short delay
    setTimeout(() => {
      router.push('/materials');
    }, 2000);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[80vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Upload Study Material</h1>
      </div>

      <div className="max-w-3xl mx-auto">
        <FileUpload 
          onUploadComplete={handleUploadComplete}
          allowedFileTypes={['.pdf', '.doc', '.docx', '.txt', '.md', '.jpg', '.jpeg', '.png']}
          maxFileSizeMB={20}
        />
      </div>
    </div>
  );
}
