// src/components/layout/Layout.tsx
import React, { ReactNode, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useTheme } from '../../theme/ThemeProvider';
import Sidebar from './Sidebar';
import TopNav from './TopNav';

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const router = useRouter();
  const { theme } = useTheme();

  // Public routes don't need the authenticated layout
  const publicRoutes = ['/auth/login', '/auth/register', '/auth/forgot-password', '/auth/reset-password'];
  const isPublicRoute = publicRoutes.includes(router.pathname);

  if (isPublicRoute) {
    return (
      <div className={`flex min-h-screen bg-gray-50 dark:bg-gray-900 ${theme}`}>
        <div className="flex flex-col flex-1">
          <div className="flex flex-col flex-1 overflow-y-auto">
            <main className="flex-1 p-4 sm:p-6 md:p-8">
              {children}
            </main>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex min-h-screen bg-gray-50 dark:bg-gray-900 ${theme}`}>
      {/* Mobile sidebar */}
      <div
        className={`fixed inset-0 z-20 bg-gray-900 bg-opacity-50 transition-opacity duration-200 ${
          sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
        onClick={() => setSidebarOpen(false)}
      />

      {/* Sidebar */}
      <Sidebar
        open={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
      />

      {/* Content area */}
      <div className="flex flex-col flex-1">
        <TopNav onMenuClick={() => setSidebarOpen(true)} />

        <div className="flex flex-col flex-1 overflow-y-auto">
          <main className="flex-1 p-4 sm:p-6 md:p-8">
            {children}
          </main>

          <footer className="p-4 border-t dark:border-gray-700">
            <div className="text-sm text-center text-gray-500 dark:text-gray-400">
              © {new Date().getFullYear()} Medical Student Progress Tracker
            </div>
          </footer>
        </div>
      </div>
    </div>
  );
};

export default Layout;
