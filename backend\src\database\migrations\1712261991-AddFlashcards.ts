import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFlashcards1712261991 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE flashcards (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                question_id UUID REFERENCES quiz_questions(id) ON DELETE CASCADE,
                ef FLOAT NOT NULL,
                interval INTEGER NOT NULL,
                next_review TIMESTAMP WITH TIME ZONE NOT NULL,
                correct_streak INTEGER NOT NULL,
                last_review TIMESTAMP WITH TIME ZONE NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );

            CREATE INDEX idx_flashcards_user_id ON flashcards(user_id);
            CREATE INDEX idx_flashcards_question_id ON flashcards(question_id);
            CREATE INDEX idx_flashcards_next_review ON flashcards(next_review);
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP TABLE IF EXISTS flashcards;
        `);
    }
} 