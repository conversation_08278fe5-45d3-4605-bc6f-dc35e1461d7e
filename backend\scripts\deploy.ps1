# Stop execution on error
$ErrorActionPreference = "Stop"

Write-Host "🚀 Starting deployment process..." -ForegroundColor Green

# 1. Validate configuration
Write-Host "`nStep 1: Validating configuration..." -ForegroundColor Cyan
.\scripts\validate-config.ps1
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Configuration validation failed" -ForegroundColor Red
    exit 1
}

# 2. Run tests
Write-Host "`nStep 2: Running tests..." -ForegroundColor Cyan
.\test-docker-compose.ps1
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Tests failed" -ForegroundColor Red
    exit 1
}

# 3. Build and push Docker images
Write-Host "`nStep 3: Building and pushing Docker images..." -ForegroundColor Cyan
$version = Get-Date -Format "yyyyMMdd.HHmmss"
$registry = $env:OCI_REGISTRY_URL

docker build -t ${registry}/medical-backend:${version} .
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Docker build failed" -ForegroundColor Red
    exit 1
}

docker push ${registry}/medical-backend:${version}
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Docker push failed" -ForegroundColor Red
    exit 1
}

# 4. Deploy to OCI
Write-Host "`nStep 4: Deploying to OCI..." -ForegroundColor Cyan
.\scripts\deploy-oci.ps1 -Version ${version}
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Deployment failed" -ForegroundColor Red
    exit 1
}

# 5. Verify deployment
Write-Host "`nStep 5: Verifying deployment..." -ForegroundColor Cyan
.\scripts\validate-prod.ps1
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Deployment verification failed" -ForegroundColor Red
    Write-Host "🔄 Rolling back..." -ForegroundColor Yellow
    .\scripts\rollback.ps1
    exit 1
}

# 6. Start monitoring
Write-Host "`nStep 6: Starting monitoring..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-NoExit -File .\scripts\monitor-prod.ps1"

Write-Host "`n✅ Deployment completed successfully!" -ForegroundColor Green
Write-Host "📊 Monitoring dashboard started in new window" -ForegroundColor Green
