import { QuizQuestion } from '../types/quiz';
import { SpacedRepetitionAlgorithm } from './spacedRepetitionAlgorithm';
import { flashcardApi } from './flashcardApi';
import { useAuth } from '../hooks/useAuth';

export class QuizToFlashcardService {
    static async convertQuizToFlashcards(questions: QuizQuestion[], userId: string): Promise<void> {
        for (const question of questions) {
            const sm2Card = SpacedRepetitionAlgorithm.createNewCard();
            
            await flashcardApi.createFlashcard(userId, question.id);
        }
    }

    static async convertQuizResponseToFlashcard(
        questionId: string,
        userId: string,
        isCorrect: boolean
    ): Promise<void> {
        const quality = isCorrect ? 5 : 2; // High quality for correct, low for incorrect
        const sm2Card = SpacedRepetitionAlgorithm.createNewCard();
        sm2Card.quality = SpacedRepetitionAlgorithm.getQualityFromRating(quality);
        
        const updatedCard = SpacedRepetitionAlgorithm.calculateNextReview(sm2Card);
        await flashcardApi.updateCard(questionId, quality);
    }
}

// Hook for converting quiz questions to flashcards
export const useQuizToFlashcard = () => {
    const { user } = useAuth();

    const convertQuizToFlashcards = async (questions: QuizQuestion[]) => {
        if (!user) return;
        await QuizToFlashcardService.convertQuizToFlashcards(questions, user.id);
    };

    const convertQuizResponseToFlashcard = async (questionId: string, isCorrect: boolean) => {
        if (!user) return;
        await QuizToFlashcardService.convertQuizResponseToFlashcard(questionId, user.id, isCorrect);
    };

    return {
        convertQuizToFlashcards,
        convertQuizResponseToFlashcard,
    };
}; 