declare class SecurityQuestion {
    question: string;
    answer: string;
}
export declare class CreateUserSecuritySettingsDto {
    userId: string;
    twoFactorEnabled?: boolean;
    twoFactorSecret?: string;
    backupCodes?: string[];
    securityQuestions?: SecurityQuestion[];
    emailNotificationsEnabled?: boolean;
    isEmailVerified?: boolean;
    emailVerificationToken?: string | null;
    emailVerificationExpires?: Date | null;
}
export {};
