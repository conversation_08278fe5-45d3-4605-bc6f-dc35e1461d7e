import axios from 'axios';
import { format } from 'date-fns';

const BACKEND_URL = 'http://localhost:3002';
const FRONTEND_URL = 'http://localhost:3000';
const CURRENT_USER = 'Pkowech';
const CURRENT_DATE = format(new Date(), 'yyyy-MM-dd HH:mm:ss');

async function checkHealth() {
    console.log(`Health Check Started at ${CURRENT_DATE} by ${CURRENT_USER}\n`);
    console.log('='.repeat(50));

    try {
        // 1. Check Frontend
        console.log('\n🔍 Checking Frontend...');
        await axios.get(FRONTEND_URL);
        console.log('✅ Frontend is running at:', FRONTEND_URL);

        // 2. Check Backend
        console.log('\n🔍 Checking Backend...');
        await axios.get(`${BACKEND_URL}/health`);
        console.log('✅ Backend is running at:', BACKEND_URL);

        // 3. Test Authentication
        console.log('\n🔍 Testing Authentication...');
        const loginResponse = await axios.post(`${BACKEND_URL}/auth/login`, {
            username: CURRENT_USER,
            password: 'your-test-password'
        });
        const token = loginResponse.data.access_token;
        console.log('✅ Authentication successful');

        // 4. Test Protected Routes
        console.log('\n🔍 Testing Protected Routes...');
        await axios.get(`${BACKEND_URL}/materials`, {
            headers: { Authorization: `Bearer ${token}` }
        });
        console.log('✅ Protected routes accessible');

        // 5. Test Database Connection
        console.log('\n🔍 Testing Database Connection...');
        const dbHealth = await axios.get(`${BACKEND_URL}/health/db`);
        console.log('✅ Database connection successful');

        console.log('\n='.repeat(50));
        console.log('🎉 All systems operational!');

    } catch (error: any) {
        console.error('\n❌ Health check failed:');
        if (error.response) {
            console.error(`Status: ${error.response.status}`);
            console.error('Error details:', error.response.data);
        } else {
            console.error('Error:', error.message);
        }
        process.exit(1);
    }
}

// Add this to your package.json:
// "scripts": {
//   "health-check": "ts-node scripts/health-check.ts"
// }

checkHealth();