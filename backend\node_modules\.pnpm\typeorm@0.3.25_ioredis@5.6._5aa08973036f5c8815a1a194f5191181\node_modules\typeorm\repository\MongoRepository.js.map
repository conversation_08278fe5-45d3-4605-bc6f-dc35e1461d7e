{"version": 3, "sources": ["../../src/repository/MongoRepository.ts"], "names": [], "mappings": ";;;AACA,6CAAyC;AAKzC,wDAAoD;AA2CpD;;GAEG;AACH,MAAa,eAEX,SAAQ,uBAAkB;IAUxB,4EAA4E;IAC5E,qBAAqB;IACrB,4EAA4E;IAE5E;;;OAGG;IACH,KAAK,CAAC,KAAa,EAAE,UAAkB;QACnC,MAAM,IAAI,2BAAY,CAAC,sCAAsC,CAAC,CAAA;IAClE,CAAC;IAED;;;OAGG;IACH,kBAAkB,CACd,KAAa,EACb,WAAyB;QAEzB,MAAM,IAAI,2BAAY,CAAC,4CAA4C,CAAC,CAAA;IACxE,CAAC;IAED;;OAEG;IACH,IAAI,CACA,OAG6B;QAE7B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IAC3D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAU;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IAC3D,CAAC;IAED;;;;OAIG;IACH,YAAY,CACR,OAAsC;QAEtC,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IACnE,CAAC;IAED;;;;OAIG;IACH,cAAc,CAAC,KAAU;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IACnE,CAAC;IAED;;;;;;;;;OASG;IACH,SAAS,CAAC,GAAU,EAAE,OAAa;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAA;IACrE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CACT,OAAoC;QAEpC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IAC9D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,KAAU;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IAC9D,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,WAAW,CACb,EAAqC;QAErC,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;IAC7D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa,CAAC,OAA+B;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IACpE,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,eAAe,CAAC,KAAU;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IACpE,CAAC;IAED;;OAEG;IACH,YAAY,CAAU,KAAsB;QACxC,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IACjE,CAAC;IAED;;;OAGG;IACH,kBAAkB,CAAC,KAAsB;QACrC,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IACvE,CAAC;IAED;;OAEG;IACH,SAAS,CACL,QAAyB,EACzB,OAA0B;QAE1B,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CACzB,IAAI,CAAC,QAAQ,CAAC,MAAM,EACpB,QAAQ,EACR,OAAO,CACV,CAAA;IACL,CAAC;IAED;;;OAGG;IACH,eAAe,CACX,QAAyB,EACzB,OAA0B;QAE1B,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,CAC/B,IAAI,CAAC,QAAQ,CAAC,MAAM,EACpB,QAAQ,EACR,OAAO,CACV,CAAA;IACL,CAAC;IACD;;OAEG;IACH,SAAS,CACL,UAAmC,EACnC,OAA0B;QAE1B,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;IAC5E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAqB,EAAE,OAAsB;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE,EAAE,OAAO,CAAC,CAAA;IACzE,CAAC;IAED;;OAEG;IACH,cAAc,CACV,KAAqB,EACrB,OAA+B;QAE/B,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAC9B,IAAI,CAAC,QAAQ,CAAC,MAAM,EACpB,KAAK,IAAI,EAAE,EACX,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,KAAqB,EAAE,OAAsB;QACjD,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;IACrE,CAAC;IAED;;OAEG;IACH,qBAAqB,CACjB,WAAyB,EACzB,OAA8B;QAE9B,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,CACrC,IAAI,CAAC,QAAQ,CAAC,MAAM,EACpB,WAAW,EACX,OAAO,CACV,CAAA;IACL,CAAC;IAED;;;;OAIG;IACH,uBAAuB,CAAC,UAA8B;QAClD,OAAO,IAAI,CAAC,OAAO,CAAC,uBAAuB,CACvC,IAAI,CAAC,QAAQ,CAAC,MAAM,EACpB,UAAU,CACb,CAAA;IACL,CAAC;IAED;;OAEG;IACH,UAAU,CACN,KAAoB,EACpB,OAAuB;QAEvB,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;IAC3E,CAAC;IAED;;OAEG;IACH,SAAS,CACL,KAAoB,EACpB,OAAuB;QAEvB,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;IAC1E,CAAC;IAED;;OAEG;IACH,QAAQ,CACJ,GAAW,EACX,KAAoB,EACpB,OAAiC;QAEjC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CACxB,IAAI,CAAC,QAAQ,CAAC,SAAS,EACvB,GAAG,EACH,KAAK,EACL,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,mBAAmB,CACf,SAAiB,EACjB,OAAiC;QAEjC,OAAO,IAAI,CAAC,OAAO,CAAC,mBAAmB,CACnC,IAAI,CAAC,QAAQ,CAAC,SAAS,EACvB,SAAS,EACT,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,qBAAqB;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;IACtE,CAAC;IAED;;OAEG;IACH,gBAAgB,CACZ,KAAoB,EACpB,OAAiC;QAEjC,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAChC,IAAI,CAAC,QAAQ,CAAC,SAAS,EACvB,KAAK,EACL,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,iBAAiB,CACb,KAAoB,EACpB,WAAmB,EACnB,OAAkC;QAElC,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CACjC,IAAI,CAAC,QAAQ,CAAC,SAAS,EACvB,KAAK,EACL,WAAW,EACX,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB,CACZ,KAAoB,EACpB,MAAc,EACd,OAAiC;QAEjC,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAChC,IAAI,CAAC,QAAQ,CAAC,SAAS,EACvB,KAAK,EACL,MAAM,EACN,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,iBAAiB;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;IAClE,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,OAA0B;QAC5C,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,CACrC,IAAI,CAAC,QAAQ,CAAC,SAAS,EACvB,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,0BAA0B,CAAC,OAA2B;QAClD,OAAO,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAC1C,IAAI,CAAC,QAAQ,CAAC,SAAS,EACvB,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,uBAAuB,CAAC,OAA0B;QAC9C,OAAO,IAAI,CAAC,OAAO,CAAC,uBAAuB,CACvC,IAAI,CAAC,QAAQ,CAAC,SAAS,EACvB,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,yBAAyB,CACrB,OAA0B;QAE1B,OAAO,IAAI,CAAC,OAAO,CAAC,yBAAyB,CACzC,IAAI,CAAC,QAAQ,CAAC,SAAS,EACvB,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,UAAU,CACN,IAAqB,EACrB,OAA0B;QAE1B,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;IAC1E,CAAC;IAED;;OAEG;IACH,SAAS,CACL,GAAkB,EAClB,OAA0B;QAE1B,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE,OAAO,CAAC,CAAA;IACxE,CAAC;IAED;;OAEG;IACH,QAAQ;QACJ,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;IACzD,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,OAA4B;QAC9C,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,CACrC,IAAI,CAAC,QAAQ,CAAC,SAAS,EACvB,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CACF,OAAe,EACf,OAAkC;QAElC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;IACzE,CAAC;IAED;;OAEG;IACH,UAAU,CACN,KAAoB,EACpB,GAAkB,EAClB,OAAwB;QAExB,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAC1B,IAAI,CAAC,QAAQ,CAAC,SAAS,EACvB,KAAK,EACL,GAAG,EACH,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAA0B;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;IAC/D,CAAC;IAED;;OAEG;IACH,UAAU,CACN,KAAoB,EACpB,MAA8B,EAC9B,OAAuB;QAEvB,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAC1B,IAAI,CAAC,QAAQ,CAAC,SAAS,EACvB,KAAK,EACL,MAAM,EACN,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,SAAS,CACL,KAAoB,EACpB,MAA8B,EAC9B,OAAuB;QAEvB,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CACzB,IAAI,CAAC,QAAQ,CAAC,SAAS,EACvB,KAAK,EACL,MAAM,EACN,OAAO,CACV,CAAA;IACL,CAAC;CACJ;AA/eD,0CA+eC", "file": "MongoRepository.js", "sourcesContent": ["import { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { Repository } from \"./Repository\"\nimport { MongoFindManyOptions } from \"../find-options/mongodb/MongoFindManyOptions\"\nimport { MongoEntityManager } from \"../entity-manager/MongoEntityManager\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { SelectQueryBuilder } from \"../query-builder/SelectQueryBuilder\"\nimport { TypeORMError } from \"../error/TypeORMError\"\nimport { MongoFindOneOptions } from \"../find-options/mongodb/MongoFindOneOptions\"\nimport { FindOneOptions } from \"../find-options/FindOneOptions\"\n\nimport {\n    CreateIndexesOptions,\n    ObjectId,\n    ReplaceOptions,\n    //\n    AggregateOptions,\n    AggregationCursor,\n    AnyBulkWriteOperation,\n    BulkWriteOptions,\n    BulkWriteResult,\n    Collection,\n    CollStats,\n    CollStatsOptions,\n    CommandOperationOptions,\n    CountOptions,\n    DeleteOptions,\n    DeleteResult,\n    Document,\n    Filter,\n    FilterOperators,\n    FindCursor,\n    FindOneAndDeleteOptions,\n    FindOneAndReplaceOptions,\n    FindOneAndUpdateOptions,\n    IndexDescription,\n    InsertManyResult,\n    InsertOneOptions,\n    InsertOneResult,\n    ListIndexesCursor,\n    ListIndexesOptions,\n    OrderedBulkOperation,\n    UnorderedBulkOperation,\n    UpdateFilter,\n    UpdateOptions,\n    UpdateResult,\n    CountDocumentsOptions,\n} from \"../driver/mongodb/typings\"\nimport { FindManyOptions } from \"../find-options/FindManyOptions\"\n\n/**\n * Repository used to manage mongodb documents of a single entity type.\n */\nexport class MongoRepository<\n    Entity extends ObjectLiteral,\n> extends Repository<Entity> {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Entity Manager used by this repository.\n     */\n    readonly manager: MongoEntityManager\n\n    // -------------------------------------------------------------------------\n    // Overridden Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Raw SQL query execution is not supported by MongoDB.\n     * Calling this method will return an error.\n     */\n    query(query: string, parameters?: any[]): Promise<any> {\n        throw new TypeORMError(`Queries aren't supported by MongoDB.`)\n    }\n\n    /**\n     * Using Query Builder with MongoDB is not supported yet.\n     * Calling this method will return an error.\n     */\n    createQueryBuilder(\n        alias: string,\n        queryRunner?: QueryRunner,\n    ): SelectQueryBuilder<Entity> {\n        throw new TypeORMError(`Query Builder is not supported by MongoDB.`)\n    }\n\n    /**\n     * Finds entities that match given find options or conditions.\n     */\n    find(\n        options?:\n            | FindManyOptions<Entity>\n            | Partial<Entity>\n            | FilterOperators<Entity>,\n    ): Promise<Entity[]> {\n        return this.manager.find(this.metadata.target, options)\n    }\n\n    /**\n     * Finds entities that match given find options or conditions.\n     */\n    findBy(where: any): Promise<Entity[]> {\n        return this.manager.findBy(this.metadata.target, where)\n    }\n\n    /**\n     * Finds entities that match given find options or conditions.\n     * Also counts all entities that match given conditions,\n     * but ignores pagination settings (from and take options).\n     */\n    findAndCount(\n        options?: MongoFindManyOptions<Entity>,\n    ): Promise<[Entity[], number]> {\n        return this.manager.findAndCount(this.metadata.target, options)\n    }\n\n    /**\n     * Finds entities that match given find options or conditions.\n     * Also counts all entities that match given conditions,\n     * but ignores pagination settings (from and take options).\n     */\n    findAndCountBy(where: any): Promise<[Entity[], number]> {\n        return this.manager.findAndCountBy(this.metadata.target, where)\n    }\n\n    /**\n     * Finds entities by ids.\n     * Optionally find options can be applied.\n     *\n     * @deprecated use `findBy` method instead in conjunction with `In` operator, for example:\n     *\n     * .findBy({\n     *     id: In([1, 2, 3])\n     * })\n     */\n    findByIds(ids: any[], options?: any): Promise<Entity[]> {\n        return this.manager.findByIds(this.metadata.target, ids, options)\n    }\n\n    /**\n     * Finds first entity that matches given find options.\n     */\n    async findOne(\n        options: MongoFindOneOptions<Entity>,\n    ): Promise<Entity | null> {\n        return this.manager.findOne(this.metadata.target, options)\n    }\n\n    /**\n     * Finds first entity that matches given WHERE conditions.\n     */\n    async findOneBy(where: any): Promise<Entity | null> {\n        return this.manager.findOneBy(this.metadata.target, where)\n    }\n\n    /**\n     * Finds entity that matches given id.\n     *\n     * @deprecated use `findOneBy` method instead in conjunction with `In` operator, for example:\n     *\n     * .findOneBy({\n     *     id: 1 // where \"id\" is your primary column name\n     * })\n     */\n    async findOneById(\n        id: string | number | Date | ObjectId,\n    ): Promise<Entity | null> {\n        return this.manager.findOneById(this.metadata.target, id)\n    }\n\n    /**\n     * Finds first entity by a given find options.\n     * If entity was not found in the database - rejects with error.\n     */\n    async findOneOrFail(options: FindOneOptions<Entity>): Promise<Entity> {\n        return this.manager.findOneOrFail(this.metadata.target, options)\n    }\n\n    /**\n     * Finds first entity that matches given where condition.\n     * If entity was not found in the database - rejects with error.\n     */\n    async findOneByOrFail(where: any): Promise<Entity> {\n        return this.manager.findOneByOrFail(this.metadata.target, where)\n    }\n\n    /**\n     * Creates a cursor for a query that can be used to iterate over results from MongoDB.\n     */\n    createCursor<T = any>(query?: Filter<Entity>): FindCursor<T> {\n        return this.manager.createCursor(this.metadata.target, query)\n    }\n\n    /**\n     * Creates a cursor for a query that can be used to iterate over results from MongoDB.\n     * This returns modified version of cursor that transforms each result into Entity model.\n     */\n    createEntityCursor(query?: Filter<Entity>): FindCursor<Entity> {\n        return this.manager.createEntityCursor(this.metadata.target, query)\n    }\n\n    /**\n     * Execute an aggregation framework pipeline against the collection.\n     */\n    aggregate<R = any>(\n        pipeline: ObjectLiteral[],\n        options?: AggregateOptions,\n    ): AggregationCursor<R> {\n        return this.manager.aggregate<R>(\n            this.metadata.target,\n            pipeline,\n            options,\n        )\n    }\n\n    /**\n     * Execute an aggregation framework pipeline against the collection.\n     * This returns modified version of cursor that transforms each result into Entity model.\n     */\n    aggregateEntity(\n        pipeline: ObjectLiteral[],\n        options?: AggregateOptions,\n    ): AggregationCursor<Entity> {\n        return this.manager.aggregateEntity(\n            this.metadata.target,\n            pipeline,\n            options,\n        )\n    }\n    /**\n     * Perform a bulkWrite operation without a fluent API.\n     */\n    bulkWrite(\n        operations: AnyBulkWriteOperation[],\n        options?: BulkWriteOptions,\n    ): Promise<BulkWriteResult> {\n        return this.manager.bulkWrite(this.metadata.target, operations, options)\n    }\n\n    /**\n     * Count number of matching documents in the db to a query.\n     */\n    count(query?: ObjectLiteral, options?: CountOptions): Promise<number> {\n        return this.manager.count(this.metadata.target, query || {}, options)\n    }\n\n    /**\n     * Count number of matching documents in the db to a query.\n     */\n    countDocuments(\n        query?: ObjectLiteral,\n        options?: CountDocumentsOptions,\n    ): Promise<number> {\n        return this.manager.countDocuments(\n            this.metadata.target,\n            query || {},\n            options,\n        )\n    }\n\n    /**\n     * Count number of matching documents in the db to a query.\n     */\n    countBy(query?: ObjectLiteral, options?: CountOptions): Promise<number> {\n        return this.manager.countBy(this.metadata.target, query, options)\n    }\n\n    /**\n     * Creates an index on the db and collection.\n     */\n    createCollectionIndex(\n        fieldOrSpec: string | any,\n        options?: CreateIndexesOptions,\n    ): Promise<string> {\n        return this.manager.createCollectionIndex(\n            this.metadata.target,\n            fieldOrSpec,\n            options,\n        )\n    }\n\n    /**\n     * Creates multiple indexes in the collection, this method is only supported for MongoDB 2.6 or higher.\n     * Earlier version of MongoDB will throw a command not supported error.\n     * Index specifications are defined at http://docs.mongodb.org/manual/reference/command/createIndexes/.\n     */\n    createCollectionIndexes(indexSpecs: IndexDescription[]): Promise<string[]> {\n        return this.manager.createCollectionIndexes(\n            this.metadata.target,\n            indexSpecs,\n        )\n    }\n\n    /**\n     * Delete multiple documents on MongoDB.\n     */\n    deleteMany(\n        query: ObjectLiteral,\n        options?: DeleteOptions,\n    ): Promise<DeleteResult> {\n        return this.manager.deleteMany(this.metadata.tableName, query, options)\n    }\n\n    /**\n     * Delete a document on MongoDB.\n     */\n    deleteOne(\n        query: ObjectLiteral,\n        options?: DeleteOptions,\n    ): Promise<DeleteResult> {\n        return this.manager.deleteOne(this.metadata.tableName, query, options)\n    }\n\n    /**\n     * The distinct command returns returns a list of distinct values for the given key across a collection.\n     */\n    distinct(\n        key: string,\n        query: ObjectLiteral,\n        options?: CommandOperationOptions,\n    ): Promise<any> {\n        return this.manager.distinct(\n            this.metadata.tableName,\n            key,\n            query,\n            options,\n        )\n    }\n\n    /**\n     * Drops an index from this collection.\n     */\n    dropCollectionIndex(\n        indexName: string,\n        options?: CommandOperationOptions,\n    ): Promise<any> {\n        return this.manager.dropCollectionIndex(\n            this.metadata.tableName,\n            indexName,\n            options,\n        )\n    }\n\n    /**\n     * Drops all indexes from the collection.\n     */\n    dropCollectionIndexes(): Promise<any> {\n        return this.manager.dropCollectionIndexes(this.metadata.tableName)\n    }\n\n    /**\n     * Find a document and delete it in one atomic operation, requires a write lock for the duration of the operation.\n     */\n    findOneAndDelete(\n        query: ObjectLiteral,\n        options?: FindOneAndDeleteOptions,\n    ): Promise<Document | null> {\n        return this.manager.findOneAndDelete(\n            this.metadata.tableName,\n            query,\n            options,\n        )\n    }\n\n    /**\n     * Find a document and replace it in one atomic operation, requires a write lock for the duration of the operation.\n     */\n    findOneAndReplace(\n        query: ObjectLiteral,\n        replacement: Object,\n        options?: FindOneAndReplaceOptions,\n    ): Promise<Document | null> {\n        return this.manager.findOneAndReplace(\n            this.metadata.tableName,\n            query,\n            replacement,\n            options,\n        )\n    }\n\n    /**\n     * Find a document and update it in one atomic operation, requires a write lock for the duration of the operation.\n     */\n    findOneAndUpdate(\n        query: ObjectLiteral,\n        update: Object,\n        options?: FindOneAndUpdateOptions,\n    ): Promise<Document | null> {\n        return this.manager.findOneAndUpdate(\n            this.metadata.tableName,\n            query,\n            update,\n            options,\n        )\n    }\n\n    /**\n     * Retrieve all the indexes on the collection.\n     */\n    collectionIndexes(): Promise<any> {\n        return this.manager.collectionIndexes(this.metadata.tableName)\n    }\n\n    /**\n     * Retrieve all the indexes on the collection.\n     */\n    collectionIndexExists(indexes: string | string[]): Promise<boolean> {\n        return this.manager.collectionIndexExists(\n            this.metadata.tableName,\n            indexes,\n        )\n    }\n\n    /**\n     * Retrieves this collections index info.\n     */\n    collectionIndexInformation(options?: { full: boolean }): Promise<any> {\n        return this.manager.collectionIndexInformation(\n            this.metadata.tableName,\n            options,\n        )\n    }\n\n    /**\n     * Initiate an In order bulk write operation, operations will be serially executed in the order they are added, creating a new operation for each switch in types.\n     */\n    initializeOrderedBulkOp(options?: BulkWriteOptions): OrderedBulkOperation {\n        return this.manager.initializeOrderedBulkOp(\n            this.metadata.tableName,\n            options,\n        )\n    }\n\n    /**\n     * Initiate a Out of order batch write operation. All operations will be buffered into insert/update/remove commands executed out of order.\n     */\n    initializeUnorderedBulkOp(\n        options?: BulkWriteOptions,\n    ): UnorderedBulkOperation {\n        return this.manager.initializeUnorderedBulkOp(\n            this.metadata.tableName,\n            options,\n        )\n    }\n\n    /**\n     * Inserts an array of documents into MongoDB.\n     */\n    insertMany(\n        docs: ObjectLiteral[],\n        options?: BulkWriteOptions,\n    ): Promise<InsertManyResult<Document>> {\n        return this.manager.insertMany(this.metadata.tableName, docs, options)\n    }\n\n    /**\n     * Inserts a single document into MongoDB.\n     */\n    insertOne(\n        doc: ObjectLiteral,\n        options?: InsertOneOptions,\n    ): Promise<InsertOneResult> {\n        return this.manager.insertOne(this.metadata.tableName, doc, options)\n    }\n\n    /**\n     * Returns if the collection is a capped collection.\n     */\n    isCapped(): Promise<any> {\n        return this.manager.isCapped(this.metadata.tableName)\n    }\n\n    /**\n     * Get the list of all indexes information for the collection.\n     */\n    listCollectionIndexes(options?: ListIndexesOptions): ListIndexesCursor {\n        return this.manager.listCollectionIndexes(\n            this.metadata.tableName,\n            options,\n        )\n    }\n\n    /**\n     * Reindex all indexes on the collection Warning: reIndex is a blocking operation (indexes are rebuilt in the foreground) and will be slow for large collections.\n     */\n    rename(\n        newName: string,\n        options?: { dropTarget?: boolean },\n    ): Promise<Collection<Document>> {\n        return this.manager.rename(this.metadata.tableName, newName, options)\n    }\n\n    /**\n     * Replace a document on MongoDB.\n     */\n    replaceOne(\n        query: ObjectLiteral,\n        doc: ObjectLiteral,\n        options?: ReplaceOptions,\n    ): Promise<Document | UpdateResult> {\n        return this.manager.replaceOne(\n            this.metadata.tableName,\n            query,\n            doc,\n            options,\n        )\n    }\n\n    /**\n     * Get all the collection statistics.\n     */\n    stats(options?: CollStatsOptions): Promise<CollStats> {\n        return this.manager.stats(this.metadata.tableName, options)\n    }\n\n    /**\n     * Update multiple documents on MongoDB.\n     */\n    updateMany(\n        query: ObjectLiteral,\n        update: UpdateFilter<Document>,\n        options?: UpdateOptions,\n    ): Promise<Document | UpdateResult> {\n        return this.manager.updateMany(\n            this.metadata.tableName,\n            query,\n            update,\n            options,\n        )\n    }\n\n    /**\n     * Update a single document on MongoDB.\n     */\n    updateOne(\n        query: ObjectLiteral,\n        update: UpdateFilter<Document>,\n        options?: UpdateOptions,\n    ): Promise<Document | UpdateResult> {\n        return this.manager.updateOne(\n            this.metadata.tableName,\n            query,\n            update,\n            options,\n        )\n    }\n}\n"], "sourceRoot": ".."}