import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnalyticsController } from './analytics.controller';
import { AnalyticsService } from './analytics.service';
import { StudyEvent } from '../entities/study-event.entity';
import { QuizAttempt } from '../entities/quiz-attempt.entity';
import { Flashcard } from '../entities/flashcard.entity';
import { Progress } from '../entities/progress.entity';
import { UserResponse } from '../entities/user-response.entity';
import { RedisModule } from '../redis/redis.module';

@Module({
    imports: [
        TypeOrmModule.forFeature([
            StudyEvent,
            QuizAttempt,
            Flashcard,
            Progress,
            UserResponse
        ]),
        RedisModule,
    ],
    controllers: [AnalyticsController],
    providers: [AnalyticsService],
    exports: [AnalyticsService],
})
export class AnalyticsModule {}