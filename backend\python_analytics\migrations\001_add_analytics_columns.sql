-- Add new columns for learning style tracking
ALTER TABLE user_progress 
ADD COLUMN IF NOT EXISTS learning_style VARCHAR(50),
ADD COLUMN IF NOT EXISTS topic_preferences JSONB;

-- Add new columns for performance tracking
ALTER TABLE quiz_results 
ADD COLUMN IF NOT EXISTS confidence_interval JSONB,
ADD COLUMN IF NOT EXISTS anomaly_score FLOAT;

-- Add new columns for study patterns
ALTER TABLE study_sessions
ADD COLUMN IF NOT EXISTS learning_style_score JSONB,
ADD COLUMN IF NOT EXISTS effectiveness_score FLOAT;

-- Create index for faster analytics queries
CREATE INDEX IF NOT EXISTS idx_user_progress_learning_style 
ON user_progress(learning_style);

CREATE INDEX IF NOT EXISTS idx_quiz_results_anomaly_score 
ON quiz_results(anomaly_score);

CREATE INDEX IF NOT EXISTS idx_study_sessions_effectiveness 
ON study_sessions(effectiveness_score); 