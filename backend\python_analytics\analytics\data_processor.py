from typing import Dict, Any, List
import pandas as pd
import numpy as np
from sqlalchemy import create_engine, text
import os
from dotenv import load_dotenv
import logging
from functools import lru_cache
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

load_dotenv()

class DataProcessor:
    def __init__(self):
        self.db_url = os.getenv("DATABASE_URL")
        if not self.db_url:
            raise ValueError("DATABASE_URL environment variable is not set")
        
        try:
            self.engine = create_engine(self.db_url)
            # Test connection
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("Successfully connected to database")
        except Exception as e:
            logger.error(f"Failed to connect to database: {str(e)}")
            raise

    @lru_cache(maxsize=100)
    async def process_user_data(self, user_id: str) -> Dict[str, Any]:
        """Process all user data for analytics with caching"""
        try:
            logger.info(f"Processing data for user {user_id}")
            
            # Fetch user progress data
            progress_data = await self._fetch_progress_data(user_id)
            logger.debug(f"Progress data fetched: {len(progress_data)} records")
            
            # Fetch quiz responses
            quiz_data = await self._fetch_quiz_data(user_id)
            logger.debug(f"Quiz data fetched: {quiz_data}")
            
            # Fetch study time data
            study_time_data = await self._fetch_study_time_data(user_id)
            logger.debug(f"Study time data fetched: {len(study_time_data)} records")
            
            # Validate data
            self._validate_data(progress_data, quiz_data, study_time_data)
            
            # Combine all data
            result = {
                "progress": progress_data,
                "quiz_performance": quiz_data,
                "study_time": study_time_data,
                "last_updated": datetime.utcnow().isoformat()
            }
            
            logger.info(f"Successfully processed data for user {user_id}")
            return result
            
        except Exception as e:
            logger.error(f"Error processing user data for {user_id}: {str(e)}")
            raise

    def _validate_data(
        self,
        progress_data: List[Dict[str, Any]],
        quiz_data: Dict[str, Any],
        study_time_data: List[Dict[str, Any]]
    ) -> None:
        """Validate the fetched data"""
        # Validate progress data
        if not isinstance(progress_data, list):
            raise ValueError("Progress data must be a list")
        
        # Validate quiz data
        required_quiz_fields = ["total_questions", "correct_answers", "accuracy"]
        for field in required_quiz_fields:
            if field not in quiz_data:
                raise ValueError(f"Missing required field in quiz data: {field}")
        
        # Validate study time data
        if not isinstance(study_time_data, list):
            raise ValueError("Study time data must be a list")
        
        for record in study_time_data:
            if not all(k in record for k in ["study_date", "total_duration"]):
                raise ValueError("Invalid study time record format")

    async def _fetch_progress_data(self, user_id: str) -> List[Dict[str, Any]]:
        """Fetch user progress data from database"""
        try:
            query = text("""
                SELECT 
                    status,
                    COUNT(*) as count,
                    MAX(updated_at) as last_updated
                FROM progress
                WHERE user_id = :user_id
                GROUP BY status
            """)
            
            with self.engine.connect() as conn:
                result = conn.execute(query, {"user_id": user_id})
                return [dict(row) for row in result]
        except Exception as e:
            logger.error(f"Error fetching progress data for user {user_id}: {str(e)}")
            raise

    async def _fetch_quiz_data(self, user_id: str) -> Dict[str, Any]:
        """Fetch user quiz performance data"""
        try:
            query = text("""
                SELECT 
                    COUNT(*) as total_questions,
                    SUM(CASE WHEN is_correct THEN 1 ELSE 0 END) as correct_answers,
                    AVG(CASE WHEN is_correct THEN 1 ELSE 0 END) as accuracy,
                    MAX(created_at) as last_attempt
                FROM user_response
                WHERE user_id = :user_id
            """)
            
            with self.engine.connect() as conn:
                result = conn.execute(query, {"user_id": user_id})
                return dict(result.fetchone())
        except Exception as e:
            logger.error(f"Error fetching quiz data for user {user_id}: {str(e)}")
            raise

    async def _fetch_study_time_data(self, user_id: str) -> List[Dict[str, Any]]:
        """Fetch user study time data"""
        try:
            query = text("""
                SELECT 
                    DATE(created_at) as study_date,
                    SUM(duration) as total_duration,
                    COUNT(DISTINCT material_id) as materials_covered
                FROM study_session
                WHERE user_id = :user_id
                GROUP BY DATE(created_at)
                ORDER BY study_date DESC
                LIMIT 30
            """)
            
            with self.engine.connect() as conn:
                result = conn.execute(query, {"user_id": user_id})
                return [dict(row) for row in result]
        except Exception as e:
            logger.error(f"Error fetching study time data for user {user_id}: {str(e)}")
            raise

# Create a singleton instance
data_processor = DataProcessor()

async def process_user_data(user_id: str) -> Dict[str, Any]:
    """Global function to process user data"""
    return await data_processor.process_user_data(user_id) 