import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from './user.entity';
import { Material } from './materials.entity';

@Entity('learning_suggestions')
export class LearningSuggestion {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @ManyToOne(() => User)
    user: User;

    @Column({ type: 'date' })
    suggestion_date: Date;

    @Column({ type: 'boolean', default: false })
    is_viewed: boolean;

    @Column({ type: 'timestamp', nullable: true })
    viewed_at: Date;

    @Column({ type: 'enum', enum: ['pending', 'completed', 'dismissed'], default: 'pending' })
    status: 'pending' | 'completed' | 'dismissed';

    @Column({ type: 'timestamp', nullable: true })
    completed_at: Date;

    @Column({ type: 'int', default: 0 })
    priority: number;

    @Column({ type: 'jsonb' })
    suggestions: {
        topic: string;
        priority: number;
        reason: string;
        materials: Material[];
        estimated_time: number;
        prerequisites?: string[];
        related_topics?: string[];
    }[];

    @Column({ type: 'jsonb' })
    user_context: {
        recent_activity: {
            type: string;
            material_id: string;
            timestamp: Date;
        }[];
        mastery_level: {
            topic: string;
            level: number;
            last_assessed: Date;
        }[];
        learning_gaps: {
            topic: string;
            confidence: number;
            last_attempted: Date;
        }[];
    };

    @Column({ type: 'jsonb', nullable: true })
    feedback: {
        helpful: boolean;
        comments?: string;
        implemented?: boolean;
        timestamp?: Date;
    };

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
} 