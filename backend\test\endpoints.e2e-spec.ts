import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AppModule } from '../src/app.module';

describe('Backend Endpoints (e2e)', () => {
  let app: INestApplication;
  let authToken: string;
  let testUserId: string;
  let testUnitId: string;
  let testMaterialId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Create test user and get auth token
    const registerResponse = await request(app.getHttpServer())
      .post('/auth/register')
      .send({
        email: '<EMAIL>',
        username: 'testuser',
        password: 'Test123!',
        name: 'Test User',
      });

    const loginResponse = await request(app.getHttpServer())
      .post('/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'Test123!',
      });

    authToken = loginResponse.body.accessToken;
    testUserId = registerResponse.body.id;
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Auth Module', () => {
    it('should register a new user', () => {
      return request(app.getHttpServer())
        .post('/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'newuser',
          password: 'Test123!',
          name: 'New User',
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body).toHaveProperty('email');
        });
    });

    it('should login user', () => {
      return request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Test123!',
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('accessToken');
        });
    });

    it('should get user profile', () => {
      return request(app.getHttpServer())
        .get('/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body).toHaveProperty('email');
        });
    });

    it('should get security settings', () => {
      return request(app.getHttpServer())
        .get('/auth/security/settings')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
    });

    it('should get active sessions', () => {
      return request(app.getHttpServer())
        .get('/auth/security/sessions')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
    });
  });

  describe('Materials Module', () => {
    it('should create a new material', () => {
      return request(app.getHttpServer())
        .post('/materials')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: 'Test Material',
          description: 'Test Description',
          type: 'document',
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          testMaterialId = res.body.id;
        });
    });

    it('should get all materials', () => {
      return request(app.getHttpServer())
        .get('/materials')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBeTruthy();
        });
    });

    it('should get material by id', () => {
      return request(app.getHttpServer())
        .get(`/materials/${testMaterialId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('id', testMaterialId);
        });
    });
  });

  describe('Units Module', () => {
    it('should create a new unit', () => {
      return request(app.getHttpServer())
        .post('/units')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: 'Test Unit',
          description: 'Test Unit Description',
          materialId: testMaterialId,
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          testUnitId = res.body.id;
        });
    });

    it('should get all units', () => {
      return request(app.getHttpServer())
        .get('/units')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBeTruthy();
        });
    });

    it('should get unit by id', () => {
      return request(app.getHttpServer())
        .get(`/units/${testUnitId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('id', testUnitId);
        });
    });
  });

  describe('Progress Module', () => {
    it('should update unit progress', () => {
      return request(app.getHttpServer())
        .post(`/progress/${testUnitId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          completed: true,
          score: 85,
        })
        .expect(201);
    });

    it('should get user progress', () => {
      return request(app.getHttpServer())
        .get(`/progress/user/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBeTruthy();
        });
    });
  });

  describe('Quiz Module', () => {
    it('should get quiz for unit', () => {
      return request(app.getHttpServer())
        .get(`/quiz/unit/${testUnitId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('questions');
        });
    });

    it('should submit quiz answers', () => {
      return request(app.getHttpServer())
        .post('/quiz/submit')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          unitId: testUnitId,
          answers: [
            { questionId: 1, answer: 'A' },
            { questionId: 2, answer: 'B' },
          ],
        })
        .expect(201);
    });

    it('should get quiz results', () => {
      return request(app.getHttpServer())
        .get(`/quiz/results/${testUserId}/${testUnitId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('score');
        });
    });
  });

  describe('Notifications Module', () => {
    it('should create a notification', () => {
      return request(app.getHttpServer())
        .post('/notifications')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: 'Test Notification',
          message: 'This is a test notification',
          type: 'info',
        })
        .expect(201);
    });

    it('should get all notifications', () => {
      return request(app.getHttpServer())
        .get('/notifications')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBeTruthy();
        });
    });
  });

  describe('Analytics Module', () => {
    it('should get learning patterns', () => {
      return request(app.getHttpServer())
        .get(`/analytics/learning-patterns/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
    });

    it('should get recommendations', () => {
      return request(app.getHttpServer())
        .get(`/analytics/recommendations/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
    });

    it('should get performance metrics', () => {
      return request(app.getHttpServer())
        .get(`/analytics/performance/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
    });

    it('should get peer benchmarks', () => {
      return request(app.getHttpServer())
        .get(`/analytics/benchmarks/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
    });
  });
}); 