"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenBlacklistModule = void 0;
const common_1 = require("@nestjs/common");
const token_blacklist_service_1 = require("./token-blacklist.service");
const cache_module_1 = require("../../cache/cache.module");
const jwt_1 = require("@nestjs/jwt");
let TokenBlacklistModule = class TokenBlacklistModule {
};
exports.TokenBlacklistModule = TokenBlacklistModule;
exports.TokenBlacklistModule = TokenBlacklistModule = __decorate([
    (0, common_1.Module)({
        imports: [
            cache_module_1.CacheModule,
            jwt_1.JwtModule.register({
                secret: process.env.JWT_SECRET || 'your-secret-key',
            }),
        ],
        providers: [token_blacklist_service_1.TokenBlacklistService],
        exports: [token_blacklist_service_1.TokenBlacklistService],
    })
], TokenBlacklistModule);
//# sourceMappingURL=token-blacklist.module.js.map