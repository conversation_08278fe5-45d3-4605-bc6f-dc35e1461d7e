import { SecurityService } from './security.service';
import { SecuritySettingsDto, ChangePasswordDto, VerifyTwoFactorDto, EmailVerificationDto, AccountRecoveryDto, VerifyRecoveryDto } from '../../dto/auth/security.dto';
import { AuthenticatedRequest } from '../../interfaces/authenticated-request.interface';
export declare class SecurityController {
    private readonly securityService;
    constructor(securityService: SecurityService);
    setupTwoFactor(req: AuthenticatedRequest): Promise<{
        secret: string;
        qrCode: string;
    }>;
    verifyTwoFactor(req: AuthenticatedRequest, dto: VerifyTwoFactorDto): Promise<{
        valid: boolean;
    }>;
    changePassword(req: AuthenticatedRequest, dto: ChangePasswordDto): Promise<void>;
    updateSecuritySettings(req: AuthenticatedRequest, dto: SecuritySettingsDto): Promise<{
        message: string;
    }>;
    getSecuritySettings(req: AuthenticatedRequest): Promise<import("../../entities/security.entity").UserSecuritySettings>;
    getActiveSessions(req: AuthenticatedRequest): Promise<import("../../entities/security.entity").UserSession[]>;
    revokeSession(req: AuthenticatedRequest, sessionId: string): Promise<void>;
    getSecurityEvents(req: AuthenticatedRequest, limit?: number): Promise<import("../../entities/security.entity").SecurityEvent[]>;
    generateBackupCodes(req: AuthenticatedRequest): Promise<{
        backupCodes: string[];
    }>;
    verifyEmail(dto: EmailVerificationDto): Promise<{
        verified: boolean;
    }>;
    resendVerificationEmail(req: AuthenticatedRequest): Promise<{
        message: string;
    }>;
    initiateAccountRecovery(dto: AccountRecoveryDto): Promise<{
        message: string;
    }>;
    verifyRecoveryRequest(dto: VerifyRecoveryDto): Promise<{
        verified: boolean;
    }>;
}
