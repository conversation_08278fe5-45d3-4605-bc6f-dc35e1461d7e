import React from 'react';
import {
    <PERSON>,
    Card,
    CardContent,
    <PERSON>po<PERSON>,
    Grid,
    Chip,
    Button,
    List,
    ListItem,
    ListItemText,
    ListItemIcon,
    Divider,
} from '@mui/material';
import {
    School as SchoolIcon,
    AccessTime as AccessTimeIcon,
    Bookmark as BookmarkIcon,
    BookmarkBorder as BookmarkBorderIcon,
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';

interface LearningSuggestion {
    id: string;
    title: string;
    description: string;
    type: string;
    estimatedTime: number;
    unit: {
        id: string;
        name: string;
        code: string;
    };
    isBookmarked: boolean;
}

interface LearningSuggestionsProps {
    suggestions: LearningSuggestion[];
    onBookmark: (id: string) => void;
    onStart: (id: string) => void;
}

export const LearningSuggestions: React.FC<LearningSuggestionsProps> = ({
    suggestions,
    onBookmark,
    onStart,
}) => {
    const theme = useTheme();

    return (
        <Box sx={{ p: 3 }}>
            <Grid container spacing={3}>
                {suggestions.map((suggestion) => (
                    <Grid item xs={12} md={6} key={suggestion.id}>
                        <Card>
                            <CardContent>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                                    <Typography variant="h6" component="div">
                                        {suggestion.title}
                                    </Typography>
                                    <Button
                                        onClick={() => onBookmark(suggestion.id)}
                                        startIcon={suggestion.isBookmarked ? <BookmarkIcon /> : <BookmarkBorderIcon />}
                                        color={suggestion.isBookmarked ? "primary" : "default"}
                                    >
                                        {suggestion.isBookmarked ? "Bookmarked" : "Bookmark"}
                                    </Button>
                                </Box>
                                
                                <Typography variant="body2" color="text.secondary" paragraph>
                                    {suggestion.description}
                                </Typography>

                                <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                                    <Chip
                                        icon={<SchoolIcon />}
                                        label={suggestion.type}
                                        size="small"
                                    />
                                    <Chip
                                        icon={<AccessTimeIcon />}
                                        label={`${suggestion.estimatedTime} mins`}
                                        size="small"
                                    />
                                    <Chip
                                        label={suggestion.unit.name}
                                        size="small"
                                        variant="outlined"
                                    />
                                    <Chip
                                        label={suggestion.unit.code}
                                        size="small"
                                        variant="outlined"
                                    />
                                </Box>

                                <Button
                                    variant="contained"
                                    color="primary"
                                    fullWidth
                                    onClick={() => onStart(suggestion.id)}
                                >
                                    Start Learning
                                </Button>
                            </CardContent>
                        </Card>
                    </Grid>
                ))}
            </Grid>
        </Box>
    );
}; 