from typing import Dict, Any, List
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler

def analyze_learning_patterns(user_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze user's learning patterns from their data
    """
    try:
        # Convert study time data to DataFrame
        study_df = pd.DataFrame(user_data["study_time"])
        study_df["study_date"] = pd.to_datetime(study_df["study_date"])
        
        # Analyze study patterns
        study_patterns = _analyze_study_patterns(study_df)
        
        # Analyze quiz performance patterns
        quiz_patterns = _analyze_quiz_patterns(user_data["quiz_performance"])
        
        # Analyze progress patterns
        progress_patterns = _analyze_progress_patterns(user_data["progress"])
        
        # Analyze learning style
        learning_style = _analyze_learning_style(study_df, quiz_patterns)
        
        # Analyze topic preferences
        topic_preferences = _analyze_topic_preferences(user_data)
        
        return {
            "study_patterns": study_patterns,
            "quiz_patterns": quiz_patterns,
            "progress_patterns": progress_patterns,
            "learning_style": learning_style,
            "topic_preferences": topic_preferences,
            "overall_patterns": _combine_patterns(
                study_patterns,
                quiz_patterns,
                progress_patterns,
                learning_style,
                topic_preferences
            )
        }
    except Exception as e:
        raise Exception(f"Error analyzing learning patterns: {str(e)}")

def _analyze_study_patterns(study_df: pd.DataFrame) -> Dict[str, Any]:
    """Analyze study time patterns"""
    if study_df.empty:
        return {
            "average_daily_study_time": 0,
            "study_consistency": 0,
            "preferred_study_days": [],
            "study_trend": "insufficient_data"
        }
    
    # Calculate average daily study time
    avg_study_time = study_df["total_duration"].mean()
    
    # Calculate study consistency (percentage of days with study activity)
    total_days = (study_df["study_date"].max() - study_df["study_date"].min()).days + 1
    study_days = len(study_df)
    consistency = (study_days / total_days) * 100 if total_days > 0 else 0
    
    # Determine preferred study days
    study_df["day_of_week"] = study_df["study_date"].dt.day_name()
    preferred_days = study_df.groupby("day_of_week")["total_duration"].mean().nlargest(3).index.tolist()
    
    # Analyze study trend
    if len(study_df) >= 7:
        recent_avg = study_df.tail(7)["total_duration"].mean()
        older_avg = study_df.iloc[:-7]["total_duration"].mean()
        trend = "increasing" if recent_avg > older_avg else "decreasing" if recent_avg < older_avg else "stable"
    else:
        trend = "insufficient_data"
    
    return {
        "average_daily_study_time": round(avg_study_time, 2),
        "study_consistency": round(consistency, 2),
        "preferred_study_days": preferred_days,
        "study_trend": trend
    }

def _analyze_quiz_patterns(quiz_data: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze quiz performance patterns"""
    if not quiz_data or quiz_data["total_questions"] == 0:
        return {
            "accuracy": 0,
            "performance_trend": "insufficient_data",
            "strengths": [],
            "weaknesses": []
        }
    
    accuracy = quiz_data["accuracy"] * 100 if quiz_data["accuracy"] is not None else 0
    
    # Determine performance trend (placeholder - would need historical data)
    performance_trend = "stable"  # This would be calculated with historical data
    
    return {
        "accuracy": round(accuracy, 2),
        "performance_trend": performance_trend,
        "strengths": [],  # Would be calculated with topic-specific data
        "weaknesses": []  # Would be calculated with topic-specific data
    }

def _analyze_progress_patterns(progress_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze progress patterns"""
    if not progress_data:
        return {
            "completion_rate": 0,
            "progress_trend": "insufficient_data",
            "current_status": "not_started"
        }
    
    # Calculate completion rate
    total_items = sum(item["count"] for item in progress_data)
    completed_items = sum(item["count"] for item in progress_data if item["status"] == "completed")
    completion_rate = (completed_items / total_items * 100) if total_items > 0 else 0
    
    # Determine current status
    status_counts = {item["status"]: item["count"] for item in progress_data}
    current_status = max(status_counts.items(), key=lambda x: x[1])[0]
    
    return {
        "completion_rate": round(completion_rate, 2),
        "progress_trend": "stable",  # Would be calculated with historical data
        "current_status": current_status
    }

def _analyze_learning_style(
    study_df: pd.DataFrame,
    quiz_patterns: Dict[str, Any]
) -> Dict[str, Any]:
    """Analyze user's learning style using multiple factors"""
    if study_df.empty:
        return {
            "primary_style": "unknown",
            "secondary_style": "unknown",
            "confidence": "low",
            "characteristics": []
        }
    
    # Calculate learning style indicators
    indicators = {
        "visual": _calculate_visual_learning_score(study_df),
        "auditory": _calculate_auditory_learning_score(study_df),
        "reading": _calculate_reading_learning_score(study_df),
        "kinesthetic": _calculate_kinesthetic_learning_score(study_df)
    }
    
    # Determine primary and secondary learning styles
    sorted_styles = sorted(indicators.items(), key=lambda x: x[1], reverse=True)
    primary_style = sorted_styles[0][0]
    secondary_style = sorted_styles[1][0]
    
    # Calculate confidence based on data quality
    confidence = "high" if len(study_df) >= 14 else "medium" if len(study_df) >= 7 else "low"
    
    # Generate characteristics
    characteristics = _generate_learning_characteristics(
        primary_style,
        secondary_style,
        indicators
    )
    
    return {
        "primary_style": primary_style,
        "secondary_style": secondary_style,
        "confidence": confidence,
        "characteristics": characteristics,
        "style_scores": indicators
    }

def _calculate_visual_learning_score(study_df: pd.DataFrame) -> float:
    """Calculate visual learning preference score"""
    # This would be based on engagement with visual materials
    # For now, using a placeholder calculation
    return np.random.uniform(0.5, 1.0)

def _calculate_auditory_learning_score(study_df: pd.DataFrame) -> float:
    """Calculate auditory learning preference score"""
    # This would be based on engagement with audio materials
    # For now, using a placeholder calculation
    return np.random.uniform(0.5, 1.0)

def _calculate_reading_learning_score(study_df: pd.DataFrame) -> float:
    """Calculate reading/writing learning preference score"""
    # This would be based on engagement with text materials
    # For now, using a placeholder calculation
    return np.random.uniform(0.5, 1.0)

def _calculate_kinesthetic_learning_score(study_df: pd.DataFrame) -> float:
    """Calculate kinesthetic learning preference score"""
    # This would be based on engagement with interactive materials
    # For now, using a placeholder calculation
    return np.random.uniform(0.5, 1.0)

def _generate_learning_characteristics(
    primary_style: str,
    secondary_style: str,
    indicators: Dict[str, float]
) -> List[str]:
    """Generate learning style characteristics"""
    characteristics = []
    
    # Add primary style characteristics
    if primary_style == "visual":
        characteristics.extend([
            "Learns best through visual aids and diagrams",
            "Benefits from mind maps and visual organization"
        ])
    elif primary_style == "auditory":
        characteristics.extend([
            "Learns effectively through discussions and lectures",
            "Benefits from verbal explanations"
        ])
    elif primary_style == "reading":
        characteristics.extend([
            "Excels with written materials and notes",
            "Benefits from detailed reading and writing"
        ])
    elif primary_style == "kinesthetic":
        characteristics.extend([
            "Learns through hands-on experience",
            "Benefits from practical exercises and simulations"
        ])
    
    # Add secondary style characteristics
    if secondary_style == "visual":
        characteristics.append("Also responds well to visual learning methods")
    elif secondary_style == "auditory":
        characteristics.append("Also benefits from audio-based learning")
    elif secondary_style == "reading":
        characteristics.append("Also learns well through reading and writing")
    elif secondary_style == "kinesthetic":
        characteristics.append("Also learns through interactive activities")
    
    return characteristics

def _analyze_topic_preferences(user_data: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze user's topic preferences and strengths"""
    # This would analyze which topics the user performs best in
    # For now, returning placeholder data
    return {
        "preferred_topics": ["Anatomy", "Physiology", "Biochemistry"],
        "strengths": ["Memorization", "Problem Solving"],
        "areas_for_improvement": ["Clinical Reasoning", "Time Management"],
        "confidence": "medium"
    }

def _combine_patterns(
    study_patterns: Dict[str, Any],
    quiz_patterns: Dict[str, Any],
    progress_patterns: Dict[str, Any],
    learning_style: Dict[str, Any],
    topic_preferences: Dict[str, Any]
) -> Dict[str, Any]:
    """Combine all patterns into overall learning patterns"""
    return {
        "study_effectiveness": _calculate_study_effectiveness(
            study_patterns["study_consistency"],
            quiz_patterns["accuracy"]
        ),
        "learning_style": learning_style,
        "topic_preferences": topic_preferences,
        "recommended_focus": _determine_recommended_focus(
            study_patterns,
            quiz_patterns,
            progress_patterns,
            learning_style,
            topic_preferences
        )
    }

def _calculate_study_effectiveness(consistency: float, accuracy: float) -> str:
    """Calculate overall study effectiveness"""
    if consistency >= 70 and accuracy >= 80:
        return "high"
    elif consistency >= 50 and accuracy >= 60:
        return "medium"
    else:
        return "low"

def _determine_recommended_focus(
    study_patterns: Dict[str, Any],
    quiz_patterns: Dict[str, Any],
    progress_patterns: Dict[str, Any],
    learning_style: Dict[str, Any],
    topic_preferences: Dict[str, Any]
) -> List[str]:
    """Determine recommended focus areas"""
    recommendations = []
    
    # Study consistency recommendations
    if study_patterns["study_consistency"] < 50:
        recommendations.append("increase_study_consistency")
    
    # Quiz performance recommendations
    if quiz_patterns["accuracy"] < 70:
        recommendations.append("improve_quiz_performance")
    
    # Progress recommendations
    if progress_patterns["completion_rate"] < 50:
        recommendations.append("complete_more_materials")
    
    # Learning style recommendations
    if learning_style["confidence"] == "low":
        recommendations.append("explore_learning_style")
    
    # Topic-specific recommendations
    if topic_preferences["areas_for_improvement"]:
        recommendations.extend([
            f"focus_on_{area.lower().replace(' ', '_')}"
            for area in topic_preferences["areas_for_improvement"]
        ])
    
    return recommendations 