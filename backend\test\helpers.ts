import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import databaseConfig from '../src/config/database.config';

export const createTestingModule = async (imports: any[]) => {
  const module: TestingModule = await Test.createTestingModule({
    imports: [
      ConfigModule.forRoot({
        load: [databaseConfig],
        isGlobal: true,
      }),
      TypeOrmModule.forRoot(databaseConfig()),
      ...imports,
    ],
  }).compile();

  return module;
};
