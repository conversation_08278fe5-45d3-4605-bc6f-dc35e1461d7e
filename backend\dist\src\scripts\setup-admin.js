"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("../app.module");
const typeorm_1 = require("@nestjs/typeorm");
const user_entity_1 = require("../entities/user.entity");
const bcrypt = __importStar(require("bcrypt"));
async function bootstrap() {
    const app = await core_1.NestFactory.createApplicationContext(app_module_1.AppModule);
    const userRepository = app.get((0, typeorm_1.getRepositoryToken)(user_entity_1.User));
    const adminUser = await userRepository.findOne({ where: { email: '<EMAIL>' } });
    if (!adminUser) {
        console.log('Creating admin user...');
        const hashedPassword = await bcrypt.hash('admin123', 10);
        const newAdmin = new user_entity_1.User();
        newAdmin.email = '<EMAIL>';
        newAdmin.username = 'admin';
        newAdmin.name = 'Development Admin';
        newAdmin.first_name = 'Dev';
        newAdmin.last_name = 'Admin';
        newAdmin.password_hash = hashedPassword;
        newAdmin.role = user_entity_1.UserRole.ADMIN;
        newAdmin.is_active = true;
        newAdmin.email_verified = true;
        await userRepository.save(newAdmin);
        console.log('Admin user created successfully!');
        console.log('Email: <EMAIL>');
        console.log('Password: admin123');
    }
    else {
        console.log('Admin user already exists.');
    }
    await app.close();
}
bootstrap().catch(console.error);
//# sourceMappingURL=setup-admin.js.map