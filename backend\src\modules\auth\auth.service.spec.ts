import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { JwtService } from '@nestjs/jwt';
import { getRepositoryToken } from '@nestjs/typeorm';
import * as bcryptjs from 'bcryptjs';
import { User } from '../../entities/user.entity';
import { UserSession } from '../../entities/security.entity';
import { SecurityService } from './security.service';
import { RefreshTokenService } from './refresh-token.service';
import { TokenBlacklistService } from './token-blacklist.service';

jest.mock('bcryptjs', () => ({
  compare: jest.fn(),
  hash: jest.fn(),
  genSalt: jest.fn()
}));

describe('AuthService', () => {
  let service: AuthService;
  let userRepo: any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(UserSession),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
          },
        },
        {
          provide: SecurityService,
          useValue: {
            logSecurityEvent: jest.fn(),
            getSecuritySettings: jest.fn(),
            verifyTwoFactor: jest.fn(),
            sendVerificationEmail: jest.fn(),
            isEmailVerified: jest.fn(),
          },
        },
        {
          provide: RefreshTokenService,
          useValue: {
            createRefreshToken: jest.fn(),
            validateRefreshToken: jest.fn(),
            revokeRefreshToken: jest.fn(),
            revokeAllUserTokens: jest.fn(),
          },
        },
        {
          provide: TokenBlacklistService,
          useValue: {
            blacklist: jest.fn(),
            isBlacklisted: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    userRepo = module.get(getRepositoryToken(User));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('validateUser', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return user data without password if credentials are valid', async () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        password_hash: 'hashedpassword',
        role: 'student'
      };

      userRepo.findOne.mockResolvedValue(mockUser);
      (bcryptjs.compare as jest.Mock).mockResolvedValue(true);

      const result = await service.validateUser('<EMAIL>', 'password');
      
      const { password_hash, ...expectedResult } = mockUser;
      expect(result).toEqual(expectedResult);
      expect(userRepo.findOne).toHaveBeenCalledWith({ where: { email: '<EMAIL>' } });
      expect(bcryptjs.compare).toHaveBeenCalledWith('password', 'hashedpassword');
    });

    it('should return null if credentials are invalid', async () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        password_hash: 'hashedpassword'
      };

      userRepo.findOne.mockResolvedValue(mockUser);
      (bcryptjs.compare as jest.Mock).mockResolvedValue(false);

      const result = await service.validateUser('<EMAIL>', 'wrongpassword');
      expect(result).toBeNull();
      expect(userRepo.findOne).toHaveBeenCalledWith({ where: { email: '<EMAIL>' } });
      expect(bcryptjs.compare).toHaveBeenCalledWith('wrongpassword', 'hashedpassword');
    });

    it('should return null if user not found', async () => {
      userRepo.findOne.mockResolvedValue(null);

      const result = await service.validateUser('<EMAIL>', 'password');
      expect(result).toBeNull();
      expect(userRepo.findOne).toHaveBeenCalledWith({ where: { email: '<EMAIL>' } });
    });
  });
});