import { Injectable, NotFoundException, UnauthorizedException, ConflictException } from '@nestjs/common';
import { UsersRepository } from './repositories/users.repository';
import { CacheService } from '../../cache/cache.service';
import { User, UserRole } from '../../entities/user.entity';
import * as bcryptjs from 'bcryptjs';

@Injectable()
export class UsersService {
  private readonly CACHE_PREFIX = 'user';
  private readonly CACHE_TTL = 3600; // 1 hour
  private readonly SALT_ROUNDS = 12; // Increased from 10 for better security

  constructor(
    private readonly usersRepository: UsersRepository,
    private readonly cacheService: CacheService,
  ) {}

  /** Fetch all users directly from the repository */
  async findAll(): Promise<User[]> {
    return this.usersRepository.findAll();
  }

  /** Fetch a user by ID with caching */
  async findById(id: string): Promise<User | null> {
    if (!id) {
      throw new Error('User ID is required');
    }

    const cacheKey = this.cacheService.generateKey(this.CACHE_PREFIX, { id });
    const cachedUser = await this.cacheService.get<User>(cacheKey);

    if (cachedUser) {
      return cachedUser;
    }

    const user = await this.usersRepository.findById(id);

    if (user) {
      await this.cacheService.set(cacheKey, user, this.CACHE_TTL);
    }

    return user;
  }

  /** Fetch a user by email with caching */
  async findByEmail(email: string): Promise<User | null> {
    if (!email) {
      throw new Error('Email is required');
    }

    console.log(`[USERS-SERVICE] Finding user by email: ${email}`);

    const cacheKey = this.cacheService.generateKey(this.CACHE_PREFIX, { email });
    const cachedUser = await this.cacheService.get<User>(cacheKey);

    if (cachedUser) {
      console.log(`[USERS-SERVICE] User found in cache: ${email}`);
      return cachedUser;
    }

    console.log(`[USERS-SERVICE] User not in cache, querying database: ${email}`);
    const user = await this.usersRepository.findByEmail(email);

    if (user) {
      console.log(`[USERS-SERVICE] User found in database: ${email}, ID: ${user.id}`);
      await this.cacheService.set(cacheKey, user, this.CACHE_TTL);
      
      // Also cache by ID for consistency
      const idCacheKey = this.cacheService.generateKey(this.CACHE_PREFIX, { id: user.id });
      await this.cacheService.set(idCacheKey, user, this.CACHE_TTL);
    } else {
      console.log(`[USERS-SERVICE] User not found in database: ${email}`);
    }

    return user;
  }

  /** Create a new user with proper validation and password hashing */
  async create(emailOrUser: string | User, name?: string, password?: string): Promise<User> {
    console.log(`[USERS-SERVICE] Creating new user: ${typeof emailOrUser === 'string' ? emailOrUser : emailOrUser.email}`);

    let user: User;

    if (emailOrUser instanceof User) {
      user = emailOrUser;
      console.log(`[USERS-SERVICE] Creating user from User object: ${user.email}`);
    } else {
      // Validate email format
      if (!this.isValidEmail(emailOrUser)) {
        throw new Error('Invalid email format');
      }

      // Check if user already exists
      const existingUser = await this.findByEmail(emailOrUser);
      if (existingUser) {
        throw new ConflictException('User with this email already exists');
      }

      console.log(`[USERS-SERVICE] Creating user from email: ${emailOrUser}, name: ${name}`);
      user = new User();
      user.email = emailOrUser;
      user.name = name ?? '';
      user.username = await this.generateUniqueUsername(emailOrUser);
      
      if (!password) {
        console.error(`[USERS-SERVICE] Password missing for new user: ${emailOrUser}`);
        throw new Error('Password is required for new users');
      }

      // Validate password strength
      if (!this.isValidPassword(password)) {
        throw new Error('Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character');
      }

      console.log(`[USERS-SERVICE] Hashing password for new user: ${emailOrUser}`);
      user.password_hash = await bcryptjs.hash(password, this.SALT_ROUNDS);
      user.role = UserRole.STUDENT;
      user.createdAt = new Date();
      user.updatedAt = new Date();
    }

    console.log(`[USERS-SERVICE] Saving user to database: ${user.email}`);
    const newUser = await this.usersRepository.create(user);
    console.log(`[USERS-SERVICE] User created with ID: ${newUser.id}`);

    // Cache by both ID and email
    await this.setCacheForUser(newUser);
    
    return newUser;
  }

  /** Update a user and invalidate related cache */
  async update(user: User): Promise<User> {
    const { id, ...userData } = user;
    
    if (!id) {
      throw new Error('User ID is required for update');
    }

    const existingUser = await this.usersRepository.findById(id);
    if (!existingUser) {
      throw new NotFoundException('User not found');
    }

    // Update timestamp
    userData.updatedAt = new Date();
    
    await this.usersRepository.update(id, userData);
    const updatedUser = await this.usersRepository.findById(id);
    
    if (!updatedUser) {
      throw new NotFoundException('User not found after update');
    }

    // Clear old cache entries and set new ones
    await this.clearCacheForUser(existingUser);
    await this.setCacheForUser(updatedUser);
    
    return updatedUser;
  }

  /** Delete a user and clear its cache */
  async delete(id: string): Promise<void> {
    if (!id) {
      throw new Error('User ID is required');
    }

    const user = await this.usersRepository.findById(id);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    await this.usersRepository.delete(id);
    await this.clearCacheForUser(user);
  }

  /** Fetch all users with caching */
  async getAllUsers(): Promise<User[]> {
    const cacheKey = this.cacheService.generateKey(this.CACHE_PREFIX, { type: 'all_users' });
    const cachedUsers = await this.cacheService.get<User[]>(cacheKey);

    if (cachedUsers) {
      return cachedUsers;
    }

    const users = await this.usersRepository.findAll();
    await this.cacheService.set(cacheKey, users, this.CACHE_TTL);

    return users;
  }

  /** Fetch a user by username with caching */
  async findByUsername(username: string): Promise<User | null> {
    if (!username) {
      throw new Error('Username is required');
    }

    const cacheKey = this.cacheService.generateKey(this.CACHE_PREFIX, { username });
    const cachedUser = await this.cacheService.get<User>(cacheKey);

    if (cachedUser) {
      return cachedUser;
    }

    const user = await this.usersRepository.findByUsername(username);

    if (user) {
      await this.cacheService.set(cacheKey, user, this.CACHE_TTL);
    }

    return user;
  }

  /** Validate a user's credentials */
  async validateUser(username: string, password: string): Promise<User | null> {
    if (!username || !password) {
      throw new UnauthorizedException('Username and password are required');
    }

    const user = await this.findByUsername(username);
    if (!user) {
      throw new UnauthorizedException('Invalid username or password');
    }

    const isPasswordValid = await bcryptjs.compare(password, user.password_hash);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid username or password');
    }

    return user;
  }

  /** Reset a user's password */
  async resetPassword(email: string, newPassword: string): Promise<void> {
    if (!email || !newPassword) {
      throw new Error('Email and new password are required');
    }

    if (!this.isValidPassword(newPassword)) {
      throw new Error('Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character');
    }

    const user = await this.usersRepository.findByEmail(email);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const newPasswordHash = await bcryptjs.hash(newPassword, this.SALT_ROUNDS);
    await this.usersRepository.update(user.id, { 
      password_hash: newPasswordHash, 
      updatedAt: new Date() 
    });

    // Clear cache to ensure fresh data
    await this.clearCacheForUser(user);
  }

  /** Update a user's password with validation */
  async updatePassword(userId: string, oldPassword: string, newPassword: string): Promise<void> {
    if (!userId || !oldPassword || !newPassword) {
      throw new Error('User ID, old password, and new password are required');
    }

    if (!this.isValidPassword(newPassword)) {
      throw new Error('New password must be at least 8 characters long and contain uppercase, lowercase, number, and special character');
    }

    const user = await this.usersRepository.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const isPasswordValid = await bcryptjs.compare(oldPassword, user.password_hash);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Current password is incorrect');
    }

    const newPasswordHash = await bcryptjs.hash(newPassword, this.SALT_ROUNDS);
    await this.usersRepository.update(userId, { 
      password_hash: newPasswordHash,
      updatedAt: new Date()
    });

    // Clear cache
    await this.clearCacheForUser(user);
  }

  /** Update a user's role */
  async updateUserRole(id: string, role: UserRole): Promise<User> {
    if (!id || !role) {
      throw new Error('User ID and role are required');
    }

    const user = await this.usersRepository.findById(id);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    await this.usersRepository.update(id, { 
      role, 
      updatedAt: new Date() 
    });
    
    const updatedUser = await this.usersRepository.findById(id);
    if (!updatedUser) {
      throw new NotFoundException('User not found after update');
    }

    // Update cache
    await this.clearCacheForUser(user);
    await this.setCacheForUser(updatedUser);

    return updatedUser;
  }

  /** Update user details */
  async updateUserDetails(id: string, userDetails: Partial<User>): Promise<User> {
    if (!id) {
      throw new Error('User ID is required');
    }

    const user = await this.usersRepository.findById(id);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Prevent updating sensitive fields through this method
    const { password_hash, createdAt, ...safeDetails } = userDetails;
    safeDetails.updatedAt = new Date();

    await this.usersRepository.update(id, safeDetails);
    
    const updatedUser = await this.usersRepository.findById(id);
    if (!updatedUser) {
      throw new NotFoundException('User not found after update');
    }

    // Update cache
    await this.clearCacheForUser(user);
    await this.setCacheForUser(updatedUser);

    return updatedUser;
  }

  /** Clear cache for a specific user by ID */
  async clearCache(id: string): Promise<void> {
    if (!id) return;
    
    const user = await this.usersRepository.findById(id);
    if (user) {
      await this.clearCacheForUser(user);
    }
  }

  /** Clear all user-related cache */
  async clearAllCache(): Promise<void> {
    await this.cacheService.clear(this.CACHE_PREFIX);
  }

  // Private helper methods

  private async setCacheForUser(user: User): Promise<void> {
    const promises = [];
    
    // Cache by ID
    const idKey = this.cacheService.generateKey(this.CACHE_PREFIX, { id: user.id });
    promises.push(this.cacheService.set(idKey, user, this.CACHE_TTL));
    
    // Cache by email
    if (user.email) {
      const emailKey = this.cacheService.generateKey(this.CACHE_PREFIX, { email: user.email });
      promises.push(this.cacheService.set(emailKey, user, this.CACHE_TTL));
    }
    
    // Cache by username
    if (user.username) {
      const usernameKey = this.cacheService.generateKey(this.CACHE_PREFIX, { username: user.username });
      promises.push(this.cacheService.set(usernameKey, user, this.CACHE_TTL));
    }
    
    await Promise.all(promises);
  }

  private async clearCacheForUser(user: User): Promise<void> {
    const promises = [];
    
    // Clear cache by ID
    const idKey = this.cacheService.generateKey(this.CACHE_PREFIX, { id: user.id });
    promises.push(this.cacheService.delete(idKey));
    
    // Clear cache by email
    if (user.email) {
      const emailKey = this.cacheService.generateKey(this.CACHE_PREFIX, { email: user.email });
      promises.push(this.cacheService.delete(emailKey));
    }
    
    // Clear cache by username
    if (user.username) {
      const usernameKey = this.cacheService.generateKey(this.CACHE_PREFIX, { username: user.username });
      promises.push(this.cacheService.delete(usernameKey));
    }
    
    // Clear all users cache as well
    const allUsersKey = this.cacheService.generateKey(this.CACHE_PREFIX, { type: 'all_users' });
    promises.push(this.cacheService.delete(allUsersKey));
    
    await Promise.all(promises);
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidPassword(password: string): boolean {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return passwordRegex.test(password);
  }

  private async generateUniqueUsername(email: string): Promise<string> {
    const baseUsername = email.split('@')[0].toLowerCase().replace(/[^a-z0-9]/g, '');
    let username = baseUsername;
    let counter = 1;

    while (await this.findByUsername(username)) {
      username = `${baseUsername}${counter}`;
      counter++;
    }

    return username;
  }
}