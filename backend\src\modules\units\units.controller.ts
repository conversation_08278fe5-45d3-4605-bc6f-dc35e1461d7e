import { Controller, Get, Post, Put, Delete, Body, Param, UseGuards } from '@nestjs/common';
import { UnitsService } from './units.service';
import { Unit } from '../../entities/unit.entity';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';

@Controller('units')
@UseGuards(JwtAuthGuard)
export class UnitsController {
    constructor(private readonly unitsService: UnitsService) {}

    @Get()
    findAll(): Promise<Unit[]> {
        return this.unitsService.findAll();
    }

    @Get(':id')
    findOne(@Param('id') id: string): Promise<Unit> {
        return this.unitsService.findOne(id);
    }

    @Post()
    create(@Body() unit: Partial<Unit>): Promise<Unit> {
        return this.unitsService.create(unit);
    }

    @Put(':id')
    update(
        @Param('id') id: string,
        @Body() unit: Partial<Unit>,
    ): Promise<Unit> {
        return this.unitsService.update(id, unit);
    }

    @Delete(':id')
    async remove(@Param('id') id: string): Promise<void> {
        return this.unitsService.delete(id);
    }
}