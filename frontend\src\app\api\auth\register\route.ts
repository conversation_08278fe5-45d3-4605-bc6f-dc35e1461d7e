import { NextResponse } from 'next/server';

interface RegistrationBody {
  email: string;
  username: string;
  name?: string;
  password: string;
  role?: string;
}

// Validate registration data
function validateRegistrationData(body: RegistrationBody) {
  const errors: Record<string, string> = {};

  // Validate email
  if (!body.email) {
    errors.email = 'Email is required';
  } else if (!/\S+@\S+\.\S+/.test(body.email)) {
    errors.email = 'Email address is invalid';
  }

  // Validate username
  if (!body.username) {
    errors.username = 'Username is required';
  } else if (body.username.length < 3) {
    errors.username = 'Username must be at least 3 characters';
  } else if (!/^[a-zA-Z0-9_]+$/.test(body.username)) {
    errors.username = 'Username can only contain letters, numbers, and underscores';
  }

  // Validate password
  if (!body.password) {
    errors.password = 'Password is required';
  } else if (body.password.length < 4) {
    errors.password = 'Password must be at least 4 characters';
  }

  // Validate name if provided
  if (body.name && body.name.trim() === '') {
    errors.name = 'Name cannot be empty if provided';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

export async function POST(request: Request) {
  try {
    const body = await request.json() as RegistrationBody;
    console.log('Registration request received for:', body.email);

    // Validate the registration data
    const validation = validateRegistrationData(body);
    if (!validation.isValid) {
      return NextResponse.json(
        {
          message: 'Validation failed',
          errors: validation.errors
        },
        { status: 400 }
      );
    }

    try {
      // Try to connect to the backend first
      const response = await fetch('http://localhost:3002/v1/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          email: body.email,
          username: body.username,
          name: body.name || body.username, // Use username as name if not provided
          password: body.password,
          role: body.role || 'student' // Default to student role
        }),
        signal: AbortSignal.timeout(5000)
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('Registration failed:', data);

        // Check for specific error types
        let errorMessage = data.message || 'Registration failed';
        let statusCode = response.status;
        const fieldErrors: Record<string, string> = {};

        // Handle duplicate key errors more gracefully
        if (errorMessage.includes('duplicate key') || errorMessage.includes('already exists')) {
          if (errorMessage.includes('email')) {
            errorMessage = 'This email is already registered. Please use a different email or try logging in.';
            fieldErrors.email = 'Email already in use';
          } else if (errorMessage.includes('username') || errorMessage.includes('UQ_c0d176bcc1665dc7cb60482c817')) {
            // Check if there's a suggested username in the response
            let suggestedUsername = '';

            // Try to extract suggested username from the response
            if (data && typeof data === 'object' && 'suggestedUsername' in data) {
              suggestedUsername = data.suggestedUsername;
            }

            if (suggestedUsername) {
              errorMessage = `This username is already taken. Please choose a different username. Try '${suggestedUsername}' instead?`;
              fieldErrors.username = 'Username already taken';
              fieldErrors.suggestedUsername = suggestedUsername;
            } else {
              // Include the constraint name in the error message to help the frontend identify the issue
              errorMessage = 'This username is already taken. Please choose a different username.';
              fieldErrors.username = 'Username already taken';
            }
          } else {
            errorMessage = 'This account already exists. Please try a different email or username.';
          }
          statusCode = 409; // Conflict
        }

        return NextResponse.json(
          {
            message: errorMessage,
            errors: fieldErrors
          },
          { status: statusCode }
        );
      }

      console.log('Registration successful for:', body.email);
      // Add a success message to the response
      return NextResponse.json({
        ...data,
        message: 'Registration successful! You can now log in with your credentials.'
      });
    } catch (backendError) {
      console.error('Backend connection error:', backendError);

      // Fall back to mock response if backend is not available
      console.log('Falling back to mock response for:', body.email);

      // In development mode, we'll create a mock response
      // In production, we should return an error
      if (process.env.NODE_ENV === 'production') {
        return NextResponse.json(
          { message: 'Unable to connect to authentication service. Please try again later.' },
          { status: 503 } // Service Unavailable
        );
      }

      return NextResponse.json({
        user: {
          id: 'mock-user-' + Date.now(),
          email: body.email,
          name: body.name || body.username,
          role: body.role || 'student',
          username: body.username
        },
        accessToken: 'mock-token-' + Date.now(),
        refreshToken: 'mock-refresh-token-' + Date.now(),
        message: 'Registration successful! (MOCK RESPONSE - Backend unavailable) You can now log in with your credentials.'
      });
    }
  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

