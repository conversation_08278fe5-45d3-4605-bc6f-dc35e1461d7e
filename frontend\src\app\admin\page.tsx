import { 
  Users, 
  BookOpen, 
  Activity, 
  TrendingUp 
} from 'lucide-react';

export default function AdminDashboard() {
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Dashboard Overview</h1>
      
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Users"
          value="1,234"
          icon={Users}
          trend="+12%"
          trendUp={true}
        />
        <StatCard
          title="Active Courses"
          value="45"
          icon={BookOpen}
          trend="+5%"
          trendUp={true}
        />
        <StatCard
          title="Active Sessions"
          value="89"
          icon={Activity}
          trend="-2%"
          trendUp={false}
        />
        <StatCard
          title="Completion Rate"
          value="78%"
          icon={TrendingUp}
          trend="+8%"
          trendUp={true}
        />
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4">Recent Activity</h2>
        <div className="space-y-4">
          <ActivityItem
            user="John Doe"
            action="completed"
            target="Cardiology Module"
            time="2 hours ago"
          />
          <ActivityItem
            user="Jane Smith"
            action="enrolled in"
            target="Neurology Course"
            time="4 hours ago"
          />
          <ActivityItem
            user="Mike Johnson"
            action="uploaded"
            target="Study Materials"
            time="6 hours ago"
          />
        </div>
      </div>
    </div>
  );
}

interface StatCardProps {
  title: string;
  value: string;
  icon: any;
  trend: string;
  trendUp: boolean;
}

function StatCard({ title, value, icon: Icon, trend, trendUp }: StatCardProps) {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-semibold text-gray-900 mt-1">{value}</p>
        </div>
        <div className="p-3 bg-blue-50 rounded-full">
          <Icon className="w-6 h-6 text-blue-600" />
        </div>
      </div>
      <div className="mt-4">
        <span className={`text-sm font-medium ${trendUp ? 'text-green-600' : 'text-red-600'}`}>
          {trend}
        </span>
        <span className="text-sm text-gray-600 ml-2">from last month</span>
      </div>
    </div>
  );
}

interface ActivityItemProps {
  user: string;
  action: string;
  target: string;
  time: string;
}

function ActivityItem({ user, action, target, time }: ActivityItemProps) {
  return (
    <div className="flex items-center justify-between py-2">
      <div>
        <span className="font-medium">{user}</span>
        <span className="text-gray-600"> {action} </span>
        <span className="font-medium">{target}</span>
      </div>
      <span className="text-sm text-gray-500">{time}</span>
    </div>
  );
} 