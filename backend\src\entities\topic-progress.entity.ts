import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne } from 'typeorm';
import { User } from './user.entity';
import { Topic } from './topic.entity';

@Entity()
export class TopicProgress {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @ManyToOne(() => User, (user: User) => user.topic_progress)
    user: User;

    @ManyToOne(() => Topic, (topic: Topic) => topic.progress)
    topic: Topic;

    @Column({ type: 'float', default: 0 })
    completion_percentage: number;

    @Column({ type: 'int', default: 0 })
    time_spent_minutes: number;

    @Column({ type: 'jsonb', nullable: true })
    quiz_scores: {
        quiz_id: string;
        score: number;
        date: Date;
    }[];

    @Column({ type: 'jsonb', nullable: true })
    notes: {
        content: string;
        created_at: Date;
    }[];

    @Column({ default: false })
    is_completed: boolean;

    @Column({ type: 'timestamp', nullable: true })
    completed_at: Date;

    @Column({ type: 'int', default: 0 })
    streak_days: number;

    @Column({ type: 'timestamp', nullable: true })
    last_studied_at: Date;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
} 