# JWT Configuration
JWT_SECRET=${OCI_VAULT_JWT_SECRET}
JWT_EXPIRATION=1h
REFRESH_TOKEN_SECRET=${OCI_VAULT_REFRESH_TOKEN_SECRET}
REFRESH_TOKEN_EXPIRATION=7d

# Database Configuration
POSTGRES_HOST=${OCI_DB_HOST}
POSTGRES_PORT=5432
POSTGRES_USER=${OCI_DB_USER}
POSTGRES_PASSWORD=${OCI_DB_PASSWORD}
POSTGRES_DB=medical_tracker

# Server Configuration
PORT=3002
NODE_ENV=production

# Redis Configuration
REDIS_HOST=${OCI_REDIS_HOST}
REDIS_PORT=6379
REDIS_PASSWORD=${OCI_REDIS_PASSWORD}
REDIS_DB=0
REDIS_PREFIX=medtrack:

# CORS and Security
ALLOWED_ORIGINS=${FRONTEND_DOMAIN}
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_REQUESTS=100
LOGIN_RATE_LIMIT_MAX=5

# Set this to false in production
TYPEORM_SYNC=false

# Database Migration Configuration
TYPEORM_MIGRATIONS=dist/database/migrations/*.js
TYPEORM_MIGRATIONS_DIR=src/database/migrations

# AWS Configuration (if using S3)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=your_aws_region
AWS_S3_BUCKET=your_s3_bucket

# Railway-specific environment variables
RAILWAY_ENVIRONMENT=production
RAILWAY_PROJECT_ID=your_project_id
RAILWAY_SERVICE_NAME=your_service_name
RAILWAY_DATABASE_URL=${DATABASE_URL}
RAILWAY_REDIS_URL=${REDIS_URL}
RAILWAY_API_URL=${API_URL}
