from typing import Dict, Any, List
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def predict_performance(user_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Predict user's future performance using multiple models
    """
    try:
        # Prepare features
        features = _prepare_features(user_data)
        
        # Train multiple models
        models = {
            "random_forest": _train_random_forest(features),
            "gradient_boosting": _train_gradient_boosting(features),
            "linear": _train_linear_regression(features)
        }
        
        # Make predictions
        predictions = _make_predictions(models, features)
        
        # Calculate confidence intervals
        confidence_intervals = _calculate_confidence_intervals(predictions)
        
        # Detect anomalies
        anomalies = _detect_anomalies(user_data, predictions)
        
        # Analyze seasonal patterns
        seasonal_patterns = _analyze_seasonal_patterns(user_data)
        
        return {
            "predictions": predictions,
            "confidence_intervals": confidence_intervals,
            "anomalies": anomalies,
            "seasonal_patterns": seasonal_patterns,
            "model_metrics": _calculate_model_metrics(models, features),
            "recommendations": _generate_performance_recommendations(
                predictions,
                confidence_intervals,
                anomalies,
                seasonal_patterns
            )
        }
    except Exception as e:
        logger.error(f"Error predicting performance: {str(e)}")
        raise

def _prepare_features(user_data: Dict[str, Any]) -> pd.DataFrame:
    """Prepare features for prediction"""
    try:
        # Convert study time data to DataFrame
        study_df = pd.DataFrame(user_data["study_time"])
        study_df["study_date"] = pd.to_datetime(study_df["study_date"])
        
        # Calculate features
        features = pd.DataFrame()
        
        # Study time features
        features["avg_daily_study_time"] = study_df["total_duration"].mean()
        features["study_consistency"] = len(study_df) / 30  # Last 30 days
        features["max_streak"] = _calculate_max_streak(study_df)
        
        # Quiz performance features
        quiz_data = user_data["quiz_performance"]
        features["quiz_accuracy"] = quiz_data["accuracy"]
        features["total_questions"] = quiz_data["total_questions"]
        
        # Progress features
        progress_data = user_data["progress"]
        features["completion_rate"] = _calculate_completion_rate(progress_data)
        
        # Add time-based features
        features["day_of_week"] = datetime.now().weekday()
        features["is_weekend"] = features["day_of_week"].isin([5, 6]).astype(int)
        
        return features
    except Exception as e:
        logger.error(f"Error preparing features: {str(e)}")
        raise

def _train_random_forest(features: pd.DataFrame) -> Dict[str, Any]:
    """Train Random Forest model"""
    try:
        X = features.drop("target", axis=1)
        y = features["target"]
        
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        model = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            random_state=42
        )
        
        model.fit(X_train, y_train)
        
        return {
            "model": model,
            "feature_importance": dict(zip(X.columns, model.feature_importances_))
        }
    except Exception as e:
        logger.error(f"Error training Random Forest: {str(e)}")
        raise

def _train_gradient_boosting(features: pd.DataFrame) -> Dict[str, Any]:
    """Train Gradient Boosting model"""
    try:
        X = features.drop("target", axis=1)
        y = features["target"]
        
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        model = GradientBoostingRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=5,
            random_state=42
        )
        
        model.fit(X_train, y_train)
        
        return {
            "model": model,
            "feature_importance": dict(zip(X.columns, model.feature_importances_))
        }
    except Exception as e:
        logger.error(f"Error training Gradient Boosting: {str(e)}")
        raise

def _train_linear_regression(features: pd.DataFrame) -> Dict[str, Any]:
    """Train Linear Regression model"""
    try:
        X = features.drop("target", axis=1)
        y = features["target"]
        
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        model = LinearRegression()
        model.fit(X_train, y_train)
        
        return {
            "model": model,
            "coefficients": dict(zip(X.columns, model.coef_))
        }
    except Exception as e:
        logger.error(f"Error training Linear Regression: {str(e)}")
        raise

def _make_predictions(
    models: Dict[str, Dict[str, Any]],
    features: pd.DataFrame
) -> Dict[str, Any]:
    """Make predictions using all models"""
    predictions = {}
    
    for model_name, model_data in models.items():
        predictions[model_name] = model_data["model"].predict(features)
    
    # Calculate ensemble prediction
    predictions["ensemble"] = np.mean([
        predictions["random_forest"],
        predictions["gradient_boosting"],
        predictions["linear"]
    ], axis=0)
    
    return predictions

def _calculate_confidence_intervals(
    predictions: Dict[str, np.ndarray]
) -> Dict[str, Dict[str, float]]:
    """Calculate confidence intervals for predictions"""
    intervals = {}
    
    for model_name, preds in predictions.items():
        mean = np.mean(preds)
        std = np.std(preds)
        
        intervals[model_name] = {
            "lower_95": mean - 1.96 * std,
            "upper_95": mean + 1.96 * std,
            "lower_99": mean - 2.576 * std,
            "upper_99": mean + 2.576 * std
        }
    
    return intervals

def _detect_anomalies(
    user_data: Dict[str, Any],
    predictions: Dict[str, np.ndarray]
) -> List[Dict[str, Any]]:
    """Detect anomalies in user's performance"""
    anomalies = []
    
    # Calculate z-scores for recent performance
    recent_performance = user_data["quiz_performance"]["accuracy"]
    mean_performance = np.mean(list(predictions.values()))
    std_performance = np.std(list(predictions.values()))
    
    z_score = (recent_performance - mean_performance) / std_performance
    
    if abs(z_score) > 2:
        anomalies.append({
            "type": "performance_anomaly",
            "severity": "high" if abs(z_score) > 3 else "medium",
            "description": f"Recent performance is {z_score:.2f} standard deviations from expected",
            "recommendation": "Review recent study patterns and quiz performance"
        })
    
    return anomalies

def _analyze_seasonal_patterns(user_data: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze seasonal patterns in user's performance"""
    # Convert study time data to DataFrame
    study_df = pd.DataFrame(user_data["study_time"])
    study_df["study_date"] = pd.to_datetime(study_df["study_date"])
    
    # Add day of week
    study_df["day_of_week"] = study_df["study_date"].dt.dayofweek
    
    # Calculate average performance by day of week
    daily_performance = study_df.groupby("day_of_week")["total_duration"].mean()
    
    # Identify best and worst days
    best_day = daily_performance.idxmax()
    worst_day = daily_performance.idxmin()
    
    return {
        "best_day": best_day,
        "worst_day": worst_day,
        "daily_patterns": daily_performance.to_dict(),
        "recommendations": _generate_seasonal_recommendations(best_day, worst_day)
    }

def _generate_seasonal_recommendations(
    best_day: int,
    worst_day: int
) -> List[str]:
    """Generate recommendations based on seasonal patterns"""
    recommendations = []
    
    day_names = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
    
    recommendations.append(
        f"Schedule intensive study sessions on {day_names[best_day]}s"
    )
    recommendations.append(
        f"Use {day_names[worst_day]}s for review and lighter study activities"
    )
    
    return recommendations

def _calculate_model_metrics(
    models: Dict[str, Dict[str, Any]],
    features: pd.DataFrame
) -> Dict[str, Dict[str, float]]:
    """Calculate metrics for each model"""
    metrics = {}
    
    for model_name, model_data in models.items():
        model = model_data["model"]
        X = features.drop("target", axis=1)
        y = features["target"]
        
        # Calculate R² score
        r2_score = model.score(X, y)
        
        # Calculate mean squared error
        y_pred = model.predict(X)
        mse = np.mean((y - y_pred) ** 2)
        
        metrics[model_name] = {
            "r2_score": r2_score,
            "mse": mse
        }
    
    return metrics

def _generate_performance_recommendations(
    predictions: Dict[str, np.ndarray],
    confidence_intervals: Dict[str, Dict[str, float]],
    anomalies: List[Dict[str, Any]],
    seasonal_patterns: Dict[str, Any]
) -> List[str]:
    """Generate performance recommendations"""
    recommendations = []
    
    # Add recommendations based on predictions
    ensemble_pred = predictions["ensemble"][-1]
    if ensemble_pred < 0.7:
        recommendations.append("Focus on improving quiz performance")
    
    # Add recommendations based on confidence intervals
    ensemble_intervals = confidence_intervals["ensemble"]
    if ensemble_intervals["upper_95"] - ensemble_intervals["lower_95"] > 0.2:
        recommendations.append("Work on consistency in study habits")
    
    # Add recommendations based on anomalies
    for anomaly in anomalies:
        recommendations.append(anomaly["recommendation"])
    
    # Add recommendations based on seasonal patterns
    recommendations.extend(seasonal_patterns["recommendations"])
    
    return recommendations 