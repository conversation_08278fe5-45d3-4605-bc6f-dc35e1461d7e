// src/entities/clinical-case.entity.ts
import { 
    En<PERSON>ty, 
    Column, 
    PrimaryGeneratedColumn, 
    CreateDateColumn,
    UpdateDateColumn,
    ManyToOne,
    ManyToMany,
    JoinTable
  } from 'typeorm';
  import { User } from './user.entity';
  import { Unit } from './unit.entity';
  
  @Entity('clinical_cases')
  export class ClinicalCase {
    @PrimaryGeneratedColumn()
    id: number;
  
    @Column()
    title: string;
  
    @Column('text')
    patient_history: string;
  
    @Column('text')
    clinical_findings: string;
  
    @Column('text', { nullable: true })
    lab_results: string;
  
    @Column('text', { nullable: true })
    imaging_results: string;
  
    @Column('text')
    diagnosis: string;
  
    @Column('text')
    treatment: string;
  
    @Column('text')
    outcome_and_follow_up: string;
  
    @Column('text')
    learning_points: string;
  
    @Column('json', { nullable: true })
    questions: any;
  
    @Column('boolean', { default: false })
    is_published: boolean;
  
    @Column('int', { default: 0 })
    view_count: number;
  
    @ManyToOne(() => User, (user: User) => user.created_clinical_cases)
    author: User;
  
    @ManyToMany(() => Unit)
    @JoinTable({
      name: 'clinical_case_units',
      joinColumn: { name: 'clinical_case_id', referencedColumnName: 'id' },
      inverseJoinColumn: { name: 'unit_id', referencedColumnName: 'id' },
    })
    related_units: Unit[];
  
    @CreateDateColumn()
    created_at: Date;
  
    @UpdateDateColumn()
    updated_at: Date;
  }
  
  // src/dto/clinical-cases/create-clinical-case.dto.ts
  import { IsNotEmpty, IsString, IsOptional, IsArray, IsBoolean } from 'class-validator';
  
  export class CreateClinicalCaseDto {
    @IsNotEmpty()
    @IsString()
    title: string;
  
    @IsNotEmpty()
    @IsString()
    patient_history: string;
  
    @IsNotEmpty()
    @IsString()
    clinical_findings: string;
  
    @IsOptional()
    @IsString()
    lab_results?: string;
  
    @IsOptional()
    @IsString()
    imaging_results?: string;
  
    @IsNotEmpty()
    @IsString()
    diagnosis: string;
  
    @IsNotEmpty()
    @IsString()
    treatment: string;
  
    @IsNotEmpty()
    @IsString()
    outcome_and_follow_up: string;
  
    @IsNotEmpty()
    @IsString()
    learning_points: string;
  
    @IsOptional()
    questions?: any;
  
    @IsArray()
    related_unit_ids: number[];
  
    @IsBoolean()
    @IsOptional()
    is_published?: boolean = false;
  }
  