import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QuizService } from './quiz.service';
import { QuizController } from './quiz.controller';
import { QuizQuestion } from '../entities/quiz-question.entity';
import { UserResponse } from '../entities/user-response.entity';

@Module({
    imports: [TypeOrmModule.forFeature([QuizQuestion, UserResponse])],
    controllers: [QuizController],
    providers: [QuizService],
    exports: [QuizService],
})
export class QuizModule {}