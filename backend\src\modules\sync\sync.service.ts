import { Injectable, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { QuizAttempt } from '../../entities/quiz-attempt.entity';
import { Progress } from '../../entities/progress.entity';
import { SyncBulkDto } from './dto/sync-bulk.dto';
import { QuizAttemptSyncDto } from './dto/quiz-attempt-sync.dto';
import { ProgressSyncDto } from './dto/progress-sync.dto';

@Injectable()
export class SyncService {
  constructor(
    @InjectRepository(QuizAttempt)
    private readonly quizAttemptRepository: Repository<QuizAttempt>,
    @InjectRepository(Progress)
    private readonly progressRepository: Repository<Progress>,
  ) {}

  async syncBulk(userId: string, syncData: SyncBulkDto) {
    const results = {
      quiz_attempts: await this.processQuizAttempts(userId, syncData.quizAttempts),
      progress: await this.processProgress(userId, syncData.progress),
    };

    return {
      success: true,
      results,
      timestamp: new Date().toISOString(),
    };
  }

  async processQuizAttempts(userId: string, attempts: QuizAttemptSyncDto[]) {
    const results = {
      created: 0,
      updated: 0,
      conflicts: 0,
    };

    for (const attempt of attempts) {
      try {
        const existingAttempt = await this.quizAttemptRepository.findOne({
          where: { id: attempt.id },
        });

        if (existingAttempt) {
          // Check for conflicts
          if (existingAttempt.updated_at > attempt.updatedAt) {
            results.conflicts++;
            continue;
          }

          // Update existing attempt
          await this.quizAttemptRepository.update(attempt.id, {
            ...attempt,
            user: { id: userId },
          });
          results.updated++;
        } else {
          // Create new attempt
          await this.quizAttemptRepository.save({
            ...attempt,
            user: { id: userId },
          });
          results.created++;
        }
      } catch (error) {
        results.conflicts++;
      }
    }

    return results;
  }

  async processProgress(userId: string, progress: ProgressSyncDto[]) {
    const results = {
      created: 0,
      updated: 0,
      conflicts: 0,
    };

    for (const item of progress) {
      try {
        const existingProgress = await this.progressRepository.findOne({
          where: {
            user: { id: userId },
            material: { id: item.materialId },
          },
        });

        if (existingProgress) {
          // Check for conflicts
          if (existingProgress.updated_at > item.updatedAt) {
            results.conflicts++;
            continue;
          }

          // Update existing progress
          await this.progressRepository.update(
            { user: { id: userId }, material: { id: item.materialId } },
            {
              ...item,
              user: { id: userId },
              material: { id: item.materialId },
            },
          );
          results.updated++;
        } else {
          // Create new progress
          await this.progressRepository.save({
            ...item,
            user: { id: userId },
            material: { id: item.materialId },
          });
          results.created++;
        }
      } catch (error) {
        results.conflicts++;
      }
    }

    return results;
  }

  async getSyncStatus(userId: string) {
    const lastQuizAttempt = await this.quizAttemptRepository.findOne({
      where: { user: { id: userId } },
      order: { updated_at: 'DESC' },
    });

    const lastProgress = await this.progressRepository.findOne({
      where: { user: { id: userId } },
      order: { updated_at: 'DESC' },
    });

    return {
      lastSync: {
        quiz_attempts: lastQuizAttempt?.updated_at || null,
        progress: lastProgress?.updated_at || null,
      },
      pendingChanges: {
        quiz_attempts: await this.quizAttemptRepository.count({
          where: { user: { id: userId } }
        }),
        progress: await this.progressRepository.count({
          where: { user: { id: userId } }
        }),
      },
    };
  }

  async syncUserProgress(userId: string): Promise<void> {
    const unsyncedProgress = await this.progressRepository.find({
      where: { user: { id: userId } },
      relations: ['user', 'material']
    });

    for (const progress of unsyncedProgress) {
      // Sync progress logic here
      progress.is_completed = true;
      await this.progressRepository.save(progress);
    }
  }
} 