// src/modules/auth/auth.module.ts
import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtStrategy } from './jwt.strategy';
import { SecurityService } from './security.service';
import { RefreshTokenService } from './refresh-token.service';
import { TokenBlacklistService } from './token-blacklist.service';
import { UsersModule } from '../users/users.module';

// Import entities
import { User } from '../../entities/user.entity';
import {
  UserSession,
  SecurityEvent,
  UserSecuritySettings,
} from '../../entities/security.entity';

@Module({
  imports: [
    ConfigModule,
    // Import TypeORM features for ALL entities used in this module
    TypeOrmModule.forFeature([
      User,
      UserSession,
      SecurityEvent,
      UserSecuritySettings, // Add the missing entity
    ]),

    // Import PassportModule
    PassportModule.register({ defaultStrategy: 'jwt' }),

    // Import UsersModule to provide UsersService
    UsersModule,

    // Configure JWT Module
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '1d'),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    JwtStrategy,
    SecurityService,
    RefreshTokenService,
    TokenBlacklistService,
  ],
  exports: [AuthService, JwtModule, PassportModule],
})
export class AuthModule {}
