from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from typing import Dict, Any, Optional
import os
from dotenv import load_dotenv
from analytics.learning_patterns import analyze_learning_patterns
from analytics.performance_prediction import predict_performance
from analytics.data_processor import process_user_data
from analytics.recommendations import generate_recommendations
from auth.jwt_handler import verify_token

# Load environment variables
load_dotenv()

app = FastAPI(
    title="Medical Education Analytics API",
    description="Advanced analytics service for medical education platform",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[os.getenv("FRONTEND_URL", "http://localhost:3000")],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.get("/analytics/user/{user_id}")
async def get_user_analytics(
    user_id: str,
    current_user: Dict[str, Any] = Depends(verify_token)
) -> Dict[str, Any]:
    try:
        # Verify user has permission to access this data
        if current_user.role not in ["admin", "instructor"] and str(current_user.sub) != user_id:
            raise HTTPException(
                status_code=403,
                detail="Not authorized to access this user's analytics"
            )
        
        # Process user data
        user_data = await process_user_data(user_id)
        
        # Get learning patterns
        patterns = analyze_learning_patterns(user_data)
        
        # Get performance predictions
        predictions = predict_performance(user_data)
        
        # Generate recommendations
        recommendations = generate_recommendations(patterns, predictions)
        
        return {
            "learning_patterns": patterns,
            "performance_predictions": predictions,
            "study_recommendations": recommendations
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/analytics/patterns/{user_id}")
async def get_learning_patterns(
    user_id: str,
    current_user: Dict[str, Any] = Depends(verify_token)
) -> Dict[str, Any]:
    try:
        # Verify user has permission to access this data
        if current_user.role not in ["admin", "instructor"] and str(current_user.sub) != user_id:
            raise HTTPException(
                status_code=403,
                detail="Not authorized to access this user's learning patterns"
            )
            
        user_data = await process_user_data(user_id)
        patterns = analyze_learning_patterns(user_data)
        return patterns
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/analytics/predictions/{user_id}")
async def get_performance_predictions(
    user_id: str,
    current_user: Dict[str, Any] = Depends(verify_token)
) -> Dict[str, Any]:
    try:
        # Verify user has permission to access this data
        if current_user.role not in ["admin", "instructor"] and str(current_user.sub) != user_id:
            raise HTTPException(
                status_code=403,
                detail="Not authorized to access this user's predictions"
            )
            
        user_data = await process_user_data(user_id)
        predictions = predict_performance(user_data)
        return predictions
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/auth/status")
async def auth_status(current_user: Dict[str, Any] = Depends(verify_token)) -> Dict[str, Any]:
    """
    Check authentication status and return current user information
    """
    return {
        "authenticated": True,
        "user": {
            "id": current_user.sub,
            "email": current_user.email,
            "username": current_user.username,
            "role": current_user.role
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("PYTHON_ANALYTICS_PORT", "5000")),
        reload=True
    )