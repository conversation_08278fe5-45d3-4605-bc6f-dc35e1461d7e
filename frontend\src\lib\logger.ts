type LogLevel = 'info' | 'warn' | 'error';

class Logger {
  private prefix = '[MedTrack]';

  private log(level: LogLevel, ...args: any[]) {
    if (process.env.NODE_ENV === 'production') {
      // In production, only log errors
      if (level === 'error') {
        console.error(this.prefix, ...args);
      }
      return;
    }

    switch (level) {
      case 'info':
        console.log(this.prefix, ...args);
        break;
      case 'warn':
        console.warn(this.prefix, ...args);
        break;
      case 'error':
        console.error(this.prefix, ...args);
        break;
    }
  }

  info(...args: any[]) {
    this.log('info', ...args);
  }

  warn(...args: any[]) {
    this.log('warn', ...args);
  }

  error(...args: any[]) {
    this.log('error', ...args);
  }
}

export const logger = new Logger();
