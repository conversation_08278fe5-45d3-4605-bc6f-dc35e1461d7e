import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class ErrorService {
  private readonly logger = new Logger(ErrorService.name);
  private readonly isDevelopment = process.env.NODE_ENV === 'development';

  logError(error: Error, context: string, sensitiveData?: any) {
    // In development, we log everything
    if (this.isDevelopment) {
      this.logger.error({
        message: error.message,
        stack: error.stack,
        context,
        sensitiveData,
      });
      return;
    }

    // In production, we only log non-sensitive information
    this.logger.error({
      message: this.sanitizeErrorMessage(error.message),
      context,
      timestamp: new Date().toISOString(),
    });
  }

  private sanitizeErrorMessage(message: string): string {
    // Generic messages for common errors
    if (message.includes('password')) {
      return 'Authentication error occurred';
    }
    if (message.includes('email') || message.includes('username')) {
      return 'Invalid credentials';
    }
    if (message.includes('token')) {
      return 'Session error occurred';
    }
    if (message.includes('permission') || message.includes('forbidden')) {
      return 'Access denied';
    }
    
    // Default generic message
    return 'An error occurred';
  }

  getPublicErrorMessage(error: Error): string {
    return this.sanitizeErrorMessage(error.message);
  }
}
