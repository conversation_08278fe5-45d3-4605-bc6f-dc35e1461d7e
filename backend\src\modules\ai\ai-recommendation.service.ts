import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { User } from '../../entities/user.entity';
import { Material } from '../../entities/materials.entity';
import { LearningSuggestion } from '../../entities/learning-suggestion.entity';
import { AIService } from './ai.service';

interface Recommendation {
    materialId: string;
    score: number;
    reason: string;
}

@Injectable()
export class AIRecommendationService {
    constructor(
        @InjectRepository(User)
        private readonly userRepository: Repository<User>,
        @InjectRepository(Material)
        private readonly materialRepository: Repository<Material>,
        @InjectRepository(LearningSuggestion)
        private readonly learningSuggestionRepository: Repository<LearningSuggestion>,
        private readonly aiService: AIService
    ) {}

    async getPersonalizedRecommendations(userId: string): Promise<Recommendation[]> {
        // Get raw recommendations from AI service
        const recommendations = await this.aiService.getRecommendations(userId);

        // Filter out already completed materials and format response
        return recommendations.map((rec: Recommendation) => ({
            materialId: rec.materialId,
            score: rec.score,
            reason: rec.reason
        }));
    }

    async generateRecommendations(userId: string): Promise<Material[]> {
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user) throw new Error('User not found');

        // Get user's learning history and preferences
        const userHistory = await this.getUserLearningHistory(userId);
        const userPreferences = await this.getUserPreferences(userId);

        // Get AI recommendations
        const recommendations = await this.aiService.getRecommendations(userId);

        // Get recommended materials
        const recommendedMaterials = await this.materialRepository.find({
            where: {
                id: In(recommendations.map((rec: Recommendation) => rec.materialId))
            },
            relations: ['author', 'unit']
        });

        return recommendedMaterials;
    }

    private async getUserLearningHistory(userId: string): Promise<any> {
        // Implement user learning history retrieval
        return {};
    }

    private async getUserPreferences(userId: string): Promise<any> {
        // Implement user preferences retrieval
        return {};
    }
} 