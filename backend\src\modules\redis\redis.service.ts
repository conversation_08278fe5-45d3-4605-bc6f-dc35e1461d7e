import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';

@Injectable()
export class RedisService {
    private readonly logger = new Logger(RedisService.name);
    private readonly redis: Redis;
    private readonly memoryStore: Map<string, { value: string; expiry?: number }> = new Map();
    private isConnected = false;

    constructor(private configService: ConfigService) {
        this.redis = new Redis({
            host: this.configService.get('redis.host'),
            port: this.configService.get('redis.port'),
            password: this.configService.get('redis.password'),
            db: this.configService.get('redis.db'),
            keyPrefix: this.configService.get('redis.keyPrefix'),
            retryStrategy: this.configService.get('redis.retryStrategy'),
            maxRetriesPerRequest: this.configService.get('redis.maxRetriesPerRequest'),
            enableOfflineQueue: this.configService.get('redis.enableOfflineQueue'),
        });

        this.redis.on('connect', () => {
            this.isConnected = true;
            this.logger.log('Connected to Redis');
        });

        this.redis.on('error', (error) => {
            this.isConnected = false;
            this.logger.warn('Redis connection error, using in-memory fallback:', error.message);
        });

        // Clean up expired items from memory store periodically
        setInterval(() => {
            const now = Date.now();
            for (const [key, data] of this.memoryStore.entries()) {
                if (data.expiry && data.expiry < now) {
                    this.memoryStore.delete(key);
                }
            }
        }, 60000); // Check every minute
    }

    private async getFromMemory(key: string): Promise<string | null> {
        const data = this.memoryStore.get(key);
        if (!data) return null;
        if (data.expiry && data.expiry < Date.now()) {
            this.memoryStore.delete(key);
            return null;
        }
        return data.value;
    }

    private setInMemory(key: string, value: string, ttlSeconds?: number): void {
        const data: { value: string; expiry?: number } = { value };
        if (ttlSeconds) {
            data.expiry = Date.now() + (ttlSeconds * 1000);
        }
        this.memoryStore.set(key, data);
    }

    async get(key: string): Promise<string | null> {
        if (this.isConnected) {
            try {
                return await this.redis.get(key);
            } catch (error) {
                this.logger.warn('Redis get failed, falling back to memory:', error.message);
            }
        }
        return this.getFromMemory(key);
    }

    async set(key: string, value: string, ttlSeconds?: number): Promise<void> {
        this.setInMemory(key, value, ttlSeconds);
        if (this.isConnected) {
            try {
                if (ttlSeconds) {
                    await this.redis.setex(key, ttlSeconds, value);
                } else {
                    await this.redis.set(key, value);
                }
            } catch (error) {
                this.logger.warn('Redis set failed, using memory only:', error.message);
            }
        }
    }

    async del(key: string): Promise<void> {
        this.memoryStore.delete(key);
        if (this.isConnected) {
            try {
                await this.redis.del(key);
            } catch (error) {
                this.logger.warn('Redis del failed, using memory only:', error.message);
            }
        }
    }

    async exists(key: string): Promise<boolean> {
        if (this.isConnected) {
            try {
                const result = await this.redis.exists(key);
                return result === 1;
            } catch (error) {
                this.logger.warn('Redis exists failed, falling back to memory:', error.message);
            }
        }
        return this.memoryStore.has(key);
    }

    async onApplicationShutdown(): Promise<void> {
        if (this.isConnected) {
            await this.redis.quit();
        }
    }
} 