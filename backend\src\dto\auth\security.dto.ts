// src/dto/auth/security.dto.ts
import { IsString, IsOptional, IsBoolean, MinLength, IsEmail } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class SecuritySettingsDto {
  @ApiProperty({ description: 'Enable two-factor authentication', required: false })
  @IsOptional()
  @IsBoolean()
  twoFactorEnabled?: boolean;

  @ApiProperty({ description: 'Login notification settings', required: false })
  @IsOptional()
  @IsBoolean()
  loginNotifications?: boolean;

  @ApiProperty({ description: 'Account activity notifications', required: false })
  @IsOptional()
  @IsBoolean()
  activityNotifications?: boolean;

  @ApiProperty({ description: 'Session timeout in minutes', required: false })
  @IsOptional()
  sessionTimeout?: number;
}

export class ChangePasswordDto {
  @ApiProperty({ description: 'Current password' })
  @IsString()
  @MinLength(1)
  currentPassword: string;

  @ApiProperty({ description: 'New password' })
  @IsString()
  @MinLength(8)
  newPassword: string;

  @ApiProperty({ description: 'Confirm new password' })
  @IsString()
  @MinLength(8)
  confirmPassword: string;
}

export class TwoFactorSetupDto {
  @ApiProperty({ description: 'User ID for 2FA setup' })
  @IsString()
  userId: string;
}

export class VerifyTwoFactorDto {
  @ApiProperty({ description: 'TOTP token from authenticator app' })
  @IsString()
  @MinLength(6)
  token: string;
}

export class EmailVerificationDto {
  @ApiProperty({ description: 'Email verification token' })
  @IsString()
  token: string;
}

export class ResendVerificationDto {
  @ApiProperty({ description: 'Email address to resend verification' })
  @IsEmail()
  email: string;
}

export class RevokeSessionDto {
  @ApiProperty({ description: 'Session ID to revoke' })
  @IsString()
  sessionId: string;
}

export class AccountRecoveryDto {
  @ApiProperty({ description: 'Email address for account recovery' })
  @IsEmail()
  email: string;
}

export class VerifyRecoveryDto {
  @ApiProperty({ description: 'Recovery token' })
  @IsString()
  token: string;

  @ApiProperty({ description: 'Security question answers' })
  answers: { [key: string]: string };
}