import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CPDService } from './cpd.service';
import { CPDController } from './cpd.controller';
import { CPDActivity, CPDCycle } from '../../entities/cpd-tracking.entity';
import { User } from '../../entities/user.entity';
import { Material } from '../../entities/materials.entity';

@Module({
    imports: [
        TypeOrmModule.forFeature([CPDActivity, CPDCycle, User, Material]),
    ],
    providers: [CPDService],
    controllers: [CPDController],
    exports: [CPDService],
})
export class CPDModule {} 