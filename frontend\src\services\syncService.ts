import { offlineStorage } from './offlineStorage';
import api from './api';

class SyncService {
  private isOnline: boolean = navigator.onLine;
  private syncInProgress: boolean = false;
  private readonly MAX_RETRY_COUNT = 3;
  private readonly SYNC_INTERVAL = 5 * 60 * 1000; // 5 minutes

  constructor() {
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);
    this.startPeriodicSync();
  }

  private handleOnline = () => {
    this.isOnline = true;
    this.sync();
  };

  private handleOffline = () => {
    this.isOnline = false;
  };

  private startPeriodicSync() {
    setInterval(() => {
      if (this.isOnline && !this.syncInProgress) {
        this.sync();
      }
    }, this.SYNC_INTERVAL);
  }

  async sync() {
    if (this.syncInProgress || !this.isOnline) return;

    try {
      this.syncInProgress = true;

      // Sync progress
      const unsyncedProgress = await offlineStorage.getUnsyncedProgress();
      for (const progress of unsyncedProgress) {
        try {
          await api.post('/progress', progress);
          await offlineStorage.markAsSynced(progress.id);
        } catch (error) {
          console.error('Error syncing progress:', error);
          // Add to sync queue for retry
          await offlineStorage.addToSyncQueue({
            id: `progress-${progress.id}`,
            type: 'update',
            data: progress,
            timestamp: new Date(),
            retryCount: 0
          });
        }
      }

      // Process sync queue
      const syncQueue = await offlineStorage.getSyncQueue();
      for (const action of syncQueue) {
        if (action.retryCount >= this.MAX_RETRY_COUNT) {
          // Remove from queue if max retries reached
          await offlineStorage.removeFromSyncQueue(action.id);
          continue;
        }

        try {
          switch (action.type) {
            case 'create':
              await api.post(action.data.endpoint, action.data.payload);
              break;
            case 'update':
              await api.patch(action.data.endpoint, action.data.payload);
              break;
            case 'delete':
              await api.delete(action.data.endpoint);
              break;
          }
          await offlineStorage.removeFromSyncQueue(action.id);
        } catch (error) {
          console.error('Error processing sync queue item:', error);
          await offlineStorage.incrementRetryCount(action.id);
        }
      }
    } finally {
      this.syncInProgress = false;
    }
  }

  async saveProgress(progress: any) {
    // Save locally first
    await offlineStorage.saveProgress(progress);

    // If online, try to sync immediately
    if (this.isOnline) {
      try {
        await api.post('/progress', progress);
        await offlineStorage.markAsSynced(progress.id);
      } catch (error) {
        console.error('Error syncing progress:', error);
        // Add to sync queue for retry
        await offlineStorage.addToSyncQueue({
          id: `progress-${progress.id}`,
          type: 'update',
          data: progress,
          timestamp: new Date(),
          retryCount: 0
        });
      }
    }
  }

  async getProgress(userId: string) {
    if (this.isOnline) {
      try {
        const response = await api.get(`/progress/user/${userId}`);
        // Update local storage with fresh data
        for (const progress of response.data) {
          await offlineStorage.saveProgress({
            ...progress,
            synced: true
          });
        }
        return response.data;
      } catch (error) {
        console.error('Error fetching progress:', error);
        // Fall back to local data
        return offlineStorage.getProgress(userId);
      }
    } else {
      // Offline - use local data
      return offlineStorage.getProgress(userId);
    }
  }
}

export const syncService = new SyncService(); 