import { Repository, FindOptionsWhere, ObjectLiteral, DeepPartial, FindOneOptions } from 'typeorm';
import { Injectable } from '@nestjs/common';

export interface IBaseRepository<T extends ObjectLiteral> {
    findAll(): Promise<T[]>;
    findById(id: string): Promise<T | null>;
    findOne(options: FindOneOptions<T>): Promise<T | null>;
    findMany(filter: Partial<T>): Promise<T[]>;
    create(data: DeepPartial<T>): T;
    update(id: string, data: Partial<T>): Promise<T | null>;
    delete(id: string): Promise<boolean>;
    save(entity: DeepPartial<T>): Promise<T>;
}

@Injectable()
export class BaseRepository<T extends ObjectLiteral> implements IBaseRepository<T> {
    constructor(protected readonly repository: Repository<T>) {}

    async findAll(): Promise<T[]> {
        return this.repository.find();
    }

    async findById(id: string): Promise<T | null> {
        return this.repository.findOne({ where: { id } as unknown as FindOptionsWhere<T> });
    }

    async findOne(options: FindOneOptions<T>): Promise<T | null> {
        return this.repository.findOne(options);
    }

    async findMany(filter: Partial<T>): Promise<T[]> {
        return this.repository.find({ where: filter as FindOptionsWhere<T> });
    }

    create(data: DeepPartial<T>): T {
        return this.repository.create(data);
    }

    async update(id: string, data: Partial<T>): Promise<T | null> {
        await this.repository.update(id, data);
        return this.findById(id);
    }

    async delete(id: string): Promise<boolean> {
        const result = await this.repository.delete(id);
        return (result.affected ?? 0) > 0;
    }

    async save(entity: DeepPartial<T>): Promise<T> {
        const saved = await this.repository.save(entity);
        return Array.isArray(saved) ? saved[0] : saved;
    }
}