import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAnalyticsTables1712261992 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE study_events (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                user_id VARCHAR NOT NULL,
                event_type VARCHAR NOT NULL,
                data JSONB NOT NULL,
                timestamp TIMESTAMP NOT NULL,
                created_at TIMESTAMP NOT NULL DEFAULT now(),
                updated_at TIMESTAMP NOT NULL DEFAULT now()
            );

            CREATE INDEX idx_study_events_user_id ON study_events(user_id);
            CREATE INDEX idx_study_events_timestamp ON study_events(timestamp);

            CREATE TABLE quiz_attempts (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                user_id VARCHAR NOT NULL,
                quiz_id VARCHAR NOT NULL,
                score FLOAT NOT NULL,
                responses JSONB NOT NULL,
                metadata JSONB NOT NULL,
                created_at TIMESTAMP NOT NULL DEFAULT now(),
                updated_at TIMESTAMP NOT NULL DEFAULT now()
            );

            CREATE INDEX idx_quiz_attempts_user_id ON quiz_attempts(user_id);
            CREATE INDEX idx_quiz_attempts_quiz_id ON quiz_attempts(quiz_id);
            CREATE INDEX idx_quiz_attempts_created_at ON quiz_attempts(created_at);
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP TABLE IF EXISTS study_events;
            DROP TABLE IF EXISTS quiz_attempts;
        `);
    }
} 