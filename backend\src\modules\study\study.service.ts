import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { Topic } from '../../entities/topic.entity';
import { TopicProgress } from '../../entities/topic-progress.entity';
import { StudySession } from '../../entities/study-session.entity';
import { User } from '../../entities/user.entity';

@Injectable()
export class StudyService {
    constructor(
        @InjectRepository(Topic)
        private topicRepository: Repository<Topic>,
        @InjectRepository(TopicProgress)
        private topicProgressRepository: Repository<TopicProgress>,
        @InjectRepository(StudySession)
        private studySessionRepository: Repository<StudySession>,
    ) {}

    async getTopicsByCategory(category: string): Promise<Topic[]> {
        return this.topicRepository.find({
            where: { category },
            relations: ['units', 'units.materials']
        });
    }

    async getTopicProgress(userId: string, topicId: string): Promise<TopicProgress | null> {
        return this.topicProgressRepository.findOne({
            where: { user: { id: userId }, topic: { id: topicId } },
            relations: ['topic']
        });
    }

    async startStudySession(userId: string, topicId: string): Promise<StudySession> {
        const session = this.studySessionRepository.create({
            user: { id: userId },
            topic: { id: topicId },
            started_at: new Date(),
            duration_minutes: 0,
            focus_score: 0,
            activities: []
        });
        return this.studySessionRepository.save(session);
    }

    async endStudySession(sessionId: string, activities: any[]): Promise<StudySession> {
        const session = await this.studySessionRepository.findOne({
            where: { id: sessionId },
            relations: ['user', 'topic']
        });

        if (!session) {
            throw new Error('Study session not found');
        }

        session.ended_at = new Date();
        session.duration_minutes = Math.round(
            (session.ended_at.getTime() - session.started_at.getTime()) / (1000 * 60)
        );
        session.activities = activities;

        // Calculate focus score based on activity consistency
        session.focus_score = this.calculateFocusScore(activities);

        // Update topic progress
        await this.updateTopicProgress(session);

        return this.studySessionRepository.save(session);
    }

    private calculateFocusScore(activities: any[]): number {
        if (!activities.length) return 0;
        
        // Simple focus score based on activity consistency
        const totalDuration = activities.reduce((sum, act) => sum + act.durationMinutes, 0);
        const maxDuration = Math.max(...activities.map(act => act.durationMinutes));
        return maxDuration / totalDuration;
    }

    private async updateTopicProgress(session: StudySession): Promise<void> {
        let progress = await this.topicProgressRepository.findOne({
            where: {
                user: { id: session.user.id },
                topic: { id: session.topic.id }
            }
        });

        if (!progress) {
            progress = this.topicProgressRepository.create({
                user: { id: session.user.id },
                topic: { id: session.topic.id },
                time_spent_minutes: 0,
                completion_percentage: 0,
                streak_days: 0,
                last_studied_at: new Date()
            });
        }

        // Update progress metrics
        progress.time_spent_minutes += session.duration_minutes;
        progress.last_studied_at = new Date();

        // Calculate completion percentage based on activities
        const quiz_scores = session.activities
            .filter(act => act.type === 'quiz')
            .map(act => ({ 
                type: act.type,
                duration_minutes: act.duration_minutes,
                score: act.score || 0,
                date: new Date() 
            }));

        if (quiz_scores.length > 0) {
            progress.quiz_scores = [...(progress.quiz_scores || []), ...quiz_scores.map(score => ({
                quiz_id: score.type,
                score: score.score,
                date: score.date
            }))];
            progress.completion_percentage = Math.min(
                100,
                progress.completion_percentage + (quiz_scores.length * 10)
            );
        }

        // Update streak
        const last_studied_at = progress.last_studied_at;
        const today = new Date();
        if (last_studied_at && this.isConsecutiveDay(last_studied_at, today)) {
            progress.streak_days = (progress.streak_days || 0) + 1;
        }

        await this.topicProgressRepository.save(progress);
    }

    private isConsecutiveDay(lastDate: Date, currentDate: Date): boolean {
        const diffTime = Math.abs(currentDate.getTime() - lastDate.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays === 1;
    }

    async getStudyStats(userId: string): Promise<any> {
        const [totalTime, totalSessions, streakDays] = await Promise.all([
            this.studySessionRepository
                .createQueryBuilder('session')
                .where('session.user.id = :userId', { userId })
                .select('SUM(session.durationMinutes)', 'total')
                .getRawOne(),
            this.studySessionRepository.count({ where: { user: { id: userId } } }),
            this.topicProgressRepository
                .createQueryBuilder('progress')
                .where('progress.user.id = :userId', { userId })
                .select('MAX(progress.streakDays)', 'maxStreak')
                .getRawOne()
        ]);

        return {
            totalStudyTime: totalTime?.total || 0,
            totalSessions,
            currentStreak: streakDays?.maxStreak || 0
        };
    }

    async trackActivity(user: User, activity: { type: string; duration_minutes: number; score?: number }): Promise<void> {
        const progress = this.topicProgressRepository.create({
            user: { id: user.id },
            topic: { id: activity.type },
            completion_percentage: 0,
            time_spent_minutes: activity.duration_minutes,
            is_completed: false,
            streak_days: 0
        });
        await this.topicProgressRepository.save(progress);
    }
} 