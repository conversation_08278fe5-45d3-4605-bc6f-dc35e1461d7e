"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StudyController = void 0;
const common_1 = require("@nestjs/common");
const study_service_1 = require("./study.service");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const roles_guards_1 = require("../../common/guards/roles.guards");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
const user_entity_1 = require("../../entities/user.entity");
let StudyController = class StudyController {
    constructor(studyService) {
        this.studyService = studyService;
    }
    async getTopicsByCategory(category) {
        return this.studyService.getTopicsByCategory(category);
    }
    async getTopicProgress(user, topicId) {
        return this.studyService.getTopicProgress(user.id, topicId);
    }
    async startStudySession(user, topicId) {
        return this.studyService.startStudySession(user.id, topicId);
    }
    async endStudySession(sessionId, activities) {
        return this.studyService.endStudySession(sessionId, activities);
    }
    async getStudyStats(user) {
        return this.studyService.getStudyStats(user.id);
    }
};
exports.StudyController = StudyController;
__decorate([
    (0, common_1.Get)('topics/:category'),
    (0, roles_decorator_1.Roles)('student'),
    __param(0, (0, common_1.Param)('category')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], StudyController.prototype, "getTopicsByCategory", null);
__decorate([
    (0, common_1.Get)('progress/:topicId'),
    (0, roles_decorator_1.Roles)('student'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('topicId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, String]),
    __metadata("design:returntype", Promise)
], StudyController.prototype, "getTopicProgress", null);
__decorate([
    (0, common_1.Post)('session/start'),
    (0, roles_decorator_1.Roles)('student'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)('topicId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, String]),
    __metadata("design:returntype", Promise)
], StudyController.prototype, "startStudySession", null);
__decorate([
    (0, common_1.Put)('session/:sessionId/end'),
    (0, roles_decorator_1.Roles)('student'),
    __param(0, (0, common_1.Param)('sessionId')),
    __param(1, (0, common_1.Body)('activities')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Array]),
    __metadata("design:returntype", Promise)
], StudyController.prototype, "endStudySession", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, roles_decorator_1.Roles)('student'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User]),
    __metadata("design:returntype", Promise)
], StudyController.prototype, "getStudyStats", null);
exports.StudyController = StudyController = __decorate([
    (0, common_1.Controller)('study'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guards_1.RolesGuard),
    __metadata("design:paramtypes", [study_service_1.StudyService])
], StudyController);
//# sourceMappingURL=study.controller.js.map