import { Topic } from '../entities/topic.entity';

export const kenyanMedicalTopics: Partial<Topic>[] = [
    // Anatomy
    {
        title: 'Gross Anatomy of Upper Limb',
        description: 'Comprehensive study of upper limb anatomy including bones, muscles, nerves, and blood supply',
        category: 'Anatomy',
        difficulty: 3,
        estimatedHours: 20,
        prerequisites: [],
        resources: [
            {
                type: 'pdf',
                url: '/resources/anatomy/upper-limb.pdf',
                title: 'Upper Limb Anatomy Guide'
            },
            {
                type: 'quiz',
                url: '/quizzes/anatomy/upper-limb',
                title: 'Upper Limb Quiz'
            }
        ]
    },
    {
        title: 'Head and Neck Anatomy',
        description: 'Detailed study of head and neck structures including cranial nerves and blood supply',
        category: 'Anatomy',
        difficulty: 4,
        estimatedHours: 25,
        prerequisites: ['Gross Anatomy of Upper Limb'],
        resources: [
            {
                type: 'pdf',
                url: '/resources/anatomy/head-neck.pdf',
                title: 'Head and Neck Guide'
            }
        ]
    },

    // Physiology
    {
        title: 'Cardiovascular Physiology',
        description: 'Study of heart function, blood pressure regulation, and circulatory system',
        category: 'Physiology',
        difficulty: 4,
        estimatedHours: 30,
        prerequisites: [],
        resources: [
            {
                type: 'pdf',
                url: '/resources/physiology/cardio.pdf',
                title: 'Cardiovascular System Guide'
            },
            {
                type: 'video',
                url: '/videos/physiology/cardio',
                title: 'Cardiac Cycle Animation'
            }
        ]
    },

    // Pathology
    {
        title: 'Infectious Diseases in Kenya',
        description: 'Study of common infectious diseases in Kenya including malaria, TB, and HIV',
        category: 'Pathology',
        difficulty: 3,
        estimatedHours: 25,
        prerequisites: [],
        resources: [
            {
                type: 'pdf',
                url: '/resources/pathology/kenya-infections.pdf',
                title: 'Kenyan Infectious Diseases Guide'
            },
            {
                type: 'quiz',
                url: '/quizzes/pathology/infections',
                title: 'Infectious Diseases Quiz'
            }
        ]
    },

    // Clinical Medicine
    {
        title: 'Emergency Medicine in Resource-Limited Settings',
        description: 'Practical approach to emergency care in Kenyan healthcare settings',
        category: 'Clinical Medicine',
        difficulty: 4,
        estimatedHours: 35,
        prerequisites: ['Infectious Diseases in Kenya'],
        resources: [
            {
                type: 'pdf',
                url: '/resources/clinical/emergency-care.pdf',
                title: 'Emergency Care Guide'
            },
            {
                type: 'video',
                url: '/videos/clinical/emergency',
                title: 'Emergency Procedures'
            }
        ]
    },

    // Pharmacology
    {
        title: 'Essential Medicines in Kenya',
        description: 'Study of commonly used medications in Kenyan healthcare system',
        category: 'Pharmacology',
        difficulty: 3,
        estimatedHours: 20,
        prerequisites: [],
        resources: [
            {
                type: 'pdf',
                url: '/resources/pharmacology/essential-meds.pdf',
                title: 'Essential Medicines Guide'
            },
            {
                type: 'quiz',
                url: '/quizzes/pharmacology/medications',
                title: 'Medications Quiz'
            }
        ]
    }
];