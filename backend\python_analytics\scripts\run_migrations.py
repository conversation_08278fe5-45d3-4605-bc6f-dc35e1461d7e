import os
import sys
from pathlib import Path
from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# Add the parent directory to the Python path
sys.path.append(str(Path(__file__).parent.parent))

# Load environment variables
load_dotenv()

def run_migrations():
    """Run all database migrations"""
    try:
        # Get database URL from environment
        db_url = os.getenv("DATABASE_URL")
        if not db_url:
            raise ValueError("DATABASE_URL environment variable is not set")
        
        # Create database engine
        engine = create_engine(db_url)
        
        # Get migrations directory
        migrations_dir = Path(__file__).parent.parent / "migrations"
        
        # Get all SQL files in migrations directory
        migration_files = sorted(migrations_dir.glob("*.sql"))
        
        # Run each migration
        with engine.connect() as conn:
            for migration_file in migration_files:
                print(f"Running migration: {migration_file.name}")
                
                # Read and execute migration
                with open(migration_file, "r") as f:
                    migration_sql = f.read()
                    conn.execute(text(migration_sql))
                
                print(f"Successfully ran migration: {migration_file.name}")
        
        print("All migrations completed successfully")
        
    except Exception as e:
        print(f"Error running migrations: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    run_migrations() 