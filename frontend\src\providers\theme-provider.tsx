import * as React from "react"
import { ThemeProvider as NextThemesProvider } from "next-themes"
import { type ThemeProviderProps } from "next-themes/dist/types"
import { useStore } from "@/store"

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  const { theme, setTheme } = useStore()

  React.useEffect(() => {
    // Sync theme with system preference on mount
    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    if (!theme) {
      setTheme(systemTheme)
    }
  }, [])

  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme={theme || "system"}
      enableSystem
      disableTransitionOnChange
      {...props}
    >
      {children}
    </NextThemesProvider>
  )
} 