import { MigrationInterface, QueryRunner, Table, TableForeignKey } from 'typeorm';

export class CreatePermissionsAndUpdateRoles1710000000000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create permissions table
        await queryRunner.createTable(
            new Table({
                name: 'permissions',
                columns: [
                    {
                        name: 'id',
                        type: 'uuid',
                        isPrimary: true,
                        generationStrategy: 'uuid',
                        default: 'uuid_generate_v4()',
                    },
                    {
                        name: 'name',
                        type: 'varchar',
                        isUnique: true,
                    },
                    {
                        name: 'description',
                        type: 'varchar',
                        isNullable: true,
                    },
                    {
                        name: 'is_active',
                        type: 'boolean',
                        default: true,
                    },
                    {
                        name: 'category',
                        type: 'varchar',
                        isNullable: true,
                    },
                    {
                        name: 'created_at',
                        type: 'timestamp',
                        default: 'CURRENT_TIMESTAMP',
                    },
                    {
                        name: 'updated_at',
                        type: 'timestamp',
                        default: 'CURRENT_TIMESTAMP',
                    },
                ],
            }),
            true
        );

        // Add new columns to roles table
        await queryRunner.query(`
            ALTER TABLE roles
            ADD COLUMN IF NOT EXISTS color varchar,
            ADD COLUMN IF NOT EXISTS hierarchy_level integer,
            ADD COLUMN IF NOT EXISTS metadata jsonb;
        `);

        // Create role_permissions table for many-to-many relationship
        await queryRunner.createTable(
            new Table({
                name: 'role_permissions',
                columns: [
                    {
                        name: 'role_id',
                        type: 'uuid',
                    },
                    {
                        name: 'permission_id',
                        type: 'uuid',
                    },
                ],
            }),
            true
        );

        // Add foreign keys for role_permissions
        await queryRunner.createForeignKey(
            'role_permissions',
            new TableForeignKey({
                columnNames: ['role_id'],
                referencedColumnNames: ['id'],
                referencedTableName: 'roles',
                onDelete: 'CASCADE',
            })
        );

        await queryRunner.createForeignKey(
            'role_permissions',
            new TableForeignKey({
                columnNames: ['permission_id'],
                referencedColumnNames: ['id'],
                referencedTableName: 'permissions',
                onDelete: 'CASCADE',
            })
        );

        // Add unique constraint for role_permissions
        await queryRunner.query(`
            ALTER TABLE role_permissions
            ADD CONSTRAINT UQ_role_permissions UNIQUE (role_id, permission_id);
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove foreign keys
        const rolePermissionsTable = await queryRunner.getTable('role_permissions');
        if (!rolePermissionsTable) {
            return;
        }

        const roleForeignKey = rolePermissionsTable.foreignKeys.find(
            fk => fk.columnNames.indexOf('role_id') !== -1
        );
        const permissionForeignKey = rolePermissionsTable.foreignKeys.find(
            fk => fk.columnNames.indexOf('permission_id') !== -1
        );

        if (roleForeignKey) {
            await queryRunner.dropForeignKey('role_permissions', roleForeignKey);
        }
        if (permissionForeignKey) {
            await queryRunner.dropForeignKey('role_permissions', permissionForeignKey);
        }

        // Drop role_permissions table
        await queryRunner.dropTable('role_permissions');

        // Remove new columns from roles table
        await queryRunner.query(`
            ALTER TABLE roles
            DROP COLUMN IF EXISTS color,
            DROP COLUMN IF EXISTS hierarchy_level,
            DROP COLUMN IF EXISTS metadata;
        `);

        // Drop permissions table
        await queryRunner.dropTable('permissions');
    }
}