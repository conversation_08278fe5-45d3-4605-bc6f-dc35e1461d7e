# Monitor production deployment script
$ErrorActionPreference = "Stop"

function Get-ServiceHealth {
    param (
        [string]$serviceName,
        [string]$healthEndpoint
    )
    try {
        $response = Invoke-RestMethod -Uri $healthEndpoint -Method Get
        if ($response.status -eq "ok") {
            Write-Host "✅ $serviceName is healthy" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $serviceName health check failed: $($response.status)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ $serviceName is not responding: $_" -ForegroundColor Red
        return $false
    }
}

function Get-MetricsData {
    param (
        [string]$serviceName,
        [string]$metricsEndpoint
    )
    try {
        $metrics = Invoke-RestMethod -Uri $metricsEndpoint -Method Get
        Write-Host "`n📊 $serviceName Metrics:"
        Write-Host "CPU Usage: $($metrics.cpu)%"
        Write-Host "Memory Usage: $($metrics.memory)MB"
        Write-Host "Request Rate: $($metrics.requestRate)/sec"
        Write-Host "Error Rate: $($metrics.errorRate)%"
    } catch {
        Write-Host "⚠️ Failed to fetch metrics for $serviceName: $_" -ForegroundColor Yellow
    }
}

# Configuration
$config = @{
    api = @{
        name = "MedTrack API"
        healthEndpoint = "http://localhost:3002/api/health"
        metricsEndpoint = "http://localhost:3002/api/metrics"
    }
    analytics = @{
        name = "Analytics Service"
        healthEndpoint = "http://localhost:5000/health"
        metricsEndpoint = "http://localhost:5000/metrics"
    }
}

Write-Host "🔍 Starting Production Monitoring..." -ForegroundColor Cyan
Write-Host "Press Ctrl+C to stop monitoring`n"

while ($true) {
    $date = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "`n=== Monitoring Check at $date ===" -ForegroundColor Yellow

    # Check each service
    foreach ($service in $config.GetEnumerator()) {
        Write-Host "`n🔍 Checking $($service.Value.name)..."
        
        # Health check
        $isHealthy = Get-ServiceHealth -serviceName $service.Value.name -healthEndpoint $service.Value.healthEndpoint
        
        if ($isHealthy) {
            # Get metrics if healthy
            Get-MetricsData -serviceName $service.Value.name -metricsEndpoint $service.Value.metricsEndpoint
        }
    }

    # Check database connections
    Write-Host "`n🗄️ Checking Database Status..."
    try {
        $env:PGPASSWORD = [Environment]::GetEnvironmentVariable("POSTGRES_PASSWORD")
        $result = psql -h $env:POSTGRES_HOST -p $env:POSTGRES_PORT -U $env:POSTGRES_USER -d $env:POSTGRES_DB -c "SELECT COUNT(*) FROM pg_stat_activity;"
        Write-Host "✅ Database connections: $result" -ForegroundColor Green
    } catch {
        Write-Host "❌ Database check failed: $_" -ForegroundColor Red
    }

    # Check Redis status
    Write-Host "`n📎 Checking Redis Status..."
    try {
        $result = redis-cli -h $env:REDIS_HOST -a $env:REDIS_PASSWORD info | Select-String "connected_clients"
        Write-Host "✅ Redis: $result" -ForegroundColor Green
    } catch {
        Write-Host "❌ Redis check failed: $_" -ForegroundColor Red
    }

    # Wait before next check
    Start-Sleep -Seconds 30
}
