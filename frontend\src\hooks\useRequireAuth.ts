'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface UseRequireAuthOptions {
  redirectTo?: string;
  requiredRole?: string | string[];
}

export function useRequireAuth(options: UseRequireAuthOptions = {}) {
  const { redirectTo = '/auth/login', requiredRole } = options;
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (status === 'loading') {
      return;
    }

    // Not authenticated
    if (status === 'unauthenticated') {
      router.push(`${redirectTo}?from=${encodeURIComponent(window.location.pathname)}`);
      return;
    }

    // Check for required role
    if (requiredRole && session) {
      const userRole = session.user?.role;
      
      if (Array.isArray(requiredRole)) {
        // Check if user has any of the required roles
        if (!userRole || !requiredRole.includes(userRole)) {
          router.push('/dashboard');
          return;
        }
      } else {
        // Check if user has the required role
        if (!userRole || userRole !== requiredRole) {
          router.push('/dashboard');
          return;
        }
      }
    }

    setIsAuthorized(true);
    setIsLoading(false);
  }, [status, session, router, redirectTo, requiredRole]);

  return { isAuthorized, isLoading, session };
}

export default useRequireAuth;
