export interface User {
  id: string;
  name: string;
  email: string;
  role: 'student' | 'teacher' | 'admin';
  profilePicture?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Course {
  id: string;
  title: string;
  description: string;
  progress: number;
  duration: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced';
  rating: number;
  enrolled: number;
  category: string;
  instructor: User;
  modules: Module[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Module {
  id: string;
  title: string;
  description: string;
  duration: string;
  order: number;
  lessons: Lesson[];
  quiz?: Quiz;
}

export interface Lesson {
  id: string;
  title: string;
  content: string;
  duration: string;
  order: number;
  materials: Material[];
}

export interface Quiz {
  id: string;
  title: string;
  description: string;
  questions: Question[];
  timeLimit?: number;
  passingScore: number;
  attempts: number;
}

export interface Question {
  id: string;
  text: string;
  type: 'multiple-choice' | 'true-false' | 'short-answer';
  options?: string[];
  correctAnswer: string | string[];
  points: number;
}

export interface Material {
  id: string;
  title: string;
  type: 'pdf' | 'video' | 'link' | 'document';
  url: string;
  size?: number;
  downloads: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Progress {
  userId: string;
  courseId: string;
  completedLessons: string[];
  quizScores: QuizScore[];
  lastAccessed: Date;
  totalTimeSpent: number;
}

export interface QuizScore {
  quizId: string;
  score: number;
  attempts: number;
  lastAttempt: Date;
}

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  read: boolean;
  createdAt: Date;
  action?: {
    type: string;
    url: string;
  };
}

export interface Schedule {
  id: string;
  userId: string;
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  type: 'class' | 'study' | 'exam' | 'other';
  recurring?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    endDate?: Date;
  };
  location?: string;
  attendees?: string[];
}

export interface Analytics {
  userId: string;
  courseProgress: {
    courseId: string;
    progress: number;
    timeSpent: number;
    lastAccessed: Date;
  }[];
  quizPerformance: {
    quizId: string;
    averageScore: number;
    attempts: number;
  }[];
  studyStreak: number;
  weeklyGoals: {
    week: string;
    goal: number;
    achieved: number;
  }[];
}

export interface UserSettings {
  userId: string;
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: {
    email: boolean;
    push: boolean;
    inApp: boolean;
  };
  privacy: {
    profileVisibility: 'public' | 'private' | 'connections';
    showProgress: boolean;
    showActivity: boolean;
  };
  accessibility: {
    fontSize: number;
    highContrast: boolean;
    screenReader: boolean;
  };
} 