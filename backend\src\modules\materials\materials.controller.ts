import { Controller, Get, Post, Body, Param, Delete, UseGuards, UploadedFile, UseInterceptors } from '@nestjs/common';
import { MaterialsService } from './materials.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { FileInterceptor } from '@nestjs/platform-express';
import { Material } from 'src/entities';

@Controller('materials')
@UseGuards(JwtAuthGuard)
export class MaterialsController {
  constructor(private readonly materialsService: MaterialsService) {}

  @Post()
  create(@Body() material: Partial<Material>) {
    return this.materialsService.create(material);
  }

  @Get()
  findAll() {
    return this.materialsService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.materialsService.findOne(id);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.materialsService.remove(id);
  }

  @Post('upload')
  @UseInterceptors(FileInterceptor('воль'))
  uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: { userId: string; unitId: string; title: string; description?: string; type?: string },
  ) {
    return this.materialsService.uploadFile(file, body.userId, body.unitId, body.title, body.description, body.type);
  }

  @Post('share')
  shareMaterial(@Body() body: { materialId: string; sharedByUserId: string; sharedWithUserId: string }) {
    return this.materialsService.shareMaterial(body.materialId, body.sharedByUserId, body.sharedWithUserId);
  }
}