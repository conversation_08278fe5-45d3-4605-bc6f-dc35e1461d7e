import { Controller, Get, Post, Body, Param, UseGuards } from '@nestjs/common';
import { FlashcardsService } from './flashcards.service';
import { JwtAuthGuard } from '../modules/auth/jwt-auth.guard';

@Controller('flashcards')
@UseGuards(JwtAuthGuard)
export class FlashcardsController {
    constructor(private readonly flashcardsService: FlashcardsService) {}

    @Post('create')
    async createFlashcard(
        @Body('userId') userId: string,
        @Body('questionId') questionId: string,
    ) {
        return this.flashcardsService.createFlashcard(userId, questionId);
    }

    @Get('due/:userId')
    async getDueCards(@Param('userId') userId: string) {
        return this.flashcardsService.getDueCards(userId);
    }

    @Post('update/:cardId')
    async updateCard(
        @Param('cardId') cardId: string,
        @Body('quality') quality: number,
    ) {
        return this.flashcardsService.updateCard(cardId, quality);
    }

    @Get('stats/:userId')
    async getCardStats(@Param('userId') userId: string) {
        return this.flashcardsService.getCardStats(userId);
    }

    @Post('sync/:userId')
    async syncCards(
        @Param('userId') userId: string,
        @Body('cards') cards: any[],
    ) {
        return this.flashcardsService.syncCards(userId, cards);
    }
} 