import { MigrationInterface, QueryRunner } from "typeorm";

export class AddEmailVerification1716398784000 implements MigrationInterface {
    name = 'AddEmailVerification1716398784000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" ADD COLUMN "email_verification_token" varchar NULL`);
        await queryRunner.query(`ALTER TABLE "users" ADD COLUMN "email_verification_expires" timestamp NULL`);
        await queryRunner.query(`ALTER TABLE "users" ADD COLUMN "email_verified" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_users_email_verification_token" ON "users" ("email_verification_token") WHERE "email_verification_token" IS NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_users_email_verification_token"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "email_verified"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "email_verification_expires"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "email_verification_token"`);
    }
}
