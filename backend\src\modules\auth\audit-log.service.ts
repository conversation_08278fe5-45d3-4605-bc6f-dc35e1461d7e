import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../entities/user.entity';

export enum AuditEventType {
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILURE = 'LOGIN_FAILURE',
  LOGOUT = 'LOGOUT',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',
  PASSWORD_RESET_REQUEST = 'PASSWORD_RESET_REQUEST',
  PASSWORD_RESET_COMPLETE = 'PASSWORD_RESET_COMPLETE',
  USER_CREATED = 'USER_CREATED',
  USER_UPDATED = 'USER_UPDATED',
  USER_DELETED = 'USER_DELETED',
  ROLE_CHANGED = 'ROLE_CHANGED',
  TOKEN_REFRESH = 'TOKEN_REFRESH',
  TOKEN_REVOKED = 'TOKEN_REVOKED',
}

export interface AuditLogEntry {
  id: string;
  eventType: AuditEventType;
  userId: string;
  userEmail: string;
  ipAddress: string;
  userAgent: string;
  eventData?: any;
  timestamp: Date;
}

@Injectable()
export class AuditLogService {
  private readonly logger = new Logger('AuditLog');

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async log(
    eventType: AuditEventType,
    userId: string,
    ipAddress: string,
    userAgent: string,
    eventData?: any,
  ): Promise<void> {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      const entry: AuditLogEntry = {
        id: crypto.randomUUID(),
        eventType,
        userId,
        userEmail: user?.email || 'unknown',
        ipAddress,
        userAgent,
        eventData,
        timestamp: new Date(),
      };

      // Log to application logs
      this.logger.log(JSON.stringify(entry));

      // In a production environment, you might want to:
      // 1. Store in database
      // 2. Send to external logging service
      // 3. Emit events for real-time monitoring
      
      if (this.isSecurityCriticalEvent(eventType)) {
        this.handleSecurityCriticalEvent(entry);
      }
    } catch (error) {
      this.logger.error(`Failed to log audit event: ${error.message}`, error.stack);
    }
  }

  private isSecurityCriticalEvent(eventType: AuditEventType): boolean {
    return [
      AuditEventType.LOGIN_FAILURE,
      AuditEventType.PASSWORD_RESET_REQUEST,
      AuditEventType.ROLE_CHANGED,
      AuditEventType.USER_DELETED,
    ].includes(eventType);
  }

  private handleSecurityCriticalEvent(entry: AuditLogEntry): void {
    // Handle security critical events differently
    // For example:
    // 1. Send immediate notifications
    // 2. Trigger additional security measures
    // 3. Store in a separate security audit log
    this.logger.warn(`Security critical event: ${JSON.stringify(entry)}`);
  }

  async getRecentFailedLogins(userId: string, minutes: number = 15): Promise<number> {
    // In a real implementation, this would query the database
    // For now, we'll just return 0
    return 0;
  }
}
