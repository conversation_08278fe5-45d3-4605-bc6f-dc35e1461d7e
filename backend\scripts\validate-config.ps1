# Check required environment variables
$requiredEnvVars = @(
    "OCI_VAULT_JWT_SECRET",
    "OCI_VAULT_REFRESH_TOKEN_SECRET",
    "OCI_DB_HOST",
    "OCI_DB_USER",
    "OCI_DB_PASSWORD",
    "OCI_REDIS_HOST",
    "OCI_REDIS_PASSWORD",
    "FRONTEND_DOMAIN"
)

Write-Host "Checking environment variables..." -ForegroundColor Cyan
$missingVars = @()
foreach ($var in $requiredEnvVars) {
    if (-not (Get-ChildItem env: | Where-Object { $_.Name -eq $var })) {
        $missingVars += $var
    }
}

if ($missingVars.Count -gt 0) {
    Write-Host "❌ Missing required environment variables:" -ForegroundColor Red
    $missingVars | ForEach-Object { Write-Host "   - $_" -ForegroundColor Red }
    exit 1
}
Write-Host "✅ All required environment variables are set" -ForegroundColor Green

# Validate Docker configuration
Write-Host "`nValidating Docker configuration..." -ForegroundColor Cyan
docker-compose -f docker-compose.prod.yml config
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Docker Compose configuration is valid" -ForegroundColor Green
} else {
    Write-Host "❌ Docker Compose configuration is invalid" -ForegroundColor Red
    exit 1
}

# Check network connectivity
Write-Host "`nChecking network connectivity..." -ForegroundColor Cyan
$testPorts = @(
    @{Host=$env:OCI_DB_HOST; Port="5432"; Service="PostgreSQL"},
    @{Host=$env:OCI_REDIS_HOST; Port="6379"; Service="Redis"}
)

foreach ($test in $testPorts) {
    $result = Test-NetConnection -ComputerName $test.Host -Port $test.Port -WarningAction SilentlyContinue
    if ($result.TcpTestSucceeded) {
        Write-Host "✅ Successfully connected to $($test.Service) at $($test.Host):$($test.Port)" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to connect to $($test.Service) at $($test.Host):$($test.Port)" -ForegroundColor Red
    }
}

# Check SSL certificates if applicable
Write-Host "`nChecking SSL certificates..." -ForegroundColor Cyan
$frontendDomain = $env:FRONTEND_DOMAIN -replace "https://", ""
try {
    $cert = Invoke-WebRequest -Uri "https://$frontendDomain" -Method Head
    Write-Host "✅ SSL certificate for frontend domain is valid" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Could not validate SSL certificate for frontend domain" -ForegroundColor Yellow
}

Write-Host "`nConfiguration validation complete!" -ForegroundColor Green
