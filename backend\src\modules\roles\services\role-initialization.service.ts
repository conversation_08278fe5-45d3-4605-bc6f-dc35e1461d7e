import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Role } from '../../../entities/role.entity';
import { Permission } from '../../../entities/permission.entity';

@Injectable()
export class RoleInitializationService {
    constructor(
        @InjectRepository(Role)
        private roleRepository: Repository<Role>,
        @InjectRepository(Permission)
        private permissionRepository: Repository<Permission>
    ) {}

    async initializeDefaultRoles() {
        // First, create default permissions
        const permissions = await this.createDefaultPermissions();

        // Then create roles with their permissions
        const roles = [
            {
                name: 'Administrator',
                description: 'Full access to all platform features and settings',
                color: 'purple',
                hierarchy_level: 1,
                is_active: true,
                permissions: permissions.filter(p => 
                    ['manage_users', 'manage_content', 'manage_courses', 'view_analytics', 
                     'manage_settings', 'manage_roles'].includes(p.name)
                )
            },
            {
                name: 'Medical Instructor',
                description: 'Can create and manage courses, content, and student progress',
                color: 'blue',
                hierarchy_level: 2,
                is_active: true,
                permissions: permissions.filter(p => 
                    ['create_content', 'manage_courses', 'view_analytics', 'manage_students'].includes(p.name)
                )
            },
            {
                name: 'Medical Resident',
                description: 'Advanced medical student with teaching capabilities',
                color: 'green',
                hierarchy_level: 3,
                is_active: true,
                permissions: permissions.filter(p => 
                    ['access_courses', 'create_content', 'manage_study_groups', 'view_analytics'].includes(p.name)
                )
            },
            {
                name: 'Medical Student',
                description: 'Basic access to courses and study materials',
                color: 'yellow',
                hierarchy_level: 4,
                is_active: true,
                permissions: permissions.filter(p => 
                    ['access_courses', 'join_study_groups', 'view_progress'].includes(p.name)
                )
            },
            {
                name: 'Attending Physician',
                description: 'Senior medical professional with teaching and content creation rights',
                color: 'red',
                hierarchy_level: 2,
                is_active: true,
                permissions: permissions.filter(p => 
                    ['create_content', 'manage_courses', 'manage_students', 'view_analytics', 'approve_content'].includes(p.name)
                )
            }
        ];

        for (const roleData of roles) {
            const existingRole = await this.roleRepository.findOne({
                where: { name: roleData.name }
            });

            if (!existingRole) {
                const role = this.roleRepository.create(roleData);
                await this.roleRepository.save(role);
            }
        }
    }

    private async createDefaultPermissions(): Promise<Permission[]> {
        const defaultPermissions = [
            {
                name: 'manage_users',
                description: 'Create, edit, and delete users',
                category: 'users'
            },
            {
                name: 'manage_content',
                description: 'Create and edit course content',
                category: 'content'
            },
            {
                name: 'manage_courses',
                description: 'Create and manage courses',
                category: 'courses'
            },
            {
                name: 'view_analytics',
                description: 'Access to analytics and reports',
                category: 'analytics'
            },
            {
                name: 'manage_settings',
                description: 'Modify system settings',
                category: 'settings'
            },
            {
                name: 'manage_roles',
                description: 'Create and modify user roles',
                category: 'roles'
            },
            {
                name: 'create_content',
                description: 'Create new content',
                category: 'content'
            },
            {
                name: 'manage_students',
                description: 'Manage student accounts and progress',
                category: 'users'
            },
            {
                name: 'access_courses',
                description: 'Access course materials',
                category: 'courses'
            },
            {
                name: 'manage_study_groups',
                description: 'Create and manage study groups',
                category: 'groups'
            },
            {
                name: 'join_study_groups',
                description: 'Join existing study groups',
                category: 'groups'
            },
            {
                name: 'view_progress',
                description: 'View personal progress',
                category: 'analytics'
            },
            {
                name: 'approve_content',
                description: 'Approve user-created content',
                category: 'content'
            }
        ];

        const permissions: Permission[] = [];

        for (const permData of defaultPermissions) {
            const existingPerm = await this.permissionRepository.findOne({
                where: { name: permData.name }
            });

            if (!existingPerm) {
                const permission = this.permissionRepository.create(permData);
                permissions.push(await this.permissionRepository.save(permission));
            } else {
                permissions.push(existingPerm);
            }
        }

        return permissions;
    }
} 