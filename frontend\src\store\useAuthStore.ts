// store/useAuthStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import api from '../services/api';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  year?: string;
  specialization?: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: any) => Promise<void>;
  logout: () => void;
  clearError: () => void;
  updateUser: (userData: Partial<User>) => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      
      login: async (email: string, password: string) => {
        try {
          set({ isLoading: true, error: null });
          const { data } = await api.post('/auth/login', { email, password });
          set({ 
            user: data.user,
            token: data.accessToken,
            isAuthenticated: true,
            isLoading: false
          });
          // Set token for future requests
          api.defaults.headers.common['Authorization'] = `Bearer ${data.accessToken}`;
        } catch (error: any) {
          set({ 
            error: error.response?.data?.message || 'Login failed',
            isLoading: false 
          });
        }
      },
      
      register: async (userData) => {
        try {
          set({ isLoading: true, error: null });
          const { data } = await api.post('/auth/register', userData);
          set({ 
            user: data.user,
            token: data.accessToken,
            isAuthenticated: true,
            isLoading: false
          });
          api.defaults.headers.common['Authorization'] = `Bearer ${data.accessToken}`;
        } catch (error: any) {
          set({ 
            error: error.response?.data?.message || 'Registration failed',
            isLoading: false 
          });
        }
      },
      
      logout: () => {
        set({ 
          user: null,
          token: null,
          isAuthenticated: false 
        });
        delete api.defaults.headers.common['Authorization'];
      },
      
      clearError: () => {
        set({ error: null });
      },
      
      updateUser: async (userData) => {
        try {
          set({ isLoading: true, error: null });
          const { data } = await api.patch(`/users/${get().user?.id}`, userData);
          set({ 
            user: { ...get().user, ...data },
            isLoading: false 
          });
        } catch (error: any) {
          set({ 
            error: error.response?.data?.message || 'Update failed',
            isLoading: false 
          });
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ user: state.user, token: state.token, isAuthenticated: state.isAuthenticated }),
    }
  )
);
