import { <PERSON>, Post, Put, Param, Body, UseGuards, Request } from '@nestjs/common';
import { CPDService } from './cpd.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guards';
import { Roles } from '../../common/decorators/roles.decorator';
import { RequestWithUser } from '../../common/interfaces/request-with-user.interface';

@Controller('cpd')
@UseGuards(JwtAuthGuard, RolesGuard)
export class CPDController {
    constructor(private readonly cpdService: CPDService) {}

    @Put('activities/:id/verify')
    @Roles('admin')
    async verifyActivity(
        @Param('id') activityId: string,
        @Body() data: { verified: boolean; notes?: string },
    ) {
        return this.cpdService.verifyCPDActivity(activityId, data.verified, data.notes);
    }

    @Post('cycles/:cycleId')
    @Roles('admin')
    async updateCPDCycle(
        @Request() req: RequestWithUser,
        @Body() data: {
            required_points?: number;
        }
    ) {
        return this.cpdService.updateCPDCycle(req.params.cycleId, data);
    }
} 