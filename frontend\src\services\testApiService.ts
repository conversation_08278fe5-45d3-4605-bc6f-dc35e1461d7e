import api from './api';
import { ApiError } from '@/types/api';

/**
 * Test API Service
 * 
 * This service provides methods to test the communication between the frontend and backend.
 * It can be used to verify that each module is working correctly.
 */
const testApiService = {
  /**
   * Get basic test information
   */
  async getTestInfo() {
    try {
      const response = await api.get('/test');
      return response.data;
    } catch (error) {
      console.error('Error fetching test info:', error);
      return {
        status: 'error',
        message: 'Failed to connect to test endpoint',
        timestamp: new Date().toISOString(),
      };
    }
  },

  /**
   * Get information about all modules
   */
  async getModules() {
    try {
      const response = await api.get('/test/modules');
      return response.data;
    } catch (error) {
      console.error('Error fetching modules:', error);
      throw new ApiError({
        code: 'MODULES_FETCH_ERROR',
        message: error instanceof Error ? error.message : 'Failed to fetch modules',
        status: 500
      });
    }
  },

  /**
   * Get detailed information about a specific module
   */
  async getModuleInfo(moduleName: string) {
    try {
      const response = await api.get(`/test/module/${moduleName}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching module info for ${moduleName}:`, error);
      return {
        error: `Failed to fetch info for module: ${moduleName}`,
      };
    }
  },

  /**
   * Check if authentication is working
   */
  async checkAuth() {
    try {
      const response = await api.get('/test/auth-check');
      return {
        authenticated: true,
        ...response.data,
      };
    } catch (error) {
      console.error('Authentication check failed:', error);
      return {
        authenticated: false,
        error: 'Authentication failed',
        timestamp: new Date().toISOString(),
      };
    }
  },

  /**
   * Echo back any data sent to the server
   */
  async echo(data: any) {
    try {
      const response = await api.post('/test/echo', data);
      return response.data;
    } catch (error) {
      console.error('Echo request failed:', error);
      return {
        error: 'Echo request failed',
        timestamp: new Date().toISOString(),
      };
    }
  },

  /**
   * Test all modules at once
   */
  async testAllModules() {
    const results = {
      auth: { status: 'pending' },
      materials: { status: 'pending' },
      users: { status: 'pending' },
      units: { status: 'pending' },
      progress: { status: 'pending' },
      quiz: { status: 'pending' },
      notifications: { status: 'pending' },
    };

    // Test auth module
    try {
      await this.checkAuth();
      results.auth = { status: 'success' };
    } catch (error) {
      results.auth = { status: 'error', error };
    }

    // Test materials module
    try {
      await api.get('/materials');
      results.materials = { status: 'success' };
    } catch (error) {
      results.materials = { status: 'error', error };
    }

    // Test users module
    try {
      await api.get('/users');
      results.users = { status: 'success' };
    } catch (error) {
      results.users = { status: 'error', error };
    }

    // Test units module
    try {
      await api.get('/units');
      results.units = { status: 'success' };
    } catch (error) {
      results.units = { status: 'error', error };
    }

    // Test progress module
    try {
      await api.get('/progress/user/1');
      results.progress = { status: 'success' };
    } catch (error) {
      results.progress = { status: 'error', error };
    }

    // Test quiz module
    try {
      await api.get('/quiz/unit/1');
      results.quiz = { status: 'success' };
    } catch (error) {
      results.quiz = { status: 'error', error };
    }

    // Test notifications module
    try {
      await api.get('/notifications');
      results.notifications = { status: 'success' };
    } catch (error) {
      results.notifications = { status: 'error', error };
    }

    return {
      timestamp: new Date().toISOString(),
      results,
    };
  },
};

export default testApiService;
