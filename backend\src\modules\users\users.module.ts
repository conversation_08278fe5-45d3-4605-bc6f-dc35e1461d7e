// src/modules/users/users.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { UsersRepository } from './repositories/users.repository';
import { User } from '../../entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    // CacheModule, // Import if CacheService is from a separate module
  ],
  controllers: [UsersController],
  providers: [UsersService, UsersRepository],
  exports: [
    UsersService, // This is crucial - export UsersService so other modules can use it
    UsersRepository, // Export if other modules need direct repository access
  ],
})
export class UsersModule {}
