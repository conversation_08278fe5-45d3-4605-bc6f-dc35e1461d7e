import api from './api';

export interface Course {
  id: string;
  title: string;
  description?: string;
  year: number;  // Academic year (1-4)
  semester?: number;  // Semester within the year
  category: string;  // Main subject category (e.g., Anatomy, Biochemistry)
  type: 'lecture' | 'lab' | 'clinical' | 'seminar';
  level?: 'beginner' | 'intermediate' | 'advanced';
  duration?: number;
  modules?: number;
  progress?: number;
  instructor?: string;
  lastAccessed?: string;
  prerequisites?: string[];
  resources?: {
    type: 'textbook' | 'notes' | 'video' | 'quiz';
    title: string;
    url: string;
  }[];
}

const courseService = {
  async getCourses(): Promise<Course[]> {
    try {
      const response = await api.get('/courses');
      return response.data;
    } catch (error) {
      try {
        const materialsResponse = await api.get('/materials');
        // Transform materials data to enhanced course format
        return materialsResponse.data.map((material: any) => ({
          id: material.id,
          title: material.title,
          description: material.description || 'No description available',
          year: this.determineYear(material.title),
          category: this.determineCategory(material.title),
          type: this.determineType(material.type || 'lecture'),
          level: material.difficulty || 'beginner',
          duration: material.duration || Math.floor(Math.random() * 10) + 1,
          modules: material.modules || Math.floor(Math.random() * 10) + 1,
          progress: material.progress || 0,
          instructor: material.author || 'Unknown',
          lastAccessed: material.lastAccessed || new Date().toISOString(),
          prerequisites: [],
          resources: material.resources || []
        }));
      } catch (fallbackError) {
        console.error('Error fetching courses from fallback endpoint:', fallbackError);
        throw fallbackError;
      }
    }
  },

  async getCourseById(id: string): Promise<Course> {
    try {
      const response = await api.get(`/courses/${id}`);
      return response.data;
    } catch (error) {
      try {
        const materialResponse = await api.get(`/materials/${id}`);
        return {
          id: materialResponse.data.id,
          title: materialResponse.data.title,
          description: materialResponse.data.description || 'No description available',
          year: this.determineYear(materialResponse.data.title),
          category: this.determineCategory(materialResponse.data.title),
          type: this.determineType(materialResponse.data.type || 'lecture'),
          level: materialResponse.data.difficulty || 'beginner',
          duration: materialResponse.data.duration || Math.floor(Math.random() * 10) + 1,
          modules: materialResponse.data.modules || Math.floor(Math.random() * 10) + 1,
          progress: materialResponse.data.progress || 0,
          instructor: materialResponse.data.author || 'Unknown',
          lastAccessed: materialResponse.data.lastAccessed || new Date().toISOString(),
          prerequisites: [],
          resources: materialResponse.data.resources || []
        };
      } catch (fallbackError) {
        console.error('Error fetching course by ID from fallback endpoint:', fallbackError);
        throw fallbackError;
      }
    }
  },

  async getRecentCourses(): Promise<Course[]> {
    try {
      const response = await api.get('/courses/recent');
      return response.data;
    } catch (error) {
      const courses = await this.getCourses();
      return courses
        .sort((a, b) => new Date(b.lastAccessed || '').getTime() - new Date(a.lastAccessed || '').getTime())
        .slice(0, 3);
    }
  },

  async updateProgress(courseId: string, progress: number): Promise<void> {
    try {
      await api.post(`/progress/${courseId}`, { progress });
    } catch (error) {
      console.error('Error updating course progress:', error);
      throw error;
    }
  },

  // Helper methods to determine course metadata from title/content
  determineYear(title: string): number {
    const yearMatch = title.toLowerCase().match(/year (\d)/);
    if (yearMatch) return parseInt(yearMatch[1]);
    // Basic logic - can be enhanced based on actual curriculum
    if (title.toLowerCase().includes('introduction') || title.toLowerCase().includes('basic')) return 1;
    if (title.toLowerCase().includes('advanced') || title.toLowerCase().includes('clinical')) return 3;
    return 1;
  },

  determineCategory(title: string): string {
    const categories = {
      anatomy: ['anatomy', 'histology', 'embryology'],
      biochemistry: ['biochemistry', 'molecular', 'chemistry'],
      physiology: ['physiology', 'function'],
      pathology: ['pathology', 'disease', 'disorder'],
      pharmacology: ['pharmacology', 'drug', 'medicine', 'therapeutic'],
      clinical: ['clinical', 'diagnosis', 'treatment', 'patient']
    };

    const lowercaseTitle = title.toLowerCase();
    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => lowercaseTitle.includes(keyword))) {
        return category.charAt(0).toUpperCase() + category.slice(1);
      }
    }
    return 'General';
  },

  determineType(type: string): 'lecture' | 'lab' | 'clinical' | 'seminar' {
    const typeMap: { [key: string]: 'lecture' | 'lab' | 'clinical' | 'seminar' } = {
      lecture: 'lecture',
      laboratory: 'lab',
      lab: 'lab',
      clinical: 'clinical',
      seminar: 'seminar',
      workshop: 'seminar'
    };
    return typeMap[type.toLowerCase()] || 'lecture';
  }
};

export default courseService;
