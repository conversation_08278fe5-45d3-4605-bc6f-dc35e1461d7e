import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from './user.entity';
import { Material } from './materials.entity';

@Entity('learning_suggestions')
export class LearningSuggestion {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    title: string;

    @Column('text')
    description: string;

    @Column({ type: 'int', default: 0 })
    priority: number;

    @Column({ type: 'boolean', default: false })
    is_completed: boolean;

    @Column({ nullable: true })
    completion_date: Date;

    @Column({ type: 'jsonb', nullable: true })
    metadata: {
        estimated_time?: number;
        prerequisites?: string[];
        related_topics?: string[];
        difficulty?: string;
    };

    @ManyToOne(() => User, user => user.learning_suggestions)
    user: User;

    @ManyToOne(() => Material, { nullable: true })
    material: Material;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
} 