import { Controller, Post, UseGuards, Request } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { SyncService } from './sync.service';
import { RequestWithUser } from '../../common/interfaces/request-with-user.interface';

@Controller('sync')
export class SyncController {
    constructor(private readonly syncService: SyncService) {}

    @UseGuards(JwtAuthGuard)
    @Post('progress')
    async syncProgress(@Request() req: RequestWithUser) {
        await this.syncService.syncUserProgress(req.user.id);
        return { message: 'Progress synced successfully' };
    }
} 