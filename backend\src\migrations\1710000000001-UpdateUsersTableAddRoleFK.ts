import { MigrationInterface, QueryRunner, TableColumn, TableForeignKey } from 'typeorm';

export class UpdateUsersTableAddRoleFK1710000000001 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create a temporary column for the new role_id
        await queryRunner.addColumn(
            'users',
            new TableColumn({
                name: 'role_id',
                type: 'uuid',
                isNullable: true,
            })
        );

        // Insert default roles if they don't exist
        await queryRunner.query(`
            INSERT INTO roles (id, name, description)
            VALUES 
                (uuid_generate_v4(), 'admin', 'Administrator role'),
                (uuid_generate_v4(), 'instructor', 'Medical instructor role'),
                (uuid_generate_v4(), 'student', 'Medical student role')
            ON CONFLICT (name) DO NOTHING;
        `);

        // Update the role_id based on the existing role column
        await queryRunner.query(`
            UPDATE users u
            SET role_id = r.id
            FROM roles r
            WHERE u.role = r.name;
        `);

        // Make role_id not nullable after migration
        await queryRunner.changeColumn(
            'users',
            'role_id',
            new TableColumn({
                name: 'role_id',
                type: 'uuid',
                isNullable: false,
            })
        );

        // Add foreign key constraint
        await queryRunner.createForeignKey(
            'users',
            new TableForeignKey({
                name: 'FK_users_role',
                columnNames: ['role_id'],
                referencedColumnNames: ['id'],
                referencedTableName: 'roles',
                onDelete: 'RESTRICT',
            })
        );

        // Drop the old role column
        await queryRunner.dropColumn('users', 'role');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Add back the role column
        await queryRunner.addColumn(
            'users',
            new TableColumn({
                name: 'role',
                type: 'varchar',
                length: '20',
                default: "'student'",
            })
        );

        // Update the role column based on role_id
        await queryRunner.query(`
            UPDATE users u
            SET role = r.name
            FROM roles r
            WHERE u.role_id = r.id;
        `);

        // Drop the foreign key and role_id column
        await queryRunner.dropForeignKey('users', 'FK_users_role');
        await queryRunner.dropColumn('users', 'role_id');
    }
}
