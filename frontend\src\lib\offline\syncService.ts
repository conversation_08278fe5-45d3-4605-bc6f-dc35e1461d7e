import { openDB, DBSchema, IDBPDatabase } from 'idb';

interface SyncOutboxDB extends DBSchema {
  outbox: {
    key: string;
    value: {
      id: string;
      endpoint: string;
      method: 'POST' | 'PUT' | 'DELETE';
      payload: any;
      timestamp: number;
      retryCount: number;
      lastAttempt: number | null;
      status: 'pending' | 'failed' | 'processing';
    };
    indexes: {
      'by-timestamp': number;
      'by-status': string;
    };
  };
  syncStatus: {
    key: string;
    value: {
      lastSyncTimestamp: number;
      isOnline: boolean;
      pendingChanges: number;
    };
  };
}

class SyncService {
  private db: IDBPDatabase<SyncOutboxDB> | null = null;
  private readonly DB_NAME = 'sync-outbox-db';
  private readonly DB_VERSION = 1;
  private syncInProgress = false;
  private onlineStatus = typeof navigator !== 'undefined' ? navigator.onLine : true;

  constructor() {
    if (typeof window !== 'undefined') {
      window.addEventListener('online', this.handleOnlineStatus);
      window.addEventListener('offline', this.handleOnlineStatus);
    }
  }

  private handleOnlineStatus = () => {
    this.onlineStatus = navigator.onLine;
    if (this.onlineStatus) {
      this.processOutbox();
    }
  };

  async init() {
    if (!this.db) {
      this.db = await openDB<SyncOutboxDB>(this.DB_NAME, this.DB_VERSION, {
        upgrade(db) {
          const outboxStore = db.createObjectStore('outbox', { keyPath: 'id' });
          outboxStore.createIndex('by-timestamp', 'timestamp');
          outboxStore.createIndex('by-status', 'status');

          db.createObjectStore('syncStatus', { keyPath: 'id' });
        },
      });

      // Initialize sync status
      const tx = this.db.transaction('syncStatus', 'readwrite');
      await tx.store.put({
        id: 'current',
        lastSyncTimestamp: Date.now(),
        isOnline: this.onlineStatus,
        pendingChanges: 0,
      });
      await tx.done;
    }
    return this.db;
  }

  async addToOutbox(endpoint: string, method: 'POST' | 'PUT' | 'DELETE', payload: any) {
    const db = await this.init();
    const tx = db.transaction('outbox', 'readwrite');
    const item = {
      id: crypto.randomUUID(),
      endpoint,
      method,
      payload,
      timestamp: Date.now(),
      retryCount: 0,
      lastAttempt: null,
      status: 'pending' as const,
    };
    await tx.store.add(item);
    await this.updateSyncStatus();
    await tx.done;

    if (this.onlineStatus) {
      this.processOutbox();
    }
  }

  private async updateSyncStatus() {
    const db = await this.init();
    const tx = db.transaction(['outbox', 'syncStatus'], 'readwrite');
    const pendingCount = await tx.store.index('by-status').count('pending');
    
    await tx.objectStore('syncStatus').put({
      id: 'current',
      lastSyncTimestamp: Date.now(),
      isOnline: this.onlineStatus,
      pendingChanges: pendingCount,
    });
    
    await tx.done;
  }

  async getSyncStatus() {
    const db = await this.init();
    const tx = db.transaction('syncStatus', 'readonly');
    return tx.store.get('current');
  }

  private async processOutbox() {
    if (this.syncInProgress || !this.onlineStatus) return;

    try {
      this.syncInProgress = true;
      const db = await this.init();
      const tx = db.transaction('outbox', 'readwrite');
      const pendingItems = await tx.store.index('by-status').getAll('pending');

      for (const item of pendingItems) {
        try {
          item.status = 'processing';
          await tx.store.put(item);

          const response = await fetch(item.endpoint, {
            method: item.method,
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(item.payload),
          });

          if (response.ok) {
            await tx.store.delete(item.id);
          } else {
            item.status = 'failed';
            item.retryCount++;
            item.lastAttempt = Date.now();
            await tx.store.put(item);
          }
        } catch (error) {
          item.status = 'failed';
          item.retryCount++;
          item.lastAttempt = Date.now();
          await tx.store.put(item);
        }
      }

      await this.updateSyncStatus();
    } finally {
      this.syncInProgress = false;
    }
  }
}

export const syncService = new SyncService(); 