# MedTrack Hub Deployment Checklist

## Pre-Deployment

- [ ] Environment Variables
  - [ ] Copy .env.production to OCI secrets vault
  - [ ] Update CORS origins with production domain
  - [ ] Generate new JWT secrets
  - [ ] Set secure database credentials
  - [ ] Configure Redis credentials

- [ ] Database
  - [ ] Create OCI Autonomous Database instance
  - [ ] Set up database backups
  - [ ] Run migrations: `pnpm run migration:run`
  - [ ] Create admin user: `pnpm run setup:admin`
  - [ ] Verify database connection

- [ ] Redis
  - [ ] Set up OCI Redis instance
  - [ ] Configure persistent storage
  - [ ] Set secure Redis password
  - [ ] Test Redis connection

## Deployment Steps

1. Build and Push Images
```bash
# Backend API
cd backend
docker build -t medtrack-api:latest .
docker tag medtrack-api:latest {oci-registry}/medtrack-api:latest
docker push {oci-registry}/medtrack-api:latest

# Python Analytics
cd ../backend/python_analytics
docker build -t medtrack-analytics:latest .
docker tag medtrack-analytics:latest {oci-registry}/medtrack-analytics:latest
docker push {oci-registry}/medtrack-analytics:latest
```

2. OCI Setup
- [ ] Configure Container Instances
  - [ ] Set resource limits (CPU/Memory)
  - [ ] Configure auto-scaling rules
  - [ ] Setup health checks
  - [ ] Configure logging
  - [ ] Setup monitoring alerts

3. Security
- [ ] Enable WAF (Web Application Firewall)
- [ ] Configure SSL certificates
- [ ] Set up HTTPS redirection
- [ ] Configure security headers
- [ ] Enable monitoring for security events

4. Monitoring
- [ ] Set up OCI Monitoring
  - [ ] Configure CPU/Memory alerts
  - [ ] Set up database monitoring
  - [ ] Configure Redis monitoring
  - [ ] Set up error rate alerts
  - [ ] Configure uptime monitoring

5. Logging
- [ ] Configure OCI Logging
  - [ ] Set up log retention policies
  - [ ] Configure log exports
  - [ ] Set up log alerts

6. Backup
- [ ] Configure database backups
  - [ ] Set backup schedule
  - [ ] Test restore procedure
  - [ ] Document recovery steps

## Post-Deployment

- [ ] Verify all endpoints are working
- [ ] Test authentication system
- [ ] Verify CORS settings
- [ ] Check SSL/TLS configuration
- [ ] Test rate limiting
- [ ] Monitor error rates
- [ ] Verify all health checks
- [ ] Test backup and restore procedures
- [ ] Document deployment configuration

## Emergency Procedures

1. Database Issues
```bash
# Check database connectivity
pnpm run typeorm schema:sync
# Rollback migration if needed
pnpm run migration:revert
```

2. Application Issues
```bash
# Check logs
oci logging log-group tail
# Restart container
oci container-instances container restart
```

3. Security Issues
- Follow security incident response plan
- Contact security team
- Document all actions taken

## Contacts

- DevOps Team: <EMAIL>
- Security Team: <EMAIL>
- Database Admin: <EMAIL>
