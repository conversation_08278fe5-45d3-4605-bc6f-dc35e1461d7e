import { ConfigService } from '@nestjs/config';
import { User } from '../../entities/user.entity';
import { TopicProgress } from '../../entities/topic-progress.entity';
export declare class WhatsAppService {
    private configService;
    private readonly apiUrl;
    private readonly apiToken;
    constructor(configService: ConfigService);
    sendWelcomeMessage(user: User): Promise<void>;
    sendStudyReminder(user: User): Promise<void>;
    sendAchievementNotification(user: User, achievement: string): Promise<void>;
    sendWeeklyDigest(user: User, digest: any): Promise<void>;
    sendCustomNotification(user: User, message: string): Promise<void>;
    sendProgressUpdate(user: User, progress: TopicProgress): Promise<any>;
    sendStreakAlert(user: User): Promise<any>;
    sendGroupStudyInvite(user: User, groupName: string, inviteLink: string): Promise<any>;
    sendDailyDigest(user: User, stats: any): Promise<any>;
    private sendMessage;
    handleIncomingMessage(message: any): Promise<void | null>;
    private handleStartStudyCommand;
    private handleCheckProgressCommand;
}
