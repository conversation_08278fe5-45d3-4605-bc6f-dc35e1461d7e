# Medical Education Platform

A comprehensive medical education platform that combines a modern Next.js frontend, a NestJS backend, and a Python analytics service to provide an enhanced learning experience.

## Project Structure

```
.
├── frontend/           # Next.js frontend application
├── backend/           # NestJS backend application
└── backend/python_analytics/  # Python analytics service
```

## Prerequisites

- Node.js (v18 or later)
- Python (v3.8 or later)
- PostgreSQL
- pnpm (recommended) or npm

## Getting Started

### 1. Frontend Setup

```bash
cd frontend
pnpm install
pnpm run dev
```

The frontend will be available at [http://localhost:3000](http://localhost:3000)

### 2. Backend Setup

```bash
cd backend
pnpm install
pnpm run start:dev
```

The backend API will be available at [http://localhost:3001](http://localhost:3001)

### 3. Python Analytics Service Setup

```bash
cd backend/python_analytics
python -m venv venv
# On Windows:
venv\Scripts\activate
# On Unix/MacOS:
source venv/bin/activate

pip install -r requirements.txt
python main.py
```

The analytics service will be available at [http://localhost:5000](http://localhost:5000)

## Environment Variables

Each component requires its own environment configuration:

### Frontend (.env.local)
```
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_ANALYTICS_URL=http://localhost:5000
```

### Backend (.env)
```
DATABASE_URL=postgresql://user:password@localhost:5432/medical_education
JWT_SECRET_KEY=your_jwt_secret_key
FRONTEND_URL=http://localhost:3000
```

### Python Analytics (.env)
```
DATABASE_URL=postgresql://user:password@localhost:5432/medical_education
JWT_SECRET_KEY=your_jwt_secret_key
FRONTEND_URL=http://localhost:3000
PYTHON_ANALYTICS_PORT=5000
```

## Features

- Modern, responsive user interface built with Next.js
- RESTful API built with NestJS
- Advanced analytics and machine learning capabilities
- Real-time learning pattern analysis
- Performance predictions
- Personalized study recommendations
- Secure authentication and authorization
- Database integration with PostgreSQL

## Development

Each component has its own development scripts and testing setup. Please refer to the individual README files in each directory for more detailed information:

- [Frontend Documentation](frontend/README.md)
- [Backend Documentation](backend/README.md)
- [Python Analytics Documentation](backend/python_analytics/README.md)

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details. 