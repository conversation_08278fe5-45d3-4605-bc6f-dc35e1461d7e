import { IsString, IsN<PERSON>ber, IsDate, IsBoolean, IsOptional, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export enum ProgressStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
}

export class ProgressSyncDto {
  @ApiProperty()
  @IsString()
  materialId: string;

  @ApiProperty()
  @IsNumber()
  progress: number;

  @ApiProperty({ enum: ProgressStatus })
  @IsEnum(ProgressStatus)
  status: ProgressStatus;

  @ApiProperty()
  @IsNumber()
  timeSpent: number;

  @ApiProperty()
  @IsDate()
  @Type(() => Date)
  lastAccessed: Date;

  @ApiProperty()
  @IsDate()
  @Type(() => Date)
  updatedAt: Date;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  synced?: boolean;

  @ApiProperty({ type: Object })
  @IsOptional()
  metadata?: Record<string, any>;
} 