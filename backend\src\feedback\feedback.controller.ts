import { Controller, Post, Body, Get, UseGuards } from "@nestjs/common";
import { JwtAuthGuard } from "../modules/auth/jwt-auth.guard";
import { FeedbackService } from "./feedback.service";

@Controller("feedback")
@UseGuards(JwtAuthGuard)
export class FeedbackController {
  constructor(private readonly feedbackService: FeedbackService) {}

  @Post()
  create(
    @Body() body: { userId: string; comments: string; rating?: number; materialId?: string; unitId?: string },
  ) {
    return this.feedbackService.create(body.userId, body.comments, body.rating, body.materialId, body.unitId);
  }

  @Get()
  findAll() {
    return this.feedbackService.findAll();
  }
}