# Emergency rollback script
param(
    [Parameter(Mandatory=$true)]
    [string]$Version,
    
    [Parameter(Mandatory=$false)]
    [switch]$RollbackDatabase,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force
)

$ErrorActionPreference = "Stop"

# Configuration
$OCI_REGION = $env:OCI_REGION
$OCI_REGISTRY = "$OCI_REGION.ocir.io"
$OCI_NAMESPACE = $env:OCI_NAMESPACE
$CONTAINER_INSTANCE = $env:OCI_CONTAINER_INSTANCE_ID

Write-Host "🚨 EMERGENCY ROLLBACK INITIATED 🚨" -ForegroundColor Red
Write-Host "Rolling back to version: $Version"

if (-not $Force) {
    $confirm = Read-Host "Are you sure you want to proceed with rollback? (y/N)"
    if ($confirm -ne "y") {
        Write-Host "Rollback cancelled" -ForegroundColor Yellow
        exit 0
    }
}

# Step 1: Record current state
Write-Host "`n📝 Recording current state..."
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$logDir = "rollback_logs_$timestamp"
New-Item -ItemType Directory -Path $logDir -Force | Out-Null

# Backup current container state
oci container-instances container-instance get --container-instance-id $CONTAINER_INSTANCE | 
    Out-File "$logDir\container_state.json"

# Step 2: Database rollback if requested
if ($RollbackDatabase) {
    Write-Host "`n🗄️ Rolling back database..."
    try {
        # Get the migration version corresponding to the app version
        $migrationVersion = Get-Content "migrations_versions.json" | ConvertFrom-Json | 
            Select-Object -ExpandProperty $Version

        Write-Host "Rolling back to migration version: $migrationVersion"
        
        # Execute rollback
        pnpm run migration:revert --to $migrationVersion
        
        Write-Host "✅ Database rollback successful" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Database rollback failed: $_" -ForegroundColor Red
        Write-Host "⚠️ Continuing with application rollback..." -ForegroundColor Yellow
    }
}

# Step 3: Application rollback
Write-Host "`n🔄 Rolling back application containers..."

try {
    # Update container instance to previous version
    oci container-instances container-instance update `
        --container-instance-id $CONTAINER_INSTANCE `
        --containers '[{
            "imageUrl": "'$OCI_REGISTRY/$OCI_NAMESPACE/medtrack-api:$Version'",
            "displayName": "medtrack-api"
        }]'

    Write-Host "✅ Container rollback initiated" -ForegroundColor Green
}
catch {
    Write-Host "❌ Container rollback failed: $_" -ForegroundColor Red
    exit 1
}

# Step 4: Verify rollback
Write-Host "`n🔍 Verifying rollback..."

$maxAttempts = 10
$attempt = 0
$success = $false

while ($attempt -lt $maxAttempts -and -not $success) {
    $attempt++
    Write-Host "Verification attempt $attempt of $maxAttempts..."
    
    try {
        $health = Invoke-RestMethod -Uri "http://localhost:3002/health" -Method Get
        if ($health.status -eq "ok") {
            $success = $true
            Write-Host "✅ Application is healthy after rollback" -ForegroundColor Green
            break
        }
    }
    catch {
        Start-Sleep -Seconds 10
    }
}

if (-not $success) {
    Write-Host "❌ Rollback verification failed after $maxAttempts attempts" -ForegroundColor Red
    Write-Host "⚠️ Manual intervention may be required" -ForegroundColor Yellow
    exit 1
}

# Step 5: Notify team
Write-Host "`n📧 Notifying team..."
$message = @{
    channel = "#deployments"
    text = "🚨 Emergency rollback to version $Version completed
    - Time: $(Get-Date)
    - Initiated by: $env:USERNAME
    - Database rollback: $RollbackDatabase
    - Status: Success
    Please verify system status."
}

try {
    Invoke-RestMethod -Uri $env:SLACK_WEBHOOK_URL -Method Post -Body ($message | ConvertTo-Json)
    Write-Host "✅ Team notification sent" -ForegroundColor Green
}
catch {
    Write-Host "⚠️ Failed to send team notification: $_" -ForegroundColor Yellow
}

Write-Host "`n✅ Rollback completed successfully!" -ForegroundColor Green
Write-Host "Please monitor the application for any issues."
