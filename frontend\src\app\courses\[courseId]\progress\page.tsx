'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { apiService } from '@/services/api';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { SEO } from '@/components/SEO';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line
} from 'recharts';
import {
  Award,
  Clock,
  BookOpen,
  Brain,
  Calendar,
  Target,
  TrendingUp
} from 'lucide-react';

interface ProgressData {
  overallProgress: number;
  moduleProgress: {
    id: string;
    title: string;
    progress: number;
  }[];
  quizScores: {
    id: string;
    title: string;
    score: number;
    date: string;
  }[];
  timeSpent: {
    date: string;
    minutes: number;
  }[];
  achievements: {
    id: string;
    title: string;
    description: string;
    date: string;
  }[];
  nextMilestone: {
    title: string;
    progress: number;
    target: number;
  };
}

export default function CourseProgressPage() {
  const { courseId } = useParams();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [progressData, setProgressData] = useState<ProgressData | null>(null);
  const [courseTitle, setCourseTitle] = useState('');

  useEffect(() => {
    const fetchCourseProgress = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const [courseResponse, progressResponse] = await Promise.all([
          apiService.get(`/courses/${courseId}`),
          apiService.get(`/courses/${courseId}/progress`)
        ]);
        setCourseTitle(courseResponse.data.title);
        setProgressData(progressResponse.data);
      } catch (err: any) {
        setError(err.message || 'Failed to load course progress');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCourseProgress();
  }, [courseId]);

  if (isLoading) {
    return <LoadingSpinner fullScreen />;
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-lg">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Error Loading Progress
            </h2>
            <p className="mt-2 text-sm text-gray-600">{error}</p>
            <div className="mt-5">
              <button
                onClick={() => window.location.reload()}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!progressData) {
    return null;
  }

  return (
    <ErrorBoundary>
      <SEO
        title={`${courseTitle} - Progress`}
        description={`Course progress for ${courseTitle}`}
      />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {courseTitle} - Progress
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Track your learning progress and achievements
          </p>
        </div>

        {/* Overall Progress */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">
              Overall Progress
            </h2>
            <span className="text-2xl font-bold text-indigo-600">
              {progressData.overallProgress}%
            </span>
          </div>
          <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded-full">
            <div
              className="h-2 bg-indigo-600 rounded-full"
              style={{ width: `${progressData.overallProgress}%` }}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Module Progress */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Module Progress
            </h2>
            <div className="space-y-4">
              {progressData.moduleProgress.map((module) => (
                <div key={module.id}>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {module.title}
                    </span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {module.progress}%
                    </span>
                  </div>
                  <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded-full">
                    <div
                      className="h-2 bg-indigo-600 rounded-full"
                      style={{ width: `${module.progress}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quiz Performance */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Quiz Performance
            </h2>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={progressData.quizScores}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="title" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip />
                  <Line
                    type="monotone"
                    dataKey="score"
                    stroke="#4F46E5"
                    strokeWidth={2}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Time Spent */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Time Spent
            </h2>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={progressData.timeSpent}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="minutes" fill="#4F46E5" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Achievements */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Achievements
            </h2>
            <div className="space-y-4">
              {progressData.achievements.map((achievement) => (
                <div
                  key={achievement.id}
                  className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                >
                  <Award className="h-6 w-6 text-yellow-500 flex-shrink-0" />
                  <div>
                    <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                      {achievement.title}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {achievement.description}
                    </p>
                    <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                      Earned on {new Date(achievement.date).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Next Milestone */}
        <div className="mt-8 bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                Next Milestone
              </h2>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {progressData.nextMilestone.title}
              </p>
            </div>
            <div className="text-right">
              <span className="text-2xl font-bold text-indigo-600">
                {progressData.nextMilestone.progress}%
              </span>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Target: {progressData.nextMilestone.target}%
              </p>
            </div>
          </div>
          <div className="mt-4 h-2 bg-gray-200 dark:bg-gray-600 rounded-full">
            <div
              className="h-2 bg-indigo-600 rounded-full"
              style={{
                width: `${(progressData.nextMilestone.progress /
                  progressData.nextMilestone.target) *
                  100}%`
              }}
            />
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
} 