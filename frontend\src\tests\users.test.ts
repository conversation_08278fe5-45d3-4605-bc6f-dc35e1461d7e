/**
 * Users Module Tests
 * 
 * This file contains tests for the users module.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import mockApiService from '../services/mockApiService';
import api from '../services/api';

// Mock the API service
vi.mock('../services/api', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

// Create a simple users service for testing
const usersService = {
  async getUsers() {
    try {
      const response = await api.get('/users');
      return response.data;
    } catch (error) {
      console.warn('Error fetching users from API, using mock data:', error);
      return mockApiService.getUsers();
    }
  },
  
  async createUser(userData: any) {
    try {
      const response = await api.post('/users', userData);
      return response.data;
    } catch (error) {
      console.warn('Error creating user via API, using mock data:', error);
      return mockApiService.createUser(userData);
    }
  }
};

describe('Users Module', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Get Users', () => {
    it('should fetch users from API when available', async () => {
      const mockUsers = [
        { id: '1', username: 'user1', email: '<EMAIL>' },
        { id: '2', username: 'user2', email: '<EMAIL>' },
      ];
      
      // Mock successful API response
      (api.get as any).mockResolvedValueOnce({ data: mockUsers });
      
      const users = await usersService.getUsers();
      
      expect(api.get).toHaveBeenCalledWith('/users');
      expect(users).toEqual(mockUsers);
    });

    it('should fall back to mock API when real API fails', async () => {
      // Mock API failure
      (api.get as any).mockRejectedValueOnce(new Error('API error'));
      
      // Spy on mock API
      const mockGetUsers = vi.spyOn(mockApiService, 'getUsers');
      
      const users = await usersService.getUsers();
      
      expect(api.get).toHaveBeenCalledWith('/users');
      expect(mockGetUsers).toHaveBeenCalled();
      expect(users.length).toBeGreaterThan(0);
    });
  });

  describe('Create User', () => {
    it('should create user via API when available', async () => {
      const userData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
      };
      
      const mockResponse = {
        id: '3',
        username: 'newuser',
        email: '<EMAIL>',
      };
      
      // Mock successful API response
      (api.post as any).mockResolvedValueOnce({ data: mockResponse });
      
      const result = await usersService.createUser(userData);
      
      expect(api.post).toHaveBeenCalledWith('/users', userData);
      expect(result).toEqual(mockResponse);
    });

    it('should fall back to mock API when real API fails', async () => {
      const userData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
      };
      
      // Mock API failure
      (api.post as any).mockRejectedValueOnce(new Error('API error'));
      
      // Spy on mock API
      const mockCreateUser = vi.spyOn(mockApiService, 'createUser');
      
      const result = await usersService.createUser(userData);
      
      expect(api.post).toHaveBeenCalledWith('/users', userData);
      expect(mockCreateUser).toHaveBeenCalledWith(userData);
      expect(result.username).toBe(userData.username);
    });
  });

  describe('Mock API Users', () => {
    it('should return users from mock API', async () => {
      const users = await mockApiService.getUsers();
      
      expect(users).toBeDefined();
      expect(Array.isArray(users)).toBe(true);
      expect(users.length).toBeGreaterThan(0);
      expect(users[0]).toHaveProperty('id');
      expect(users[0]).toHaveProperty('username');
      expect(users[0]).toHaveProperty('email');
    });

    it('should create a user with mock API', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
      };
      
      const user = await mockApiService.createUser(userData);
      
      expect(user).toBeDefined();
      expect(user.id).toBeDefined();
      expect(user.username).toBe(userData.username);
      expect(user.email).toBe(userData.email);
      expect(user.password).toBeUndefined(); // Password should not be returned
    });
  });
});
