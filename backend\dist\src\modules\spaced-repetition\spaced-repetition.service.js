"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpacedRepetitionService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const materials_entity_1 = require("../../entities/materials.entity");
const progress_entity_1 = require("../../entities/progress.entity");
let SpacedRepetitionService = class SpacedRepetitionService {
    constructor(progressRepository, materialRepository) {
        this.progressRepository = progressRepository;
        this.materialRepository = materialRepository;
    }
    calculateNextReview(quality, item) {
        let ease_factor = item.ease_factor + (0.1 - (5 - quality) * (0.08 + (5 - quality) * 0.02));
        if (ease_factor < 1.3)
            ease_factor = 1.3;
        let interval;
        if (quality < 3) {
            interval = 1;
        }
        else {
            if (item.interval === 0) {
                interval = 1;
            }
            else if (item.interval === 1) {
                interval = 6;
            }
            else {
                interval = Math.round(item.interval * ease_factor);
            }
        }
        const next_review_date = new Date();
        next_review_date.setDate(next_review_date.getDate() + interval);
        return {
            ...item,
            ease_factor,
            interval,
            next_review_date,
        };
    }
    async addFlashcard(userId, content, answer) {
        const material = this.materialRepository.create({ user: { id: userId.toString() }, content, answer });
        const savedMaterial = await this.materialRepository.save(material);
        const progress = this.progressRepository.create({ user: { id: userId.toString() }, material: savedMaterial });
        await this.progressRepository.save(progress);
        return {
            id: Number(progress.id),
            content,
            answer,
            ease_factor: 2.5,
            interval: 0,
            next_review_date: new Date(),
        };
    }
    async calculateNextReviewDate(userId, materialId) {
        return new Date();
    }
    async getDueReviews(userId) {
        return [];
    }
    async getDueFlashcards(userId, limit = 20) {
        const today = new Date();
        const dueItems = await this.progressRepository
            .createQueryBuilder('progress')
            .innerJoinAndSelect('progress.material', 'material')
            .where('progress.userId = :userId', { userId })
            .andWhere('progress.next_review_date <= :today', { today })
            .orderBy('progress.next_review_date', 'ASC')
            .take(limit)
            .getMany();
        return dueItems.map((item) => ({
            id: Number(item.id),
            content: item.material.content,
            answer: item.material.answer || '',
            ease_factor: item.ease_factor || 2.5,
            interval: item.interval || 0,
            next_review_date: item.next_review_date,
        }));
    }
    async getFlashcardById(id) {
        const flashcard = await this.progressRepository.findOne({
            where: { id: id.toString() },
            relations: ['material']
        });
        if (!flashcard) {
            throw new Error('Flashcard not found');
        }
        return {
            id: Number(flashcard.id),
            content: flashcard.material.content,
            answer: flashcard.material.answer || '',
            ease_factor: flashcard.ease_factor || 2.5,
            interval: flashcard.interval || 0,
            next_review_date: flashcard.next_review_date,
        };
    }
    async recordFlashcardResponse(flashcardId, userId, quality) {
        const progress = await this.progressRepository.findOne({
            where: { id: flashcardId.toString(), user: { id: userId.toString() } }
        });
        if (!progress) {
            throw new Error('Flashcard progress not found');
        }
        const updatedItem = this.calculateNextReview(quality, {
            id: Number(progress.id),
            content: '',
            answer: '',
            ease_factor: progress.ease_factor || 2.5,
            interval: progress.interval || 0,
            next_review_date: progress.next_review_date
        });
        await this.progressRepository.update(flashcardId, {
            ease_factor: updatedItem.ease_factor,
            interval: updatedItem.interval,
            next_review_date: updatedItem.next_review_date,
            last_reviewed_at: new Date()
        });
    }
    async getFlashcardMaterials(userId) {
        return this.materialRepository.find({ where: { user: { id: userId.toString() } } });
    }
    async addMaterial(userId, content, answer) {
        const material = this.materialRepository.create({ user: { id: userId.toString() }, content, answer });
        const savedMaterial = await this.materialRepository.save(material);
        return Array.isArray(savedMaterial) ? savedMaterial[0] : savedMaterial;
    }
    async updateMaterial(id, content, answer) {
        await this.materialRepository.update(id, { content, answer });
        const material = await this.materialRepository.findOne({ where: { id: id.toString() } });
        if (!material) {
            throw new Error('Material not found');
        }
        return material;
    }
    async deleteMaterial(id) {
        await this.materialRepository.delete(id);
    }
    async getProgress(userId) {
        return this.progressRepository.find({ where: { user: { id: userId.toString() } } });
    }
    async addProgress(userId, materialId) {
        const progress = this.progressRepository.create({ user: { id: userId.toString() }, material: { id: materialId.toString() } });
        const savedProgress = await this.progressRepository.save(progress);
        return Array.isArray(savedProgress) ? savedProgress[0] : savedProgress;
    }
    async updateProgress(id, materialId) {
        await this.progressRepository.update(id.toString(), { material: { id: materialId.toString() } });
        const progress = await this.progressRepository.findOne({ where: { id: id.toString() } });
        if (!progress) {
            throw new Error('Progress not found');
        }
        return progress;
    }
};
exports.SpacedRepetitionService = SpacedRepetitionService;
exports.SpacedRepetitionService = SpacedRepetitionService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(progress_entity_1.Progress)),
    __param(1, (0, typeorm_1.InjectRepository)(materials_entity_1.Material)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], SpacedRepetitionService);
//# sourceMappingURL=spaced-repetition.service.js.map