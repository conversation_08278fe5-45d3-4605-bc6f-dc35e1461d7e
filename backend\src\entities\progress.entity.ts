import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from './user.entity';
import { Unit } from './unit.entity';
import { Material, MaterialType } from './materials.entity'; 

@Entity('progress')
export class Progress {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @ManyToOne(() => User, (user: User) => user.progress)
    user: User;

    @ManyToOne(() => Unit, (unit: Unit) => unit.progress)
    unit: Unit;

    @ManyToOne(() => Material, (material: Material) => material.progress)
    material: Material;

    @Column()
    status: string;    @Column({ 
        type: 'enum', 
        enum: MaterialType, 
        enumName: 'material_type', 
        nullable: true 
    })
    activity_type: MaterialType;

    @Column({ type: 'timestamp with time zone', nullable: true })
    last_accessed: Date;

    @Column({ type: 'timestamp', nullable: true })
    last_reviewed_at: Date;

    @Column({ type: 'timestamp' })
    next_review_date: Date;

    @Column({ type: 'float', default: 2.5 })
    ease_factor: number;

    @Column({ type: 'int' })
    interval: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @Column({ default: false })
    is_completed: boolean;
}