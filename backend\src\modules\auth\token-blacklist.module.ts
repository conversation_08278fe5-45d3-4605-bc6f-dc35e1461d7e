import { Module } from '@nestjs/common';
import { TokenBlacklistService } from './token-blacklist.service';
import { CacheModule } from '../../cache/cache.module';
import { JwtModule } from '@nestjs/jwt';

@Module({
  imports: [
    CacheModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'your-secret-key',
    }),
  ],
  providers: [TokenBlacklistService],
  exports: [TokenBlacklistService],
})
export class TokenBlacklistModule {}
