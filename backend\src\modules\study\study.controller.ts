import { Controller, Get, Post, Put, Body, Param, UseGuards } from '@nestjs/common';
import { StudyService } from './study.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guards';
import { Roles } from '../../common/decorators/roles.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../../entities/user.entity';

@Controller('study')
@UseGuards(JwtAuthGuard, RolesGuard)
export class StudyController {
    constructor(private readonly studyService: StudyService) {}

    @Get('topics/:category')
    @Roles('student')
    async getTopicsByCategory(
        @Param('category') category: string
    ) {
        return this.studyService.getTopicsByCategory(category);
    }

    @Get('progress/:topicId')
    @Roles('student')
    async getTopicProgress(
        @CurrentUser() user: User,
        @Param('topicId') topicId: string
    ) {
        return this.studyService.getTopicProgress(user.id, topicId);
    }

    @Post('session/start')
    @Roles('student')
    async startStudySession(
        @CurrentUser() user: User,
        @Body('topicId') topicId: string
    ) {
        return this.studyService.startStudySession(user.id, topicId);
    }

    @Put('session/:sessionId/end')
    @Roles('student')
    async endStudySession(
        @Param('sessionId') sessionId: string,
        @Body('activities') activities: any[]
    ) {
        return this.studyService.endStudySession(sessionId, activities);
    }

    @Get('stats')
    @Roles('student')
    async getStudyStats(@CurrentUser() user: User) {
        return this.studyService.getStudyStats(user.id);
    }
} 