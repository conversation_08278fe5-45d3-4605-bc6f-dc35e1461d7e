import { Controller, UseGuards } from '@nestjs/common';
import { WeeklyDigestService } from './weekly-digest.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guards';

@Controller('weekly-digest')
@UseGuards(JwtAuthGuard, RolesGuard)
export class WeeklyDigestController {
    constructor(private readonly weeklyDigestService: WeeklyDigestService) {}
} 