"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const security_service_1 = require("./security.service");
const security_controller_1 = require("./security.controller");
const security_entity_1 = require("../../entities/security.entity");
const user_entity_1 = require("../../entities/user.entity");
const token_blacklist_module_1 = require("./token-blacklist.module");
const user_security_settings_repository_1 = require("./repositories/user-security-settings.repository");
let SecurityModule = class SecurityModule {
};
exports.SecurityModule = SecurityModule;
exports.SecurityModule = SecurityModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                security_entity_1.UserSecuritySettings,
                security_entity_1.UserSession,
                security_entity_1.SecurityEvent,
                user_entity_1.User,
            ]),
            token_blacklist_module_1.TokenBlacklistModule,
            config_1.ConfigModule,
        ],
        providers: [security_service_1.SecurityService, user_security_settings_repository_1.UserSecuritySettingsRepository],
        controllers: [security_controller_1.SecurityController],
        exports: [security_service_1.SecurityService],
    })
], SecurityModule);
//# sourceMappingURL=security.module.js.map