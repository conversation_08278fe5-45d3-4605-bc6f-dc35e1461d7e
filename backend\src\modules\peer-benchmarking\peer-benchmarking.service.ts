import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { TopicProgress } from '../../entities/topic-progress.entity';
import { StudySession } from '../../entities/study-session.entity';
import { User } from '../../entities/user.entity';

@Injectable()
export class PeerBenchmarkingService {
    constructor(
        @InjectRepository(TopicProgress)
        private topicProgressRepository: Repository<TopicProgress>,
        @InjectRepository(StudySession)
        private studySessionRepository: Repository<StudySession>,
        @InjectRepository(User)
        private userRepository: Repository<User>
    ) {}

    async getPeerStats(userId: string, topicId: string) {
        const userProgress = await this.topicProgressRepository.findOne({
            where: { user: { id: userId }, topic: { id: topicId } },
            relations: ['user', 'topic']
        });

        if (!userProgress) {
            return {
                user_progress: {
                    completion_percentage: 0,
                    time_spent_minutes: 0,
                    streak_days: 0
                },
                peer_averages: {
                    completion_percentage: 0,
                    time_spent_minutes: 0,
                    streak_days: 0
                },
                percentile: 0
            };
        }

        const peerProgress = await this.topicProgressRepository
            .createQueryBuilder('progress')
            .where('progress.topic.id = :topicId', { topicId })
            .andWhere('progress.user.id != :userId', { userId })
            .getMany();

        const stats = {
            user_progress: {
                completion_percentage: userProgress.completion_percentage || 0,
                time_spent_minutes: userProgress.time_spent_minutes || 0,
                streak_days: userProgress.streak_days || 0
            },
            peer_averages: {
                completion_percentage: this.calculateAverage(peerProgress, 'completion_percentage'),
                time_spent_minutes: this.calculateAverage(peerProgress, 'time_spent_minutes'),
                streak_days: this.calculateAverage(peerProgress, 'streak_days')
            },
            percentile: this.calculatePercentile(userProgress, peerProgress)
        };

        return stats;
    }

    async getLeaderboard(topicId: string, limit: number = 10) {
        return this.topicProgressRepository
            .createQueryBuilder('progress')
            .where('progress.topic.id = :topicId', { topicId })
            .orderBy('progress.completion_percentage', 'DESC')
            .addOrderBy('progress.streak_days', 'DESC')
            .take(limit)
            .leftJoinAndSelect('progress.user', 'user')
            .select([
                'progress.completion_percentage',
                'progress.streak_days',
                'progress.time_spent_minutes',
                'user.id',
                'user.username'
            ])
            .getMany();
    }

    async getStudyGroupStats(groupId: string) {
        const groupMembers = await this.userRepository
            .createQueryBuilder('user')
            .where('user.study_group_id = :groupId', { groupId })
            .getMany();

        const memberIds = groupMembers.map((member: User) => member.id);

        if (!memberIds.length) {
            return {
                average_completion: 0,
                average_streak: 0,
                total_study_time: 0,
                member_count: 0
            };
        }

        const groupProgress = await this.topicProgressRepository
            .createQueryBuilder('progress')
            .where('progress.user.id IN (:...memberIds)', { memberIds })
            .getMany();

        return {
            average_completion: this.calculateAverage(groupProgress, 'completion_percentage'),
            average_streak: this.calculateAverage(groupProgress, 'streak_days'),
            total_study_time: groupProgress.reduce((sum: number, p: TopicProgress) => sum + (p.time_spent_minutes || 0), 0),
            member_count: groupMembers.length
        };
    }

    private calculateAverage(progress: TopicProgress[], field: keyof TopicProgress): number {
        if (!progress.length) return 0;
        const sum = progress.reduce((acc, p) => acc + ((p[field] as number) || 0), 0);
        return sum / progress.length;
    }

    private calculatePercentile(userProgress: TopicProgress, peerProgress: TopicProgress[]): number {
        if (!userProgress || !peerProgress.length) return 0;

        const allProgress = [...peerProgress, userProgress];
        const sortedProgress = allProgress.sort((a, b) => 
            (b.completion_percentage || 0) - (a.completion_percentage || 0)
        );

        const userIndex = sortedProgress.findIndex(p => p.id === userProgress.id);
        return ((userIndex + 1) / sortedProgress.length) * 100;
    }
} 