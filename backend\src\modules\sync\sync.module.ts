import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SyncController } from './sync.controller';
import { SyncService } from './sync.service';
import { QuizAttempt } from '../../entities/quiz-attempt.entity';
import { Progress } from '../../entities/progress.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([QuizAttempt, Progress]),
  ],
  controllers: [SyncController],
  providers: [SyncService],
  exports: [SyncService],
})
export class SyncModule {} 