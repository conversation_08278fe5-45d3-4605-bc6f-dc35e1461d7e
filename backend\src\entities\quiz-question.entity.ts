import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Unit } from './unit.entity';

@Entity('quiz_questions')
export class QuizQuestion {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @ManyToOne(() => Unit)
    unit: Unit;

    @Column('text')
    question_text: string;

    @Column('jsonb')
    options: string[];

    @Column('text')
    correct_answer: string;

    @Column('text', { nullable: true })
    explanation: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}