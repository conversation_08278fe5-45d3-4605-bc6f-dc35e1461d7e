import { StudyService } from './study.service';
import { User } from '../../entities/user.entity';
export declare class StudyController {
    private readonly studyService;
    constructor(studyService: StudyService);
    getTopicsByCategory(category: string): Promise<import("../../entities/topic.entity").Topic[]>;
    getTopicProgress(user: User, topicId: string): Promise<import("../../entities/topic-progress.entity").TopicProgress | null>;
    startStudySession(user: User, topicId: string): Promise<import("../../entities/study-session.entity").StudySession>;
    endStudySession(sessionId: string, activities: any[]): Promise<import("../../entities/study-session.entity").StudySession>;
    getStudyStats(user: User): Promise<any>;
}
