{"version": 3, "sources": ["../../src/util/InstanceChecker.ts"], "names": [], "mappings": ";;;AA8BA,MAAa,eAAe;IACxB,MAAM,CAAC,gBAAgB,CAAC,GAAY;QAChC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAA;IAC5C,CAAC;IACD,MAAM,CAAC,gBAAgB,CAAC,GAAY;QAChC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAA;IAC5C,CAAC;IACD,MAAM,CAAC,gBAAgB,CAAC,GAAY;QAChC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAA;IAC5C,CAAC;IACD,MAAM,CAAC,oBAAoB,CAAC,GAAY;QACpC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAA;IAChD,CAAC;IACD,MAAM,CAAC,oBAAoB,CAAC,GAAY;QACpC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAA;IAChD,CAAC;IACD,MAAM,CAAC,oBAAoB,CAAC,GAAY;QACpC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAA;IAChD,CAAC;IACD,MAAM,CAAC,oBAAoB,CAAC,GAAY;QACpC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAA;IAChD,CAAC;IACD,MAAM,CAAC,wBAAwB,CAC3B,GAAY;QAEZ,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAA;IACpD,CAAC;IACD,MAAM,CAAC,sBAAsB,CACzB,GAAY;QAEZ,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,sBAAsB,CAAC,CAAA;IAClD,CAAC;IACD,MAAM,CAAC,UAAU,CAAC,GAAY;QAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,UAAU,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,aAAa,CAAC,CAAA;IACxE,CAAC;IACD,MAAM,CAAC,aAAa,CAAC,GAAY;QAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,aAAa,CAAC,CAAA;IACzC,CAAC;IACD,MAAM,CAAC,SAAS,CAAC,GAAY;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,SAAS,CAAC,CAAA;IACrC,CAAC;IACD,MAAM,CAAC,oBAAoB,CAAC,GAAY;QACpC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAA;IAChD,CAAC;IACD,MAAM,CAAC,oBAAoB,CAAC,GAAY;QACpC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAA;IAChD,CAAC;IACD,MAAM,CAAC,oBAAoB,CAAC,GAAY;QACpC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAA;IAChD,CAAC;IACD,MAAM,CAAC,cAAc,CAAC,GAAY;QAC9B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,cAAc,CAAC,CAAA;IAC1C,CAAC;IACD,MAAM,CAAC,uBAAuB,CAAC,GAAY;QACvC,OAAO,CACH,OAAO,GAAG,KAAK,UAAU;YACzB,OAAQ,GAAyB,CAAC,KAAK,KAAK,UAAU;YACtD,OAAQ,GAAyB,CAAC,IAAI,KAAK,UAAU;YACrD,OAAQ,GAAyB,CAAC,aAAa,KAAK,UAAU,CACjE,CAAA;IACL,CAAC;IACD,MAAM,CAAC,cAAc,CAAC,GAAY;QAC9B,OAAO,CACH,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,cAAc,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC,CACtE,CAAA;IACL,CAAC;IACD,MAAM,CAAC,eAAe,CAAC,GAAY;QAC/B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC,CAAA;IAC3C,CAAC;IACD,MAAM,CAAC,OAAO,CAAC,GAAY;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;IACnC,CAAC;IACD,MAAM,CAAC,OAAO,CAAC,GAAY;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;IACnC,CAAC;IACD,MAAM,CAAC,YAAY,CAAC,GAAY;QAC5B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,YAAY,CAAC,CAAA;IACxC,CAAC;IACD,MAAM,CAAC,aAAa,CAAC,GAAY;QAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,aAAa,CAAC,CAAA;IACzC,CAAC;IACD,MAAM,CAAC,gBAAgB,CAAC,GAAY;QAChC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAA;IAC5C,CAAC;IACD,MAAM,CAAC,iBAAiB,CAAC,GAAY;QACjC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;IAC7C,CAAC;IACD,MAAM,CAAC,YAAY,CAAC,GAAY;QAC5B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,YAAY,CAAC,CAAA;IACxC,CAAC;IACD,MAAM,CAAC,aAAa,CAAC,GAAY;QAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,aAAa,CAAC,CAAA;IACzC,CAAC;IACD,MAAM,CAAC,MAAM,CAAC,GAAY;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;IAClC,CAAC;IACD,MAAM,CAAC,YAAY,CAAC,GAAY;QAC5B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,YAAY,CAAC,CAAA;IACxC,CAAC;IAEO,MAAM,CAAC,KAAK,CAAC,GAAY,EAAE,IAAY;QAC3C,OAAO,CACH,OAAO,GAAG,KAAK,QAAQ;YACvB,GAAG,KAAK,IAAI;YACX,GAAiC,CAAC,aAAa,CAAC;gBAC7C,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CACvB,CAAA;IACL,CAAC;CACJ;AA5GD,0CA4GC", "file": "InstanceChecker.js", "sourcesContent": ["import type { MongoEntityManager } from \"../entity-manager/MongoEntityManager\"\nimport type { SqljsEntityManager } from \"../entity-manager/SqljsEntityManager\"\nimport type { EntitySchema } from \"../entity-schema/EntitySchema\"\nimport type { FindOperator } from \"../find-options/FindOperator\"\nimport type { EqualOperator } from \"../find-options/EqualOperator\"\nimport type { Query } from \"../driver/Query\"\nimport type { RdbmsSchemaBuilder } from \"../schema-builder/RdbmsSchemaBuilder\"\nimport type { Subject } from \"../persistence/Subject\"\nimport type { SelectQueryBuilder } from \"../query-builder/SelectQueryBuilder\"\nimport type { UpdateQueryBuilder } from \"../query-builder/UpdateQueryBuilder\"\nimport type { DeleteQueryBuilder } from \"../query-builder/DeleteQueryBuilder\"\nimport type { SoftDeleteQueryBuilder } from \"../query-builder/SoftDeleteQueryBuilder\"\nimport type { InsertQueryBuilder } from \"../query-builder/InsertQueryBuilder\"\nimport type { RelationQueryBuilder } from \"../query-builder/RelationQueryBuilder\"\nimport type { Brackets } from \"../query-builder/Brackets\"\nimport type { Table } from \"../schema-builder/table/Table\"\nimport type { TableCheck } from \"../schema-builder/table/TableCheck\"\nimport type { TableColumn } from \"../schema-builder/table/TableColumn\"\nimport type { TableExclusion } from \"../schema-builder/table/TableExclusion\"\nimport type { TableForeignKey } from \"../schema-builder/table/TableForeignKey\"\nimport type { TableIndex } from \"../schema-builder/table/TableIndex\"\nimport type { TableUnique } from \"../schema-builder/table/TableUnique\"\nimport type { View } from \"../schema-builder/view/View\"\nimport type { NotBrackets } from \"../query-builder/NotBrackets\"\nimport type { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport type { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport type { MssqlParameter } from \"../driver/sqlserver/MssqlParameter\"\nimport { DataSource } from \"../data-source\"\nimport { BaseEntity } from \"../repository/BaseEntity\"\n\nexport class InstanceChecker {\n    static isMssqlParameter(obj: unknown): obj is MssqlParameter {\n        return this.check(obj, \"MssqlParameter\")\n    }\n    static isEntityMetadata(obj: unknown): obj is EntityMetadata {\n        return this.check(obj, \"EntityMetadata\")\n    }\n    static isColumnMetadata(obj: unknown): obj is ColumnMetadata {\n        return this.check(obj, \"ColumnMetadata\")\n    }\n    static isSelectQueryBuilder(obj: unknown): obj is SelectQueryBuilder<any> {\n        return this.check(obj, \"SelectQueryBuilder\")\n    }\n    static isInsertQueryBuilder(obj: unknown): obj is InsertQueryBuilder<any> {\n        return this.check(obj, \"InsertQueryBuilder\")\n    }\n    static isDeleteQueryBuilder(obj: unknown): obj is DeleteQueryBuilder<any> {\n        return this.check(obj, \"DeleteQueryBuilder\")\n    }\n    static isUpdateQueryBuilder(obj: unknown): obj is UpdateQueryBuilder<any> {\n        return this.check(obj, \"UpdateQueryBuilder\")\n    }\n    static isSoftDeleteQueryBuilder(\n        obj: unknown,\n    ): obj is SoftDeleteQueryBuilder<any> {\n        return this.check(obj, \"SoftDeleteQueryBuilder\")\n    }\n    static isRelationQueryBuilder(\n        obj: unknown,\n    ): obj is RelationQueryBuilder<any> {\n        return this.check(obj, \"RelationQueryBuilder\")\n    }\n    static isBrackets(obj: unknown): obj is Brackets {\n        return this.check(obj, \"Brackets\") || this.check(obj, \"NotBrackets\")\n    }\n    static isNotBrackets(obj: unknown): obj is NotBrackets {\n        return this.check(obj, \"NotBrackets\")\n    }\n    static isSubject(obj: unknown): obj is Subject {\n        return this.check(obj, \"Subject\")\n    }\n    static isRdbmsSchemaBuilder(obj: unknown): obj is RdbmsSchemaBuilder {\n        return this.check(obj, \"RdbmsSchemaBuilder\")\n    }\n    static isMongoEntityManager(obj: unknown): obj is MongoEntityManager {\n        return this.check(obj, \"MongoEntityManager\")\n    }\n    static isSqljsEntityManager(obj: unknown): obj is SqljsEntityManager {\n        return this.check(obj, \"SqljsEntityManager\")\n    }\n    static isEntitySchema(obj: unknown): obj is EntitySchema {\n        return this.check(obj, \"EntitySchema\")\n    }\n    static isBaseEntityConstructor(obj: unknown): obj is typeof BaseEntity {\n        return (\n            typeof obj === \"function\" &&\n            typeof (obj as typeof BaseEntity).hasId === \"function\" &&\n            typeof (obj as typeof BaseEntity).save === \"function\" &&\n            typeof (obj as typeof BaseEntity).useDataSource === \"function\"\n        )\n    }\n    static isFindOperator(obj: unknown): obj is FindOperator<any> {\n        return (\n            this.check(obj, \"FindOperator\") || this.check(obj, \"EqualOperator\")\n        )\n    }\n    static isEqualOperator(obj: unknown): obj is EqualOperator<any> {\n        return this.check(obj, \"EqualOperator\")\n    }\n    static isQuery(obj: unknown): obj is Query {\n        return this.check(obj, \"Query\")\n    }\n    static isTable(obj: unknown): obj is Table {\n        return this.check(obj, \"Table\")\n    }\n    static isTableCheck(obj: unknown): obj is TableCheck {\n        return this.check(obj, \"TableCheck\")\n    }\n    static isTableColumn(obj: unknown): obj is TableColumn {\n        return this.check(obj, \"TableColumn\")\n    }\n    static isTableExclusion(obj: unknown): obj is TableExclusion {\n        return this.check(obj, \"TableExclusion\")\n    }\n    static isTableForeignKey(obj: unknown): obj is TableForeignKey {\n        return this.check(obj, \"TableForeignKey\")\n    }\n    static isTableIndex(obj: unknown): obj is TableIndex {\n        return this.check(obj, \"TableIndex\")\n    }\n    static isTableUnique(obj: unknown): obj is TableUnique {\n        return this.check(obj, \"TableUnique\")\n    }\n    static isView(obj: unknown): obj is View {\n        return this.check(obj, \"View\")\n    }\n    static isDataSource(obj: unknown): obj is DataSource {\n        return this.check(obj, \"DataSource\")\n    }\n\n    private static check(obj: unknown, name: string) {\n        return (\n            typeof obj === \"object\" &&\n            obj !== null &&\n            (obj as { \"@instanceof\": symbol })[\"@instanceof\"] ===\n                Symbol.for(name)\n        )\n    }\n}\n"], "sourceRoot": ".."}