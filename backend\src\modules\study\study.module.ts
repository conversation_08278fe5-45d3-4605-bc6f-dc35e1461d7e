import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { StudyController } from './study.controller';
import { StudyService } from './study.service';
import { Topic } from '../../entities/topic.entity';
import { TopicProgress } from '../../entities/topic-progress.entity';
import { StudySession } from '../../entities/study-session.entity';

@Module({
    imports: [
        TypeOrmModule.forFeature([Topic, TopicProgress, StudySession])
    ],
    controllers: [StudyController],
    providers: [StudyService],
    exports: [StudyService]
})
export class StudyModule {} 