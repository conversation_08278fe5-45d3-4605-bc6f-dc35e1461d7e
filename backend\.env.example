# JWT Configuration
JWT_SECRET=replace_this_with_a_secure_random_string_in_production
JWT_EXPIRATION=1h
REFRESH_TOKEN_SECRET=replace_this_with_another_secure_random_string_in_production
REFRESH_TOKEN_EXPIRATION=7d

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_db_password
DB_DATABASE=medical_db

# Server Configuration
PORT=3002
NODE_ENV=development

# Security
BCRYPT_ROUNDS=12
ALLOWED_ORIGINS=http://localhost:3000
SESSION_SECRET=replace_this_with_a_secure_session_secret

# Rate Limiting
RATE_LIMIT_WINDOW=900000  # 15 minutes in milliseconds
RATE_LIMIT_MAX_REQUESTS=100
LOGIN_RATE_LIMIT_MAX=5
