import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Notification } from "../entities/notifications.entity";

@Injectable()
export class NotificationsService {
  constructor(
    @InjectRepository(Notification)
    private notificationsRepository: Repository<Notification>,
  ) {}

  async create(userId: string, message: string): Promise<Notification> {
    const notification = this.notificationsRepository.create({ user: { id: userId }, message });
    return this.notificationsRepository.save(notification);
  }

  async findByUser(userId: string): Promise<Notification[]> {
    return this.notificationsRepository.find({ where: { user: { id: userId } } });
  }
}