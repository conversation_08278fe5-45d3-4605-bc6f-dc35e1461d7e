// src/hooks/useAuth.ts
'use client';

import { useSession, signIn, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';

export const useAuth = () => {
  const { data: session, status } = useSession();
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    if (status !== 'loading') {
      setLoading(false);
    }
  }, [status]);

  const login = async (email: string, password: string) => {
    try {
      const result = await signIn('credentials', {
        redirect: false,
        email,
        password,
      });

      return result;
    } catch (error) {
      console.error('Login error:', error);
      return { error: 'Authentication failed' };
    }
  };

  const logout = () => {
    signOut({ callbackUrl: '/' });
  };

  const requireAuth = (callback?: () => void) => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/login');
    } else if (callback) {
      callback();
    }
  };

  return {
    session,
    loading,
    isAuthenticated: !!session,
    login,
    logout,
    requireAuth,
    user: session?.user,
    role: session?.user?.role,
  };
};