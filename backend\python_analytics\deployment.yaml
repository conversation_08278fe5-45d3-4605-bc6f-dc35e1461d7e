apiVersion: apps/v1
kind: Deployment
metadata:
  name: medical-analytics-api
spec:
  replicas: 2
  selector:
    matchLabels:
      app: medical-analytics-api
  template:
    metadata:
      labels:
        app: medical-analytics-api
    spec:
      containers:
      - name: medical-analytics-api
        image: <your-registry>.ocir.io/<tenancy-namespace>/medical-analytics-api:latest
        ports:
        - containerPort: 5000
        env:
        - name: FRONTEND_URL
          value: "https://your-frontend-url"
        # Add other environment variables as needed
---
apiVersion: v1
kind: Service
metadata:
  name: medical-analytics-service
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 5000
  selector:
    app: medical-analytics-api