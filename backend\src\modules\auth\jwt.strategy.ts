import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '../users/users.service';

interface JwtPayload {
  sub: string;
  email: string;
  role?: string;
  iat?: number;
  exp?: number;
}

interface AuthenticatedUser {
  id: string;
  email: string;
  role: string;
  is_active: boolean; // Added for user status check
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  private readonly logger = new Logger(JwtStrategy.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly usersService: UsersService,
  ) {
    const jwtSecret = configService.get<string>('JWT_SECRET');

    if (!jwtSecret) {
      const errorMsg = 'JWT_SECRET not found in environment variables';
      console.error(`[${JwtStrategy.name}] ${errorMsg}`);
      throw new Error('JWT_SECRET must be defined in environment variables');
    }

    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: jwtSecret,
    });

    this.logger.log('JWT Strategy initialized successfully');
  }

  async validate(payload: JwtPayload): Promise<AuthenticatedUser> {
    const logContext = `User ID: ${payload.sub}, Email: ${payload.email}`;

    try {
      this.logger.debug(`Validating token for ${logContext}`);

      // Validate payload structure
      this.validatePayloadStructure(payload);

      // Fetch user from database
      const user = await this.fetchAndValidateUser(payload);

      // Additional security checks
      this.performSecurityChecks(user, payload);

      this.logger.log(
        `User validated successfully: ${user.email}, Role: ${user.role}`,
      );

      return {
        id: user.id,
        email: user.email,
        role: user.role,
        is_active: user.is_active ?? true,
      };
    } catch (error) {
      this.logger.error(
        `Token validation failed for ${logContext}: ${error.message}`,
      );

      if (error instanceof UnauthorizedException) {
        throw error;
      }

      // Log unexpected errors but don't expose internal details
      this.logger.error(
        `Unexpected error during token validation: ${error.stack}`,
      );
      throw new UnauthorizedException('Authentication failed');
    }
  }

  private validatePayloadStructure(payload: JwtPayload): void {
    if (!payload.sub) {
      this.logger.warn('Token payload missing user ID (sub field)');
      throw new UnauthorizedException('Invalid token format: missing user ID');
    }

    if (!payload.email) {
      this.logger.warn('Token payload missing email field');
      throw new UnauthorizedException('Invalid token format: missing email');
    }

    // Validate email format (basic check)
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(payload.email)) {
      this.logger.warn(`Invalid email format in token: ${payload.email}`);
      throw new UnauthorizedException('Invalid token format: malformed email');
    }
  }

  private async fetchAndValidateUser(payload: JwtPayload) {
    const user = await this.usersService.findById(payload.sub);

    if (!user) {
      this.logger.warn(`User not found for ID: ${payload.sub}`);
      throw new UnauthorizedException('User not found');
    }

    // Check if user account is active/enabled
    if (user.is_active === false) {
      this.logger.warn(
        `Inactive user attempted authentication: ${payload.sub}`,
      );
      throw new UnauthorizedException('Account is inactive');
    }

    return user;
  }

  private performSecurityChecks(user: any, payload: JwtPayload): void {
    // Verify email matches (protect against email changes after token issue)
    if (user.email !== payload.email) {
      this.logger.warn(
        `Email mismatch for user ID: ${payload.sub}. ` +
          `Token email: ${payload.email}, Current email: ${user.email}`,
      );
      throw new UnauthorizedException('Token authentication failed');
    }

    // Optional: Check if user's role has changed significantly
    if (payload.role && user.role !== payload.role) {
      this.logger.warn(
        `Role mismatch detected for user ${payload.sub}. ` +
          `Token role: ${payload.role}, Current role: ${user.role}`,
      );
      // Note: This might be expected behavior if roles can be updated
      // Consider whether to throw an error or just log the discrepancy
    }

    // Optional: Add timestamp checks for additional security
    if (payload.iat) {
      const tokenAge = Math.floor(Date.now() / 1000) - payload.iat;
      const maxTokenAge = this.configService.get<number>(
        'JWT_MAX_AGE_SECONDS',
        86400,
      ); // 24 hours default

      if (tokenAge > maxTokenAge) {
        this.logger.warn(
          `Token too old for user ${payload.sub}: ${tokenAge} seconds`,
        );
        throw new UnauthorizedException('Token has expired');
      }
    }
  }
}
