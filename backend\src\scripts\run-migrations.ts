#!/usr/bin/env ts-node
import { DataSource } from 'typeorm';
import { config } from 'dotenv';
import * as path from 'path';

// Load environment variables from .env.production
config({ path: path.join(__dirname, '../../.env.production') });

const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.POSTGRES_HOST,
  port: parseInt(process.env.POSTGRES_PORT || '5432'),
  username: process.env.POSTGRES_USER,
  password: process.env.POSTGRES_PASSWORD,
  database: process.env.POSTGRES_DB,
  entities: [path.join(__dirname, '..', '**', '*.entity.{ts,js}')],
  migrations: [path.join(__dirname, '..', 'database', 'migrations', '*.{ts,js}')],
  migrationsTableName: 'migrations',
  migrationsRun: true,
  synchronize: false,
  logging: true,
});

async function runMigrations() {
  try {
    await AppDataSource.initialize();
    console.log('Database connection initialized');

    await AppDataSource.runMigrations();
    console.log('Migrations completed successfully');

    await AppDataSource.destroy();
    process.exit(0);
  } catch (error) {
    console.error('Error during migration:', error);
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
    }
    process.exit(1);
  }
}

runMigrations();
