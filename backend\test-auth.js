// Simple test script to verify authentication endpoints
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3002/v1';

async function testCreateUsers() {
  try {
    console.log('Testing create-test-users endpoint...');
    const response = await fetch(`${BASE_URL}/auth/create-test-users`);
    const data = await response.json();
    console.log('Create users response:', data);
    return data;
  } catch (error) {
    console.error('Error creating test users:', error.message);
    return null;
  }
}

async function testLogin(identifier, password) {
  try {
    console.log(`Testing login with identifier: ${identifier}`);
    const response = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: identifier,
        password: password
      })
    });
    
    const data = await response.json();
    console.log('Login response:', data);
    return data;
  } catch (error) {
    console.error('Error during login:', error.message);
    return null;
  }
}

async function runTests() {
  console.log('Starting authentication tests...\n');
  
  // Test 1: Create test users
  await testCreateUsers();
  
  // Wait a bit for users to be created
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Test 2: Login with username
  await testLogin('test', 'test');
  
  // Test 3: Login with email
  await testLogin('<EMAIL>', 'test');
  
  console.log('\nTests completed.');
}

runTests().catch(console.error);
