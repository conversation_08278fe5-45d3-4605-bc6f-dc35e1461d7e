import { Repository } from 'typeorm';
import { Topic } from '../../entities/topic.entity';
import { TopicProgress } from '../../entities/topic-progress.entity';
import { StudySession } from '../../entities/study-session.entity';
import { User } from '../../entities/user.entity';
export declare class StudyService {
    private topicRepository;
    private topicProgressRepository;
    private studySessionRepository;
    constructor(topicRepository: Repository<Topic>, topicProgressRepository: Repository<TopicProgress>, studySessionRepository: Repository<StudySession>);
    getTopicsByCategory(category: string): Promise<Topic[]>;
    getTopicProgress(userId: string, topicId: string): Promise<TopicProgress | null>;
    startStudySession(userId: string, topicId: string): Promise<StudySession>;
    endStudySession(sessionId: string, activities: any[]): Promise<StudySession>;
    private calculateFocusScore;
    private updateTopicProgress;
    private isConsecutiveDay;
    getStudyStats(userId: string): Promise<any>;
    trackActivity(user: User, activity: {
        type: string;
        duration_minutes: number;
        score?: number;
    }): Promise<void>;
}
