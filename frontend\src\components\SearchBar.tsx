'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { FaSearch } from 'react-icons/fa';

interface SearchResult {
  title: string;
  type: 'course' | 'material' | 'quiz' | 'lab' | 'clinical';
  category?: string;
  year?: number;
  href: string;
}

export default function SearchBar() {
  const router = useRouter();
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [results, setResults] = useState<SearchResult[]>([]);

  // Add categories based on pharmacy curriculum
  const categories = [
    { id: 'year1', name: 'Year 1', subjects: ['Anatomy', 'Biochemistry', 'Physiology'] },
    { id: 'year2', name: 'Year 2', subjects: ['Pathology', 'Pharmacology', 'Microbiology'] },
    { id: 'year3', name: 'Year 3', subjects: ['Clinical Medicine', 'Therapeutics', 'Pharmacy Practice'] },
    { id: 'year4', name: 'Year 4', subjects: ['Advanced Pharmacy', 'Clinical Rotations', 'Research'] }
  ];

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchQuery = e.target.value;
    setQuery(searchQuery);
    setIsOpen(true);
    
    if (searchQuery.length > 0) {
      // TODO: Replace with actual API call
      const mockResults: SearchResult[] = [
        {
          title: 'Introduction to Anatomy',
          type: 'course',
          category: 'Anatomy',
          year: 1,
          href: '/courses/anatomy-101'
        },
        {
          title: 'Physiology Lab',
          type: 'lab',
          category: 'Physiology',
          year: 1,
          href: '/labs/physiology-lab-1'
        },
        {
          title: 'Clinical Case Studies',
          type: 'clinical',
          category: 'Clinical Medicine',
          year: 3,
          href: '/materials/clinical-cases'
        }
      ];
      setResults(mockResults.filter(result => 
        result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        result.category?.toLowerCase().includes(searchQuery.toLowerCase())
      ));
    } else {
      setResults([]);
    }
  };

  return (
    <div className="relative w-full max-w-2xl">
      <div className="relative">
        <input
          type="text"
          value={query}
          onChange={handleSearch}
          placeholder="Search by course name, subject, or year..."
          className="w-full px-4 py-3 pl-12 rounded-lg border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-colors"
        />
        <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
      </div>
      
      {isOpen && results.length > 0 && (
        <div className="absolute mt-2 w-full bg-white rounded-lg shadow-lg border border-gray-100 py-2 z-50">
          {results.map((result, index) => (
            <button
              key={index}
              onClick={() => {
                router.push(result.href);
                setIsOpen(false);
              }}
              className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center justify-between group"
            >
              <div>
                <div className="font-medium text-gray-900">{result.title}</div>
                <div className="text-sm text-gray-500">
                  {result.category} • Year {result.year}
                </div>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
