export interface Role {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  created_at?: Date;
  updated_at?: Date;
}

// Default roles that will be created if they don't exist
export const DEFAULT_ROLES: Omit<Role, 'id' | 'created_at' | 'updated_at'>[] = [
  {
    name: 'Administrator',
    description: 'Full access to all platform features and settings',
    is_active: true
  },
  {
    name: 'Medical Instructor',
    description: 'Can create and manage courses, content, and student progress',
    is_active: true
  },
  {
    name: 'Medical Resident',
    description: 'Advanced medical student with teaching capabilities',
    is_active: true
  },
  {
    name: 'Medical Student',
    description: 'Basic access to courses and study materials',
    is_active: true
  },
  {
    name: 'Attending Physician',
    description: 'Senior medical professional with teaching and content creation rights',
    is_active: true
  }
];

export const getRoleById = (roles: Role[], id: string): Role | undefined => {
  return roles.find(role => role.id === id);
};

export const getRoleByName = (roles: Role[], name: string): Role | undefined => {
  return roles.find(role => role.name === name);
};

export const getRoleColor = (roleId: string): string => {
  const role = getRoleById(ROLES, roleId);
  return role?.color || 'gray';
}; 