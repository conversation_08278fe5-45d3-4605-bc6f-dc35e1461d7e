import React from 'react';
import {
    <PERSON>,
    Card,
    CardContent,
    Grid,
    Typography,
    CircularProgress,
    LinearProgress,
    Chip,
    List,
    ListItem,
    ListItemText,
    ListItemIcon,
} from '@mui/material';
import {
    Timeline,
    TimelineItem,
    TimelineSeparator,
    TimelineConnector,
    TimelineContent,
    TimelineDot,
} from '@mui/lab';
import {
    TrendingUp,
    AccessTime,
    School,
    EmojiEvents,
    Warning,
    CheckCircle,
} from '@mui/icons-material';
import { useAnalytics } from '../hooks/useAnalytics';
import {
    LineChart,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    ResponsiveContainer,
    Pie<PERSON>hart,
    Pie,
    Cell,
} from 'recharts';
import { useAnalytics } from '@/hooks/useAnalytics';
import { CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle } from 'lucide-react';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

export function AnalyticsDashboard() {
    const {
        learningPatterns,
        performancePredictions,
        studyRecommendations,
        isLoading,
        error,
    } = useAnalytics();

    if (error) {
        return (
            <div className="flex items-center justify-center p-4 text-red-600">
                <AlertCircle className="mr-2 h-5 w-5" />
                <span>{error.message}</span>
            </div>
        );
    }

    if (isLoading) {
        return <AnalyticsSkeleton />;
    }

    return (
        <div className="container mx-auto p-4">
            <Tabs defaultValue="overview" className="space-y-4">
                <TabsList>
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="patterns">Learning Patterns</TabsTrigger>
                    <TabsTrigger value="predictions">Performance</TabsTrigger>
                    <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        <Card>
                            <CardHeader>
                                <CardTitle>Learning Streak</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">
                                    {learningPatterns?.learningStreak.currentStreak} days
                                </div>
                                <p className="text-sm text-muted-foreground">
                                    Longest streak: {learningPatterns?.learningStreak.longestStreak} days
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Overall Performance</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">
                                    {performancePredictions?.overallScore.toFixed(1)}%
                                </div>
                                <Progress
                                    value={performancePredictions?.overallScore}
                                    className="mt-2"
                                />
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Recommended Topics</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <ul className="space-y-2">
                                    {studyRecommendations?.recommendedTopics
                                        .slice(0, 3)
                                        .map((topic) => (
                                            <li
                                                key={topic.topic}
                                                className="flex items-center justify-between"
                                            >
                                                <span>{topic.topic}</span>
                                                <span
                                                    className={`rounded-full px-2 py-1 text-xs ${
                                                        topic.priority === 'high'
                                                            ? 'bg-red-100 text-red-800'
                                                            : topic.priority === 'medium'
                                                            ? 'bg-yellow-100 text-yellow-800'
                                                            : 'bg-green-100 text-green-800'
                                                    }`}
                                                >
                                                    {topic.priority}
                                                </span>
                                            </li>
                                        ))}
                                </ul>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                <TabsContent value="patterns" className="space-y-4">
                    <Card>
                        <CardHeader>
                            <CardTitle>Topic Mastery</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {learningPatterns?.topicMastery.map((topic) => (
                                    <div key={topic.topic}>
                                        <div className="flex items-center justify-between">
                                            <span>{topic.topic}</span>
                                            <span className="text-sm text-muted-foreground">
                                                {topic.correctAnswers}/{topic.questionsAnswered} correct
                                            </span>
                                        </div>
                                        <Progress
                                            value={(topic.correctAnswers / topic.questionsAnswered) * 100}
                                            className="mt-2"
                                        />
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="predictions" className="space-y-4">
                    <Card>
                        <CardHeader>
                            <CardTitle>Performance Predictions</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {performancePredictions?.topicScores.map((topic) => (
                                    <div key={topic.topic}>
                                        <div className="flex items-center justify-between">
                                            <span>{topic.topic}</span>
                                            <span className="text-sm text-muted-foreground">
                                                {topic.predictedScore.toFixed(1)}% (confidence:{' '}
                                                {topic.confidence.toFixed(1)}%)
                                            </span>
                                        </div>
                                        <Progress value={topic.predictedScore} className="mt-2" />
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="recommendations" className="space-y-4">
                    <Card>
                        <CardHeader>
                            <CardTitle>Study Schedule</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {studyRecommendations?.studySchedule.map((day) => (
                                    <div key={day.day} className="border-b pb-4 last:border-0">
                                        <h3 className="font-semibold">{day.day}</h3>
                                        <ul className="mt-2 space-y-1">
                                            {day.topics.map((topic) => (
                                                <li key={topic} className="text-sm text-muted-foreground">
                                                    {topic}
                                                </li>
                                            ))}
                                        </ul>
                                        <p className="mt-2 text-sm text-muted-foreground">
                                            Estimated duration: {day.estimatedDuration} minutes
                                        </p>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Recommended Resources</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {studyRecommendations?.learningResources.map((resource) => (
                                    <div
                                        key={resource.title}
                                        className="flex items-start space-x-4 rounded-lg border p-4"
                                    >
                                        <div className="flex-1">
                                            <h3 className="font-semibold">{resource.title}</h3>
                                            <p className="mt-1 text-sm text-muted-foreground">
                                                {resource.description}
                                            </p>
                                            <a
                                                href={resource.url}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="mt-2 inline-block text-sm text-blue-600 hover:underline"
                                            >
                                                View Resource
                                            </a>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    );
}

function AnalyticsSkeleton() {
    return (
        <div className="container mx-auto p-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {[1, 2, 3].map((i) => (
                    <Card key={i}>
                        <CardHeader>
                            <Skeleton className="h-6 w-32" />
                        </CardHeader>
                        <CardContent>
                            <Skeleton className="h-8 w-24" />
                            <Skeleton className="mt-2 h-4 w-full" />
                        </CardContent>
                    </Card>
                ))}
            </div>
        </div>
    );
} 