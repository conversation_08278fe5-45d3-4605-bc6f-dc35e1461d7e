'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import api from '@/services/api';
import { BarChart, Calendar, CheckCircle, Clock, Target, Trophy } from 'lucide-react';

export default function ProgressPage() {
  const [progressData, setProgressData] = useState({
    overallProgress: 0,
    coursesCompleted: 0,
    totalCourses: 0,
    streak: 0,
    lastActivity: null,
    courseProgress: [],
    recentActivities: [],
    achievements: []
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchProgressData = async () => {
      try {
        setIsLoading(true);
        
        // In a real app, you would fetch this data from your API
        // For now, we'll use placeholder data
        
        const placeholderData = {
          overallProgress: 48,
          coursesCompleted: 7,
          totalCourses: 15,
          streak: 12,
          lastActivity: '2025-04-06T18:22:00Z',
          courseProgress: [
            { id: '1', title: 'Introduction to Anatomy', progress: 100, completed: true },
            { id: '2', title: 'Physiology Fundamentals', progress: 75, completed: false },
            { id: '3', title: 'Clinical Medicine', progress: 42, completed: false },
            { id: '4', title: 'Pathology Basics', progress: 65, completed: false },
            { id: '5', title: 'Medical Ethics', progress: 90, completed: false },
            { id: '6', title: 'Pharmacology', progress: 30, completed: false },
          ],
          recentActivities: [
            { id: '1', type: 'course', title: 'Completed quiz in Physiology Fundamentals', date: '2025-04-06T18:22:00Z' },
            { id: '2', type: 'material', title: 'Downloaded Anatomy Lecture Notes', date: '2025-04-06T15:45:00Z' },
            { id: '3', type: 'course', title: 'Watched video in Clinical Medicine', date: '2025-04-05T14:30:00Z' },
            { id: '4', type: 'course', title: 'Completed module in Pathology Basics', date: '2025-04-04T10:15:00Z' },
          ],
          achievements: [
            { id: '1', title: 'Fast Learner', description: 'Completed 5 courses', icon: 'trophy', date: '2025-03-15T09:30:00Z' },
            { id: '2', title: 'Consistent Student', description: 'Maintained a 10-day streak', icon: 'calendar', date: '2025-04-01T14:45:00Z' },
            { id: '3', title: 'Knowledge Seeker', description: 'Downloaded 20 study materials', icon: 'book', date: '2025-03-28T11:20:00Z' },
          ]
        };
        
        setProgressData(placeholderData);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching progress data:', error);
        setIsLoading(false);
      }
    };

    fetchProgressData();
  }, []);

  const formatDate = (dateString) => {
    const options = { 
      month: 'short', 
      day: 'numeric', 
      hour: '2-digit', 
      minute: '2-digit' 
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[80vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Your Progress</h1>

      {/* Progress Summary */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overall Progress</CardTitle>
            <Target className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{progressData.overallProgress}%</div>
            <div className="mt-2 h-2 w-full rounded-full bg-gray-200">
              <div
                className="h-2 rounded-full bg-blue-500"
                style={{ width: `${progressData.overallProgress}%` }}
              />
            </div>
            <p className="text-xs text-gray-500 mt-2">
              {progressData.coursesCompleted} out of {progressData.totalCourses} courses completed
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Learning Streak</CardTitle>
            <Calendar className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{progressData.streak} days</div>
            <p className="text-xs text-gray-500 mt-2">
              Keep up the good work!
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recent Activity</CardTitle>
            <Clock className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {progressData.lastActivity ? formatDate(progressData.lastActivity) : 'No activity yet'}
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Last learning session
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Achievements</CardTitle>
            <Trophy className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{progressData.achievements.length}</div>
            <p className="text-xs text-gray-500 mt-2">
              Badges earned
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Course Progress */}
      <div>
        <h2 className="text-xl font-bold mb-4">Course Progress</h2>
        <div className="space-y-4">
          {progressData.courseProgress.map((course) => (
            <Card key={course.id}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <h3 className="font-medium">{course.title}</h3>
                    {course.completed && (
                      <Badge variant="default" className="ml-2">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Completed
                      </Badge>
                    )}
                  </div>
                  <span className="text-sm font-medium">{course.progress}%</span>
                </div>
                <div className="h-2 w-full rounded-full bg-gray-200">
                  <div
                    className={`h-2 rounded-full ${course.completed ? 'bg-green-500' : 'bg-blue-500'}`}
                    style={{ width: `${course.progress}%` }}
                  />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Recent Activities */}
        <div>
          <h2 className="text-xl font-bold mb-4">Recent Activities</h2>
          <Card>
            <CardContent className="p-4">
              <ul className="space-y-4">
                {progressData.recentActivities.map((activity) => (
                  <li key={activity.id} className="flex items-start">
                    <div className="flex-shrink-0 h-4 w-4 rounded-full bg-blue-500 mt-1"></div>
                    <div className="ml-3">
                      <p className="text-sm font-medium">{activity.title}</p>
                      <p className="text-xs text-gray-500">{formatDate(activity.date)}</p>
                    </div>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Achievements */}
        <div>
          <h2 className="text-xl font-bold mb-4">Achievements</h2>
          <Card>
            <CardContent className="p-4">
              <ul className="space-y-4">
                {progressData.achievements.map((achievement) => (
                  <li key={achievement.id} className="flex items-center p-2 border rounded-lg">
                    <div className="flex-shrink-0 h-10 w-10 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-600">
                      <Trophy className="h-5 w-5" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium">{achievement.title}</p>
                      <p className="text-xs text-gray-500">{achievement.description}</p>
                    </div>
                    <div className="ml-auto text-xs text-gray-500">
                      {formatDate(achievement.date)}
                    </div>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
