import { NextResponse } from 'next/server';

export async function GET(
    request: Request,
    { params }: { params: { token: string } }
) {
    try {
        const response = await fetch(`http://localhost:3002/v1/security/email/verify/${params.token}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });

        const data = await response.json();

        if (!response.ok) {
            return NextResponse.json(
                { 
                    message: data.message || 'Failed to verify email' 
                }, 
                { status: response.status }
            );
        }

        return NextResponse.json(data);
    } catch (error) {
        console.error('Email verification error:', error);
        return NextResponse.json(
            { message: 'Failed to verify email' },
            { status: 500 }
        );
    }
}
