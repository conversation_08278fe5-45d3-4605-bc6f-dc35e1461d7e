{"version": 3, "sources": ["../../src/subscriber/EntitySubscriberInterface.ts"], "names": [], "mappings": "", "file": "EntitySubscriberInterface.js", "sourcesContent": ["import { TransactionCommitEvent } from \"./event/TransactionCommitEvent\"\nimport { TransactionRollbackEvent } from \"./event/TransactionRollbackEvent\"\nimport { TransactionStartEvent } from \"./event/TransactionStartEvent\"\nimport { UpdateEvent } from \"./event/UpdateEvent\"\nimport { RemoveEvent } from \"./event/RemoveEvent\"\nimport { InsertEvent } from \"./event/InsertEvent\"\nimport { LoadEvent } from \"./event/LoadEvent\"\nimport { SoftRemoveEvent } from \"./event/SoftRemoveEvent\"\nimport { RecoverEvent } from \"./event/RecoverEvent\"\nimport { AfterQueryEvent, BeforeQueryEvent } from \"./event/QueryEvent\"\n\n/**\n * Classes that implement this interface are subscribers that subscribe for the specific events in the ORM.\n */\nexport interface EntitySubscriberInterface<Entity = any> {\n    /**\n     * Returns the class of the entity to which events will listen.\n     * If this method is omitted, then subscriber will listen to events of all entities.\n     */\n    listenTo?(): Function | string\n\n    /**\n     * Called after entity is loaded from the database.\n     *\n     * For backward compatibility this signature is slightly different from the\n     * others.  `event` was added later but is always provided (it is only\n     * optional in the signature so that its introduction does not break\n     * compilation for existing subscribers).\n     */\n    afterLoad?(entity: Entity, event?: LoadEvent<Entity>): Promise<any> | void\n\n    /**\n     * Called before query is executed.\n     */\n    beforeQuery?(event: BeforeQueryEvent<Entity>): Promise<any> | void\n\n    /**\n     * Called after query is executed.\n     */\n    afterQuery?(event: AfterQueryEvent<Entity>): Promise<any> | void\n\n    /**\n     * Called before entity is inserted to the database.\n     */\n    beforeInsert?(event: InsertEvent<Entity>): Promise<any> | void\n\n    /**\n     * Called after entity is inserted to the database.\n     */\n    afterInsert?(event: InsertEvent<Entity>): Promise<any> | void\n\n    /**\n     * Called before entity is updated in the database.\n     */\n    beforeUpdate?(event: UpdateEvent<Entity>): Promise<any> | void\n\n    /**\n     * Called after entity is updated in the database.\n     */\n    afterUpdate?(event: UpdateEvent<Entity>): Promise<any> | void\n\n    /**\n     * Called before entity is removed from the database.\n     */\n    beforeRemove?(event: RemoveEvent<Entity>): Promise<any> | void\n\n    /**\n     * Called before entity is soft removed from the database.\n     */\n    beforeSoftRemove?(event: SoftRemoveEvent<Entity>): Promise<any> | void\n\n    /**\n     * Called before entity is recovered in the database.\n     */\n    beforeRecover?(event: RecoverEvent<Entity>): Promise<any> | void\n\n    /**\n     * Called after entity is removed from the database.\n     */\n    afterRemove?(event: RemoveEvent<Entity>): Promise<any> | void\n\n    /**\n     * Called after entity is soft removed from the database.\n     */\n    afterSoftRemove?(event: SoftRemoveEvent<Entity>): Promise<any> | void\n\n    /**\n     * Called after entity is recovered in the database.\n     */\n    afterRecover?(event: RecoverEvent<Entity>): Promise<any> | void\n\n    /**\n     * Called before transaction is started.\n     */\n    beforeTransactionStart?(event: TransactionStartEvent): Promise<any> | void\n\n    /**\n     * Called after transaction is started.\n     */\n    afterTransactionStart?(event: TransactionStartEvent): Promise<any> | void\n\n    /**\n     * Called before transaction is committed.\n     */\n    beforeTransactionCommit?(event: TransactionCommitEvent): Promise<any> | void\n\n    /**\n     * Called after transaction is committed.\n     */\n    afterTransactionCommit?(event: TransactionCommitEvent): Promise<any> | void\n\n    /**\n     * Called before transaction rollback.\n     */\n    beforeTransactionRollback?(\n        event: TransactionRollbackEvent,\n    ): Promise<any> | void\n\n    /**\n     * Called after transaction rollback.\n     */\n    afterTransactionRollback?(\n        event: TransactionRollbackEvent,\n    ): Promise<any> | void\n}\n"], "sourceRoot": ".."}