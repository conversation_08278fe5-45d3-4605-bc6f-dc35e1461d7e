/**
 * Notifications Module Tests
 * 
 * This file contains tests for the notifications module.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import api from '../services/api';

// Mock the API service
vi.mock('../services/api', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

// Create a simple notifications service for testing
const notificationsService = {
  async getNotifications() {
    try {
      const response = await api.get('/notifications');
      return response.data;
    } catch (error) {
      console.error('Error fetching notifications:', error);
      // Return mock notifications
      return [
        {
          id: '1',
          title: 'New course available',
          message: 'A new course on Cardiology has been added.',
          date: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
          read: false,
          type: 'info',
        },
        {
          id: '2',
          title: 'Quiz completed',
          message: 'You scored 85% on the Anatomy quiz.',
          date: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
          read: true,
          type: 'success',
        },
      ];
    }
  },
  
  async createNotification(notification: any) {
    try {
      const response = await api.post('/notifications', notification);
      return response.data;
    } catch (error) {
      console.error('Error creating notification:', error);
      // Return mock created notification
      return {
        id: Date.now().toString(),
        ...notification,
        date: new Date().toISOString(),
        read: false,
      };
    }
  },
  
  async markAsRead(notificationId: string) {
    try {
      const response = await api.put(`/notifications/${notificationId}/read`);
      return response.data;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      // Return mock success response
      return {
        success: true,
        id: notificationId,
        read: true,
      };
    }
  }
};

describe('Notifications Module', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Get Notifications', () => {
    it('should fetch notifications from API when available', async () => {
      const mockNotifications = [
        {
          id: '1',
          title: 'Test Notification',
          message: 'This is a test notification',
          date: '2025-04-28T12:00:00Z',
          read: false,
          type: 'info',
        },
      ];
      
      // Mock successful API response
      (api.get as any).mockResolvedValueOnce({ data: mockNotifications });
      
      const notifications = await notificationsService.getNotifications();
      
      expect(api.get).toHaveBeenCalledWith('/notifications');
      expect(notifications).toEqual(mockNotifications);
    });

    it('should return mock notifications when API fails', async () => {
      // Mock API failure
      (api.get as any).mockRejectedValueOnce(new Error('API error'));
      
      const notifications = await notificationsService.getNotifications();
      
      expect(api.get).toHaveBeenCalledWith('/notifications');
      expect(notifications).toBeDefined();
      expect(Array.isArray(notifications)).toBe(true);
      expect(notifications.length).toBeGreaterThan(0);
      expect(notifications[0]).toHaveProperty('id');
      expect(notifications[0]).toHaveProperty('title');
      expect(notifications[0]).toHaveProperty('message');
      expect(notifications[0]).toHaveProperty('date');
      expect(notifications[0]).toHaveProperty('read');
      expect(notifications[0]).toHaveProperty('type');
    });
  });

  describe('Create Notification', () => {
    it('should create notification via API when available', async () => {
      const notification = {
        title: 'New Notification',
        message: 'This is a new notification',
        type: 'info',
      };
      
      const mockResponse = {
        id: '3',
        ...notification,
        date: '2025-04-28T12:00:00Z',
        read: false,
      };
      
      // Mock successful API response
      (api.post as any).mockResolvedValueOnce({ data: mockResponse });
      
      const result = await notificationsService.createNotification(notification);
      
      expect(api.post).toHaveBeenCalledWith('/notifications', notification);
      expect(result).toEqual(mockResponse);
    });

    it('should return mock created notification when API fails', async () => {
      const notification = {
        title: 'New Notification',
        message: 'This is a new notification',
        type: 'info',
      };
      
      // Mock API failure
      (api.post as any).mockRejectedValueOnce(new Error('API error'));
      
      const result = await notificationsService.createNotification(notification);
      
      expect(api.post).toHaveBeenCalledWith('/notifications', notification);
      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.title).toBe(notification.title);
      expect(result.message).toBe(notification.message);
      expect(result.type).toBe(notification.type);
      expect(result.date).toBeDefined();
      expect(result.read).toBe(false);
    });
  });

  describe('Mark Notification as Read', () => {
    it('should mark notification as read via API when available', async () => {
      const mockResponse = {
        success: true,
        id: '1',
        read: true,
      };
      
      // Mock successful API response
      (api.put as any).mockResolvedValueOnce({ data: mockResponse });
      
      const result = await notificationsService.markAsRead('1');
      
      expect(api.put).toHaveBeenCalledWith('/notifications/1/read');
      expect(result).toEqual(mockResponse);
    });

    it('should return mock success response when API fails', async () => {
      // Mock API failure
      (api.put as any).mockRejectedValueOnce(new Error('API error'));
      
      const result = await notificationsService.markAsRead('1');
      
      expect(api.put).toHaveBeenCalledWith('/notifications/1/read');
      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.id).toBe('1');
      expect(result.read).toBe(true);
    });
  });
});
