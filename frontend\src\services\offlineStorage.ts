import { openDB, DBSchema, IDBPDatabase } from 'idb';

interface ProgressDB extends DBSchema {
  progress: {
    key: string;
    value: {
      id: string;
      userId: string;
      courseId: string;
      unitId: string;
      status: 'not_started' | 'in_progress' | 'completed';
      score?: number;
      lastAccessed: Date;
      synced: boolean;
      lastModified: Date;
    };
    indexes: {
      'by-user': string;
      'by-course': string;
      'by-unit': string;
      'by-sync-status': boolean;
    };
  };
  syncQueue: {
    key: string;
    value: {
      id: string;
      type: 'create' | 'update' | 'delete';
      data: any;
      timestamp: Date;
      retryCount: number;
    };
  };
}

class OfflineStorage {
  private db: IDBPDatabase<ProgressDB> | null = null;
  private readonly DB_NAME = 'medical-app-db';
  private readonly DB_VERSION = 1;

  async init() {
    this.db = await openDB<ProgressDB>(this.DB_NAME, this.DB_VERSION, {
      upgrade(db) {
        // Create progress store
        const progressStore = db.createObjectStore('progress', { keyPath: 'id' });
        progressStore.createIndex('by-user', 'userId');
        progressStore.createIndex('by-course', 'courseId');
        progressStore.createIndex('by-unit', 'unitId');
        progressStore.createIndex('by-sync-status', 'synced');

        // Create sync queue store
        db.createObjectStore('syncQueue', { keyPath: 'id' });
      },
    });
  }

  async saveProgress(progress: ProgressDB['progress']['value']) {
    if (!this.db) await this.init();
    const tx = this.db!.transaction('progress', 'readwrite');
    await tx.store.put({
      ...progress,
      synced: false,
      lastModified: new Date(),
    });
    await tx.done;
  }

  async getProgress(userId: string) {
    if (!this.db) await this.init();
    const tx = this.db!.transaction('progress', 'readonly');
    const index = tx.store.index('by-user');
    return index.getAll(userId);
  }

  async getUnsyncedProgress() {
    if (!this.db) await this.init();
    const tx = this.db!.transaction('progress', 'readonly');
    const index = tx.store.index('by-sync-status');
    return index.getAll(false);
  }

  async markAsSynced(progressId: string) {
    if (!this.db) await this.init();
    const tx = this.db!.transaction('progress', 'readwrite');
    const progress = await tx.store.get(progressId);
    if (progress) {
      progress.synced = true;
      await tx.store.put(progress);
    }
    await tx.done;
  }

  async addToSyncQueue(action: ProgressDB['syncQueue']['value']) {
    if (!this.db) await this.init();
    const tx = this.db!.transaction('syncQueue', 'readwrite');
    await tx.store.put(action);
    await tx.done;
  }

  async getSyncQueue() {
    if (!this.db) await this.init();
    const tx = this.db!.transaction('syncQueue', 'readonly');
    return tx.store.getAll();
  }

  async removeFromSyncQueue(id: string) {
    if (!this.db) await this.init();
    const tx = this.db!.transaction('syncQueue', 'readwrite');
    await tx.store.delete(id);
    await tx.done;
  }

  async incrementRetryCount(id: string) {
    if (!this.db) await this.init();
    const tx = this.db!.transaction('syncQueue', 'readwrite');
    const action = await tx.store.get(id);
    if (action) {
      action.retryCount += 1;
      await tx.store.put(action);
    }
    await tx.done;
  }
}

export const offlineStorage = new OfflineStorage(); 