import { Connection } from 'typeorm';
import { INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { AppModule } from '../../src/app.module';
import { TestConfig } from 'test/test.config';

export class TestHelper {
  public static async createTestingApp(): Promise<INestApplication> {
    const moduleRef = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    const app = moduleRef.createNestApplication();
    await app.init();
    return app;
  }

  public static async cleanupDatabase(connection: Connection): Promise<void> {
    const entities = connection.entityMetadatas;
    for (const entity of entities) {
      const repository = connection.getRepository(entity.name);
      await repository.clear();
    }
  }

  public static async closeTestingApp(app: INestApplication): Promise<void> {
    await app.close();
  }

  public static async resetDatabase(connection: Connection): Promise<void> {
    await this.cleanupDatabase(connection);
    await connection.synchronize(true);
  }

  public static async getConnection(): Promise<Connection> {
    const app = await this.createTestingApp();
    return app.get(Connection);
  }

  public static async getDatabaseConnection(): Promise<Connection> {
    const app = await this.createTestingApp();
    return app.get(Connection);
  }


}