// src/common/guards/email-verification.guard.ts
import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { SecurityService } from '../../modules/auth/security.service';

@Injectable()
export class EmailVerificationGuard implements CanActivate {
  constructor(private securityService: SecurityService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new UnauthorizedException('User not authenticated');
    }

    const isVerified = await this.securityService.isEmailVerified(user.id);
    
    if (!isVerified) {
      throw new UnauthorizedException('Email verification required');
    }

    return true;
  }
}