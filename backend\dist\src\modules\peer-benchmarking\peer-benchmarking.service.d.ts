import { Repository } from 'typeorm';
import { TopicProgress } from '../../entities/topic-progress.entity';
import { StudySession } from '../../entities/study-session.entity';
import { User } from '../../entities/user.entity';
export declare class PeerBenchmarkingService {
    private topicProgressRepository;
    private studySessionRepository;
    private userRepository;
    constructor(topicProgressRepository: Repository<TopicProgress>, studySessionRepository: Repository<StudySession>, userRepository: Repository<User>);
    getPeerStats(userId: string, topicId: string): Promise<{
        user_progress: {
            completion_percentage: number;
            time_spent_minutes: number;
            streak_days: number;
        };
        peer_averages: {
            completion_percentage: number;
            time_spent_minutes: number;
            streak_days: number;
        };
        percentile: number;
    }>;
    getLeaderboard(topicId: string, limit?: number): Promise<TopicProgress[]>;
    getStudyGroupStats(groupId: string): Promise<{
        average_completion: number;
        average_streak: number;
        total_study_time: number;
        member_count: number;
    }>;
    private calculateAverage;
    private calculatePercentile;
}
