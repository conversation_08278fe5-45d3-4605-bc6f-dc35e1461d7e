{"version": 3, "sources": ["../../src/util/ObjectUtils.ts"], "names": [], "mappings": ";;;AAEA,MAAa,WAAW;IACpB;;;;OAIG;IACH,MAAM,CAAC,QAAQ,CAAC,GAAQ;QACpB,OAAO,GAAG,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,CAAA;IAClD,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,gBAAgB,CAAC,GAAQ;QAC5B,OAAO,CACH,GAAG,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,MAAM,CAAC,KAAK,SAAS,CACvE,CAAA;IACL,CAAC;IAkCD;;;;;OAKG;IACH,MAAM,CAAC,MAAM,CAAC,MAAc,EAAE,GAAG,OAAc;QAC3C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpD,CAAC;gBAAC,MAAc,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;YACzC,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAI,IAAkB;QACzC,IAAI,IAAI,KAAK,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC5C,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CACxB,CAAC,GAAG,EAAE,EAAE,CAAE,IAA6B,CAAC,GAAG,CAAC,CAC/C,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,CAAA;QACf,CAAC;IACL,CAAC;CACJ;AA/ED,kCA+EC", "file": "ObjectUtils.js", "sourcesContent": ["import { MixedList } from \"../common/MixedList\"\n\nexport class ObjectUtils {\n    /**\n     * Checks if given value is an object.\n     * We cannot use instanceof because it has problems when running on different contexts.\n     * And we don't simply use typeof because typeof null === \"object\".\n     */\n    static isObject(val: any): val is object {\n        return val !== null && typeof val === \"object\"\n    }\n\n    /**\n     * Checks if given value is an object.\n     * We cannot use instanceof because it has problems when running on different contexts.\n     * And we don't simply use typeof because typeof null === \"object\".\n     */\n    static isObjectWithName(val: any): val is object & { name: string } {\n        return (\n            val !== null && typeof val === \"object\" && val[\"name\"] !== undefined\n        )\n    }\n\n    /**\n     * Copy the values of all of the enumerable own properties from one or more source objects to a\n     * target object.\n     * @param target The target object to copy to.\n     * @param source The source object from which to copy properties.\n     */\n    static assign<T, U>(target: T, source: U): void\n\n    /**\n     * Copy the values of all of the enumerable own properties from one or more source objects to a\n     * target object.\n     * @param target The target object to copy to.\n     * @param source1 The first source object from which to copy properties.\n     * @param source2 The second source object from which to copy properties.\n     */\n    static assign<T, U, V>(target: T, source1: U, source2: V): void\n\n    /**\n     * Copy the values of all of the enumerable own properties from one or more source objects to a\n     * target object.\n     * @param target The target object to copy to.\n     * @param source1 The first source object from which to copy properties.\n     * @param source2 The second source object from which to copy properties.\n     * @param source3 The third source object from which to copy properties.\n     */\n    static assign<T, U, V, W>(\n        target: T,\n        source1: U,\n        source2: V,\n        source3: W,\n    ): void\n\n    /**\n     * Copy the values of all of the enumerable own properties from one or more source objects to a\n     * target object.\n     * @param target The target object to copy to.\n     * @param sources One or more source objects from which to copy properties\n     */\n    static assign(target: object, ...sources: any[]): void {\n        for (const source of sources) {\n            for (const prop of Object.getOwnPropertyNames(source)) {\n                ;(target as any)[prop] = source[prop]\n            }\n        }\n    }\n\n    /**\n     * Converts MixedList<T> to strictly an array of its T items.\n     */\n    static mixedListToArray<T>(list: MixedList<T>): T[] {\n        if (list !== null && typeof list === \"object\") {\n            return Object.keys(list).map(\n                (key) => (list as { [key: string]: T })[key],\n            )\n        } else {\n            return list\n        }\n    }\n}\n"], "sourceRoot": ".."}