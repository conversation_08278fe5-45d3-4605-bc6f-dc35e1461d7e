import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../redis/redis.service';
import { UserFeaturesService, UserFeatureProfile } from './user-features.service';

export interface ContentFeatures {
  contentId: string;
  contentType: string;
  difficulty: number;
  topics: string[];
  duration: number;
  engagement: number;
  completionRate: number;
  averageScore: number;
  prerequisites: string[];
  learningObjectives: string[];
  metadata: Record<string, any>;
}

export interface AIRecommendationInput {
  userId: string;
  userFeatures: number[];
  contentFeatures: ContentFeatures[];
  contextFeatures: number[];
  timestamp: Date;
}

export interface AIModelPrediction {
  contentId: string;
  score: number;
  confidence: number;
  reasoning: string[];
  category: string;
}

export interface FeatureImportance {
  featureName: string;
  importance: number;
  category: 'user' | 'content' | 'context';
}

@Injectable()
export class AIFeaturesService {
  private readonly logger = new Logger(AIFeaturesService.name);
  private readonly CACHE_TTL = 1800; // 30 minutes
  private readonly CONTENT_FEATURES_PREFIX = 'content_features:';
  private readonly MODEL_CACHE_PREFIX = 'ai_model:';

  constructor(
    private readonly redisService: RedisService,
    private readonly userFeaturesService: UserFeaturesService,
  ) {}

  /**
   * Extract content features for AI model
   */
  async extractContentFeatures(contentId: string, contentData: any): Promise<ContentFeatures> {
    try {
      const cacheKey = `${this.CONTENT_FEATURES_PREFIX}${contentId}`;
      const cached = await this.redisService.get(cacheKey);
      
      if (cached) {
        return JSON.parse(cached);
      }

      const features = await this.generateContentFeatures(contentId, contentData);
      
      await this.redisService.set(cacheKey, JSON.stringify(features), this.CACHE_TTL * 4); // 2 hours
      
      return features;
    } catch (error) {
      this.logger.error(`Failed to extract content features for ${contentId}:`, error);
      throw error;
    }
  }

  /**
   * Generate AI model input combining user, content, and context features
   */
  async generateAIInput(
    userId: string,
    candidateContentIds: string[],
    context?: Record<string, any>
  ): Promise<AIRecommendationInput> {
    try {
      // Get user features
      const userFeatures = await this.userFeaturesService.getUserFeatureVector(userId);
      if (!userFeatures) {
        throw new Error(`Unable to generate user features for ${userId}`);
      }

      // Get content features for all candidates
      const contentFeatures = await Promise.all(
        candidateContentIds.map(async (contentId) => {
          // In a real app, you'd fetch content data from your database
          const contentData = await this.getContentData(contentId);
          return this.extractContentFeatures(contentId, contentData);
        })
      );

      // Generate context features
      const contextFeatures = this.generateContextFeatures(userId, context);

      return {
        userId,
        userFeatures,
        contentFeatures,
        contextFeatures,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to generate AI input for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Create feature matrix for batch processing
   */
  async createFeatureMatrix(
    userIds: string[],
    contentIds: string[],
    context?: Record<string, any>
  ): Promise<{
    userFeatureMatrix: number[][];
    contentFeatureMatrix: number[][];
    contextFeatureMatrix: number[][];
    userIds: string[];
    contentIds: string[];
  }> {
    try {
      // Get user feature vectors
      const userFeatureMatrix = await Promise.all(
        userIds.map(async (userId) => {
          const features = await this.userFeaturesService.getUserFeatureVector(userId);
          return features || new Array(17).fill(0); // Default to zeros if no features
        })
      );

      // Get content feature vectors
      const contentFeatureMatrix = await Promise.all(
        contentIds.map(async (contentId) => {
          const contentData = await this.getContentData(contentId);
          const features = await this.extractContentFeatures(contentId, contentData);
          return this.contentFeaturesToVector(features);
        })
      );

      // Generate context features for each user
      const contextFeatureMatrix = userIds.map(userId => 
        this.generateContextFeatures(userId, context)
      );

      return {
        userFeatureMatrix,
        contentFeatureMatrix,
        contextFeatureMatrix,
        userIds,
        contentIds,
      };
    } catch (error) {
      this.logger.error('Failed to create feature matrix:', error);
      throw error;
    }
  }

  /**
   * Simulate AI model prediction (replace with actual ML model)
   */
  async predictRecommendations(input: AIRecommendationInput): Promise<AIModelPrediction[]> {
    try {
      const cacheKey = `${this.MODEL_CACHE_PREFIX}${input.userId}:${input.contentFeatures.map(c => c.contentId).join(',')}`;
      const cached = await this.redisService.get(cacheKey);
      
      if (cached) {
        return JSON.parse(cached);
      }

      // Simulate ML model predictions
      const predictions = await this.simulateMLPredictions(input);
      
      await this.redisService.set(cacheKey, JSON.stringify(predictions), this.CACHE_TTL);
      
      return predictions;
    } catch (error) {
      this.logger.error('Failed to generate predictions:', error);
      throw error;
    }
  }

  /**
   * Analyze feature importance for model interpretability
   */
  async analyzeFeatureImportance(
    userId: string,
    contentIds: string[]
  ): Promise<FeatureImportance[]> {
    try {
      const input = await this.generateAIInput(userId, contentIds);
      
      // Simulate feature importance analysis
      // In a real app, this would use SHAP values or similar
      const userFeatures = input.userFeatures.map((value, index) => ({
        featureName: this.getUserFeatureName(index),
        importance: Math.abs(value),
        category: 'user' as const,
      }));

      const contentFeatures = input.contentFeatures.flatMap(content => 
        this.contentFeaturesToVector(content).map((value, index) => ({
          featureName: this.getContentFeatureName(index),
          importance: Math.abs(value),
          category: 'content' as const,
        }))
      );

      const contextFeatures = input.contextFeatures.map((value, index) => ({
        featureName: this.getContextFeatureName(index),
        importance: Math.abs(value),
        category: 'context' as const,
      }));

      return [...userFeatures, ...contentFeatures, ...contextFeatures]
        .sort((a, b) => b.importance - a.importance);
    } catch (error) {
      this.logger.error('Failed to analyze feature importance:', error);
      throw error;
    }
  }

  // Helper methods
  private async getContentData(contentId: string): Promise<any> {
    // In a real app, this would fetch from your database
    return {
      id: contentId,
      type: 'article',
      difficulty: 0.5,
      topics: ['math', 'science'],
      duration: 30,
      metadata: {
        device: 'desktop',
        topic: 'algebra',
      },
    };
  }

  private async generateContentFeatures(contentId: string, contentData: any): Promise<ContentFeatures> {
    // In a real app, this would extract features from your content data
    return {
      contentId,
      contentType: contentData.type,
      difficulty: contentData.difficulty,
      topics: contentData.topics,
      duration: contentData.duration,
      engagement: 0.7, // Example value
      completionRate: 0.8, // Example value
      averageScore: 0.75, // Example value
      prerequisites: [], // Example value
      learningObjectives: [], // Example value
      metadata: contentData.metadata,
    };
  }

  private generateContextFeatures(userId: string, context?: Record<string, any>): number[] {
    // In a real app, this would generate context features based on user state and environment
    return [
      context?.timeOfDay || 0.5,
      context?.dayOfWeek || 0.5,
      context?.deviceType || 0.5,
      context?.location || 0.5,
    ];
  }

  private contentFeaturesToVector(features: ContentFeatures): number[] {
    return [
      features.difficulty,
      features.duration / 3600, // Normalize to hours
      features.engagement,
      features.completionRate,
      features.averageScore,
      features.topics.length / 10, // Normalize
      features.prerequisites.length / 5, // Normalize
      features.learningObjectives.length / 5, // Normalize
    ];
  }

  private async simulateMLPredictions(input: AIRecommendationInput): Promise<AIModelPrediction[]> {
    // Simulate ML model predictions
    // In a real app, this would use your trained model
    return input.contentFeatures.map(content => ({
      contentId: content.contentId,
      score: Math.random(), // Simulated prediction score
      confidence: 0.8 + Math.random() * 0.2, // Simulated confidence
      reasoning: [
        'Based on user learning style',
        'Matches user difficulty preference',
        'Relevant to user interests',
      ],
      category: content.contentType,
    }));
  }

  private getUserFeatureName(index: number): string {
    const names = [
      'Session Duration',
      'Learning Velocity',
      'Difficulty Progression',
      'Engagement Score',
      'Time Slot Preference',
      'Weak Areas Count',
      'Strong Areas Count',
      'Total Sessions',
      'Average Score',
      'Completion Rate',
      'Retention Rate',
      'Streak Days',
      'Days Since Last Active',
      'Social Engagement',
      'Help Seeking',
      'Top Preference Score',
      'Preference Diversity',
    ];
    return names[index] || `User Feature ${index}`;
  }

  private getContentFeatureName(index: number): string {
    const names = [
      'Difficulty',
      'Duration',
      'Engagement',
      'Completion Rate',
      'Average Score',
      'Topic Count',
      'Prerequisite Count',
      'Learning Objective Count',
    ];
    return names[index] || `Content Feature ${index}`;
  }

  private getContextFeatureName(index: number): string {
    const names = [
      'Time of Day',
      'Day of Week',
      'Device Type',
      'Location',
    ];
    return names[index] || `Context Feature ${index}`;
  }
} 