// src/dto/auth/login.dto.ts
import { IsEmail, IsNotEmpty, MinLength, IsString, IsBoolean, IsOptional, ValidateIf } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class LoginDto {
  @ValidateIf(o => !o.username)
  @IsEmail()
  @IsNotEmpty()
  email?: string;

  @ValidateIf(o => !o.email)
  @IsString()
  @IsNotEmpty()
  username?: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(4) // Reduced minimum length to allow test accounts
  password: string;
  @ApiProperty({ description: 'Device identifier', required: false })
  @IsString()
  @IsOptional()
  deviceId?: string;

  @ApiProperty({ description: 'Two-factor authentication token', required: false })
  @IsString()
  @IsOptional()
  twoFactorToken?: string;

  @ApiProperty({ description: 'Remember user session', required: false })
  @IsBoolean()
  @IsOptional()
  rememberMe?: boolean;

  @ApiProperty({ description: 'Device name', required: false })
  @IsString()
  @IsOptional()
  deviceName?: string;
  @ApiProperty({ description: 'Device type (mobile, desktop, tablet)', required: false })
  @IsString()
  @IsOptional()
  deviceType?: string;

  @ApiProperty({ description: 'Device operating system', required: false })
  @IsString()
  @IsOptional()
  deviceOs?: string;

  @ApiProperty({ description: 'Browser name', required: false })
  @IsString()
  @IsOptional()
  deviceBrowser?: string;

  @ApiProperty({ description: 'Browser version', required: false })
  @IsString()
  @IsOptional()
  deviceBrowserVersion?: string;

  @ApiProperty({ description: 'Device push notification token', required: false })
  @IsString()
  @IsOptional()
  deviceToken?: string;

  @ApiProperty({ description: 'Browser user agent string', required: false })
  @IsString()
  @IsOptional()
  userAgent?: string;

  @ApiProperty({ description: 'Client IP address', required: false })
  @IsString()
  @IsOptional()
  ipAddress?: string;
}

