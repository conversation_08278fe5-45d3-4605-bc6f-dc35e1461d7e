import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Progress } from '../entities/progress.entity';

@Injectable()
export class ProgressService {
    constructor(
        @InjectRepository(Progress)
        private progressRepository: Repository<Progress>,
    ) {}

    async updateProgress(userId: string, unitId: string, status: string): Promise<Progress> {
        let progress = await this.progressRepository.findOne({
            where: { user: { id: userId }, unit: { id: unitId } }
        });

        if (!progress) {
            progress = this.progressRepository.create({
                user: { id: userId },
                unit: { id: unitId },
                status: status,
                last_accessed: new Date()
            });
        } else {
            progress.status = status;
            progress.last_accessed = new Date();
        }

        return this.progressRepository.save(progress);
    }

    async getProgressByUser(userId: string): Promise<Progress[]> {
        return this.progressRepository.find({
            where: { user: { id: userId } },
            relations: ['unit']
        });
    }

    async calculateOverallProgress(userId: string): Promise<number> {
        const allProgress = await this.getProgressByUser(userId);
        if (!allProgress.length) return 0;

        const completedUnits = allProgress.filter(p => p.status === 'complete').length;
        return (completedUnits / allProgress.length) * 100;
    }
}