import React from 'react';
import {
    <PERSON>,
    Card,
    CardContent,
    Typography,
    LinearProgress,
    Grid,
    Chip,
    List,
    ListItem,
    ListItemText,
    ListItemIcon,
    Divider,
} from '@mui/material';
import {
    Timeline,
    TimelineItem,
    TimelineSeparator,
    TimelineConnector,
    TimelineContent,
    TimelineDot,
} from '@mui/lab';
import {
    School as SchoolIcon,
    Assignment as AssignmentIcon,
    Update as UpdateIcon,
    CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';

interface CPDActivity {
    id: string;
    type: string;
    points: number;
    date: Date;
    description: string;
    isVerified: boolean;
    unit?: {
        id: string;
        name: string;
    };
}

interface CPDCycle {
    id: string;
    startDate: Date;
    endDate: Date;
    targetPoints: number;
    earnedPoints: number;
    unitProgress: {
        unitId: string;
        unitName: string;
        requiredPoints: number;
        earnedPoints: number;
    }[];
}

interface CPDDashboardProps {
    activities: CPDActivity[];
    currentCycle: CPDCycle;
    upcomingDeadlines: {
        type: string;
        description: string;
        dueDate: Date;
    }[];
}

export const CPDDashboard: React.FC<CPDDashboardProps> = ({
    activities,
    currentCycle,
    upcomingDeadlines,
}) => {
    const theme = useTheme();

    const progress = (currentCycle.earnedPoints / currentCycle.targetPoints) * 100;

    return (
        <Box sx={{ p: 3 }}>
            <Grid container spacing={3}>
                {/* CPD Progress Card */}
                <Grid item xs={12} md={6}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                CPD Progress
                            </Typography>
                            <Box sx={{ mb: 2 }}>
                                <Typography variant="body2" color="textSecondary">
                                    {currentCycle.earnedPoints} / {currentCycle.targetPoints} points
                                </Typography>
                                <LinearProgress
                                    variant="determinate"
                                    value={progress}
                                    sx={{ height: 10, borderRadius: 5, mt: 1 }}
                                />
                            </Box>
                            <Typography variant="subtitle2" gutterBottom>
                                Unit Progress
                            </Typography>
                            <List>
                                {currentCycle.unitProgress.map((unit, index) => (
                                    <React.Fragment key={unit.unitId}>
                                        <ListItem>
                                            <ListItemText
                                                primary={unit.unitName}
                                                secondary={`${unit.earnedPoints} / ${unit.requiredPoints} points`}
                                            />
                                            <LinearProgress
                                                variant="determinate"
                                                value={(unit.earnedPoints / unit.requiredPoints) * 100}
                                                sx={{ width: 100, height: 6, borderRadius: 3 }}
                                            />
                                        </ListItem>
                                        {index < currentCycle.unitProgress.length - 1 && <Divider />}
                                    </React.Fragment>
                                ))}
                            </List>
                        </CardContent>
                    </Card>
                </Grid>

                {/* Recent Activities Card */}
                <Grid item xs={12} md={6}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                Recent Activities
                            </Typography>
                            <Timeline>
                                {activities.map((activity) => (
                                    <TimelineItem key={activity.id}>
                                        <TimelineSeparator>
                                            <TimelineDot color={activity.isVerified ? "success" : "primary"}>
                                                {activity.isVerified ? <CheckCircleIcon /> : <AssignmentIcon />}
                                            </TimelineDot>
                                            <TimelineConnector />
                                        </TimelineSeparator>
                                        <TimelineContent>
                                            <Typography variant="subtitle2">
                                                {activity.description}
                                            </Typography>
                                            <Typography variant="body2" color="textSecondary">
                                                {new Date(activity.date).toLocaleDateString()}
                                            </Typography>
                                            <Box sx={{ mt: 1, display: 'flex', gap: 1 }}>
                                                <Chip
                                                    size="small"
                                                    label={`${activity.points} points`}
                                                />
                                                {activity.unit && (
                                                    <Chip
                                                        size="small"
                                                        label={activity.unit.name}
                                                        variant="outlined"
                                                    />
                                                )}
                                            </Box>
                                        </TimelineContent>
                                    </TimelineItem>
                                ))}
                            </Timeline>
                        </CardContent>
                    </Card>
                </Grid>

                {/* Upcoming Deadlines Card */}
                <Grid item xs={12}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                Upcoming Deadlines
                            </Typography>
                            <List>
                                {upcomingDeadlines.map((deadline, index) => (
                                    <React.Fragment key={index}>
                                        <ListItem>
                                            <ListItemIcon>
                                                <UpdateIcon color="warning" />
                                            </ListItemIcon>
                                            <ListItemText
                                                primary={deadline.description}
                                                secondary={new Date(deadline.dueDate).toLocaleDateString()}
                                            />
                                        </ListItem>
                                        {index < upcomingDeadlines.length - 1 && <Divider />}
                                    </React.Fragment>
                                ))}
                            </List>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>
        </Box>
    );
}; 