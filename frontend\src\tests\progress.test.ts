/**
 * Progress Module Tests
 * 
 * This file contains tests for the progress module.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import mockApiService from '../services/mockApiService';
import api from '../services/api';

// Mock the API service
vi.mock('../services/api', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

// Create a simple progress service for testing
const progressService = {
  async getUserProgress(userId: string) {
    try {
      const response = await api.get(`/progress/user/${userId}`);
      return response.data;
    } catch (error) {
      console.warn('Error fetching user progress from API, using mock data:', error);
      return mockApiService.getUserProgress(userId);
    }
  },
  
  async updateProgress(unitId: string, data: { completed: boolean }) {
    try {
      const response = await api.post(`/progress/${unitId}`, data);
      return response.data;
    } catch (error) {
      console.warn('Error updating progress via API, using mock data:', error);
      return mockApiService.updateProgress(unitId, data);
    }
  }
};

describe('Progress Module', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Get User Progress', () => {
    it('should fetch user progress from API when available', async () => {
      const mockProgress = [
        { id: '1', userId: '1', unitId: '1', completed: true },
        { id: '2', userId: '1', unitId: '2', completed: false },
      ];
      
      // Mock successful API response
      (api.get as any).mockResolvedValueOnce({ data: mockProgress });
      
      const progress = await progressService.getUserProgress('1');
      
      expect(api.get).toHaveBeenCalledWith('/progress/user/1');
      expect(progress).toEqual(mockProgress);
    });

    it('should fall back to mock API when real API fails', async () => {
      // Mock API failure
      (api.get as any).mockRejectedValueOnce(new Error('API error'));
      
      // Spy on mock API
      const mockGetUserProgress = vi.spyOn(mockApiService, 'getUserProgress');
      
      const progress = await progressService.getUserProgress('1');
      
      expect(api.get).toHaveBeenCalledWith('/progress/user/1');
      expect(mockGetUserProgress).toHaveBeenCalledWith('1');
      expect(progress.length).toBeGreaterThan(0);
    });
  });

  describe('Update Progress', () => {
    it('should update progress via API when available', async () => {
      const progressData = { completed: true };
      
      const mockResponse = {
        id: '1',
        userId: '1',
        unitId: '1',
        completed: true,
        completedAt: '2025-04-28T12:00:00Z',
      };
      
      // Mock successful API response
      (api.post as any).mockResolvedValueOnce({ data: mockResponse });
      
      const result = await progressService.updateProgress('1', progressData);
      
      expect(api.post).toHaveBeenCalledWith('/progress/1', progressData);
      expect(result).toEqual(mockResponse);
    });

    it('should fall back to mock API when real API fails', async () => {
      const progressData = { completed: true };
      
      // Mock API failure
      (api.post as any).mockRejectedValueOnce(new Error('API error'));
      
      // Spy on mock API
      const mockUpdateProgress = vi.spyOn(mockApiService, 'updateProgress');
      
      const result = await progressService.updateProgress('1', progressData);
      
      expect(api.post).toHaveBeenCalledWith('/progress/1', progressData);
      expect(mockUpdateProgress).toHaveBeenCalledWith('1', progressData);
      expect(result.completed).toBe(progressData.completed);
    });
  });

  describe('Mock API Progress', () => {
    it('should return user progress from mock API', async () => {
      const progress = await mockApiService.getUserProgress('1');
      
      expect(progress).toBeDefined();
      expect(Array.isArray(progress)).toBe(true);
      expect(progress.length).toBeGreaterThan(0);
      expect(progress[0]).toHaveProperty('id');
      expect(progress[0]).toHaveProperty('userId');
      expect(progress[0]).toHaveProperty('unitId');
      expect(progress[0]).toHaveProperty('completed');
    });

    it('should update progress with mock API', async () => {
      const progressData = { completed: true };
      
      const result = await mockApiService.updateProgress('1', progressData);
      
      expect(result).toBeDefined();
      expect(result.unitId).toBe('1');
      expect(result.completed).toBe(progressData.completed);
      expect(result.completedAt).toBeDefined();
    });
  });
});
