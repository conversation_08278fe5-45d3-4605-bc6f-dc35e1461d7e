import { Repository } from 'typeorm';
import { QuizAttempt } from '../../entities/quiz-attempt.entity';
import { Progress } from '../../entities/progress.entity';
import { SyncBulkDto } from './dto/sync-bulk.dto';
import { QuizAttemptSyncDto } from './dto/quiz-attempt-sync.dto';
import { ProgressSyncDto } from './dto/progress-sync.dto';
export declare class SyncService {
    private readonly quizAttemptRepository;
    private readonly progressRepository;
    constructor(quizAttemptRepository: Repository<QuizAttempt>, progressRepository: Repository<Progress>);
    syncBulk(userId: string, syncData: SyncBulkDto): Promise<{
        success: boolean;
        results: {
            quiz_attempts: {
                created: number;
                updated: number;
                conflicts: number;
            };
            progress: {
                created: number;
                updated: number;
                conflicts: number;
            };
        };
        timestamp: string;
    }>;
    processQuizAttempts(userId: string, attempts: QuizAttemptSyncDto[]): Promise<{
        created: number;
        updated: number;
        conflicts: number;
    }>;
    processProgress(userId: string, progress: ProgressSyncDto[]): Promise<{
        created: number;
        updated: number;
        conflicts: number;
    }>;
    getSyncStatus(userId: string): Promise<{
        lastSync: {
            quiz_attempts: Date | null;
            progress: Date | null;
        };
        pendingChanges: {
            quiz_attempts: number;
            progress: number;
        };
    }>;
    syncUserProgress(userId: string): Promise<void>;
}
