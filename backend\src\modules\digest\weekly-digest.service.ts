import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, MoreThanOrEqual, LessThanOrEqual, In, Between } from 'typeorm';
import { User } from '../../entities/user.entity';
import { Material } from '../../entities/materials.entity';
import { CPDActivity } from '../../entities/cpd-tracking.entity';
import { WeeklyDigest } from '../../entities/weekly-digest.entity';

@Injectable()
export class WeeklyDigestService {
    constructor(
        @InjectRepository(User)
        private userRepository: Repository<User>,
        @InjectRepository(Material)
        private materialRepository: Repository<Material>,
        @InjectRepository(CPDActivity)
        private cpdActivityRepository: Repository<CPDActivity>,
        @InjectRepository(WeeklyDigest)
        private weeklyDigestRepository: Repository<WeeklyDigest>
    ) {}

    async generateWeeklyDigest(userId: string): Promise<WeeklyDigest> {
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user) throw new Error('User not found');

        // Get date range for the past week
        const endDate = new Date();
        const startDate = new Date(endDate);
        startDate.setDate(startDate.getDate() - 7);

        // Get user's activities for the past week
        const activities = await this.cpdActivityRepository.find({
            where: {
                user: { id: userId },
                activity_date: Between(startDate, endDate)
            },
            relations: ['material'],
        });

        // Get new materials
        const newMaterials = await this.materialRepository.find({
            where: {
                createdAt: MoreThanOrEqual(startDate),
            },
            order: { createdAt: 'DESC' },
            take: 5,
        });

        // Calculate CPD progress
        const totalPoints = activities.reduce((sum: number, activity: any) => sum + activity.points, 0);

        // Create digest
        const digest = this.weeklyDigestRepository.create({
            user: { id: userId },
            week_start_date: startDate,
            week_end_date: endDate,
            is_sent: false,
            status: 'pending',
            content: {
                new_materials: newMaterials.map((m: any) => ({
                    material: m,
                    relevance: 1.0,
                })),
                recommended_topics: [],
                cpd_progress: {
                    points_earned: totalPoints,
                    points_required: 50,
                    activities: activities.map((a: any) => ({
                        type: a.activityType,
                        points: a.points,
                        date: a.activity_date,
                    })),
                },
                upcoming_deadlines: [],
            },
        });

        return this.weeklyDigestRepository.save(digest);
    }

    async getLatestDigest(userId: string): Promise<WeeklyDigest | null> {
        return this.weeklyDigestRepository.findOne({
            where: { user: { id: userId } },
            order: { created_at: 'DESC' },
        });
    }

    async markDigestAsRead(digestId: string): Promise<WeeklyDigest> {
        const digest = await this.weeklyDigestRepository.findOne({
            where: { id: digestId },
        });
        if (!digest) throw new Error('Digest not found');

        digest.status = 'read';
        digest.read_at = new Date();

        return this.weeklyDigestRepository.save(digest);
    }

    async getUnreadDigestCount(userId: string): Promise<number> {
        return this.weeklyDigestRepository.count({
            where: {
                user: { id: userId },
                status: 'pending',
            },
        });
    }

    private async getRelevantMaterials(user: User): Promise<Material[]> {
        return this.materialRepository.find({
            where: {},
            order: {
                createdAt: 'DESC'
            },
            take: 10
        });
    }

    private calculatePoints(materials: Material[]): number {
        return materials.reduce((acc, material) => {
            return acc + (material.metadata?.cpd_points || 0);
        }, 0);
    }
} 