"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WhatsAppService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const axios_1 = __importDefault(require("axios"));
let WhatsAppService = class WhatsAppService {
    constructor(configService) {
        this.configService = configService;
        const apiUrl = this.configService.get('WHATSAPP_API_URL');
        const apiToken = this.configService.get('WHATSAPP_API_TOKEN');
        if (!apiUrl || !apiToken) {
            throw new Error('WhatsApp API configuration is missing');
        }
        this.apiUrl = apiUrl;
        this.apiToken = apiToken;
    }
    async sendWelcomeMessage(user) {
        const message = `Welcome to our platform! We're excited to have you on board.`;
        return this.sendMessage(user.phone_number, message);
    }
    async sendStudyReminder(user) {
        const message = `Don't forget to study today! Your learning streak is at ${user.streak_days} days.`;
        return this.sendMessage(user.phone_number, message);
    }
    async sendAchievementNotification(user, achievement) {
        const message = `Congratulations! You've earned a new achievement: ${achievement}`;
        return this.sendMessage(user.phone_number, message);
    }
    async sendWeeklyDigest(user, digest) {
        const message = `Your weekly learning summary:\n${JSON.stringify(digest, null, 2)}`;
        return this.sendMessage(user.phone_number, message);
    }
    async sendCustomNotification(user, message) {
        return this.sendMessage(user.phone_number, message);
    }
    async sendProgressUpdate(user, progress) {
        const message = `📊 Progress Update\n\nTopic: ${progress.topic.title}\nCompletion: ${progress.completion_percentage}%\nTime Spent: ${progress.time_spent_minutes} minutes\n\nKeep going! 🌟`;
        return this.sendMessage(user.phone_number, message);
    }
    async sendStreakAlert(user) {
        const message = `🔥 Streak Alert!\n\nYou've maintained your study streak for ${user.streak_days} days!\n\nDon't break the chain! 💪`;
        return this.sendMessage(user.phone_number, message);
    }
    async sendGroupStudyInvite(user, groupName, inviteLink) {
        const message = `👥 Study Group Invitation\n\nYou've been invited to join ${groupName}!\n\nClick here to join: ${inviteLink}\n\nStudy together, achieve more! 📚`;
        return this.sendMessage(user.phone_number, message);
    }
    async sendDailyDigest(user, stats) {
        const message = `📈 Daily Study Digest\n\nTopics Completed: ${stats.completedTopics}\nTime Studied: ${stats.totalMinutes} minutes\nStreak: ${user.streak_days} days\n\nGreat job today! 🎉`;
        return this.sendMessage(user.phone_number, message);
    }
    async sendMessage(phone_number, message) {
        try {
            const response = await axios_1.default.post(`${this.apiUrl}/messages`, {
                messaging_product: 'whatsapp',
                to: phone_number,
                type: 'text',
                text: { body: message }
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiToken}`,
                    'Content-Type': 'application/json'
                }
            });
            return response.data;
        }
        catch (error) {
            console.error('WhatsApp API Error:', error);
            throw new Error('Failed to send WhatsApp message');
        }
    }
    async handleIncomingMessage(message) {
        const { from, text } = message;
        if (text.toLowerCase().includes('start study')) {
            return this.handleStartStudyCommand(from);
        }
        if (text.toLowerCase().includes('check progress')) {
            return this.handleCheckProgressCommand(from);
        }
        return null;
    }
    async handleStartStudyCommand(phone_number) {
    }
    async handleCheckProgressCommand(phone_number) {
    }
};
exports.WhatsAppService = WhatsAppService;
exports.WhatsAppService = WhatsAppService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], WhatsAppService);
//# sourceMappingURL=whatsapp.service.js.map