import { offlineService } from './offlineService';

class SyncManager {
  private isOnline: boolean = navigator.onLine;
  private syncInProgress: boolean = false;
  private maxRetries: number = 3;

  constructor() {
    this.setupNetworkListeners();
  }

  private setupNetworkListeners() {
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);
  }

  private handleOnline = async () => {
    this.isOnline = true;
    await this.syncPendingItems();
  };

  private handleOffline = () => {
    this.isOnline = false;
  };

  async syncPendingItems() {
    if (this.syncInProgress || !this.isOnline) return;

    try {
      this.syncInProgress = true;
      const items = await offlineService.getSyncQueueItems();

      for (const item of items) {
        if (item.attempts >= this.maxRetries) {
          // Remove items that have exceeded max retries
          await offlineService.removeFromSyncQueue(item.id);
          continue;
        }

        try {
          await this.processSyncItem(item);
          await offlineService.removeFromSyncQueue(item.id);
        } catch (error) {
          console.error(`Failed to sync item ${item.id}:`, error);
          await offlineService.incrementSyncAttempt(item.id);
        }
      }
    } finally {
      this.syncInProgress = false;
    }
  }

  private async processSyncItem(item: any) {
    switch (item.type) {
      case 'quiz_submission':
        await this.syncQuizSubmission(item.data);
        break;
      case 'progress_log':
        await this.syncProgressLog(item.data);
        break;
      default:
        throw new Error(`Unknown sync item type: ${item.type}`);
    }
  }

  private async syncQuizSubmission(data: any) {
    // TODO: Implement actual API call to sync quiz submission
    const response = await fetch('/api/quizzes/submit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...data,
        syncedAt: new Date().toISOString(),
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to sync quiz submission');
    }
  }

  private async syncProgressLog(data: any) {
    // TODO: Implement actual API call to sync progress log
    const response = await fetch('/api/progress/log', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...data,
        syncedAt: new Date().toISOString(),
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to sync progress log');
    }
  }

  // Public methods
  async queueQuizSubmission(quizData: any) {
    await offlineService.addToSyncQueue({
      type: 'quiz_submission',
      data: quizData,
      timestamp: Date.now(),
    });

    if (this.isOnline) {
      await this.syncPendingItems();
    }
  }

  async queueProgressLog(progressData: any) {
    await offlineService.addToSyncQueue({
      type: 'progress_log',
      data: progressData,
      timestamp: Date.now(),
    });

    if (this.isOnline) {
      await this.syncPendingItems();
    }
  }

  isCurrentlyOnline() {
    return this.isOnline;
  }
}

export const syncManager = new SyncManager(); 