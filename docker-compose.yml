version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:3002
      - NEXT_PUBLIC_ANALYTICS_API_URL=http://analytics:5000
    depends_on:
      - backend
      - analytics

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3002:3002"
    environment:
      - DATABASE_URL=**************************************/medtrack
      - JWT_SECRET=your-jwt-secret
      - JWT_REFRESH_SECRET=your-jwt-refresh-secret
      - NODE_ENV=production
    depends_on:
      - db

  analytics:
    build:
      context: ./backend/python_analytics
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - DATABASE_URL=**************************************/medtrack
      - JWT_SECRET=your-jwt-secret
      - PYTHONUNBUFFERED=1
    depends_on:
      - db

  db:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=medtrack
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data: 