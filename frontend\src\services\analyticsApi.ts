import api from './api';
import { PerformanceOptimizer } from './performanceOptimizer';

export interface PerformanceMetrics {
    totalCards: number;
    cardsDue: number;
    averageInterval: number;
    successRate: number;
    quizPerformance: {
        averageScore: number;
        totalAttempts: number;
        improvement: number;
    };
    studyStreak: number;
}

export interface StudyPatterns {
    preferredStudyTimes: {
        morning: number;
        afternoon: number;
        evening: number;
    };
    studyDuration: {
        averageDuration: number;
        longestSession: number;
        shortestSession: number;
    };
    performanceByTopic: Record<string, number>;
    consistencyScore: number;
}

export interface PerformancePredictions {
    predictedSuccessRate: number;
    confidence: number;
    recommendedStudyTime: number;
}

export interface StudyRecommendations {
    studySchedule: {
        recommendedTimes: string[];
        duration: number;
        frequency: string;
    };
    focusAreas: string[];
    improvementTips: string[];
}

class AnalyticsApiService {
    private static readonly CACHE_PREFIX = 'analytics:';
    private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

    async getPerformanceMetrics(userId: string): Promise<PerformanceMetrics> {
        const cacheKey = `${AnalyticsApiService.CACHE_PREFIX}metrics:${userId}`;
        const cachedData = PerformanceOptimizer.getCache<PerformanceMetrics>(cacheKey);
        
        if (cachedData) {
            return cachedData;
        }

        const response = await api.get(`/analytics/performance/${userId}`);
        PerformanceOptimizer.setCache(cacheKey, response.data);
        return response.data;
    }

    async getStudyPatterns(userId: string): Promise<StudyPatterns> {
        const cacheKey = `${AnalyticsApiService.CACHE_PREFIX}patterns:${userId}`;
        const cachedData = PerformanceOptimizer.getCache<StudyPatterns>(cacheKey);
        
        if (cachedData) {
            return cachedData;
        }

        const response = await api.get(`/analytics/study-patterns/${userId}`);
        PerformanceOptimizer.setCache(cacheKey, response.data);
        return response.data;
    }

    async getPerformancePredictions(userId: string): Promise<PerformancePredictions> {
        const cacheKey = `${AnalyticsApiService.CACHE_PREFIX}predictions:${userId}`;
        const cachedData = PerformanceOptimizer.getCache<PerformancePredictions>(cacheKey);
        
        if (cachedData) {
            return cachedData;
        }

        const response = await api.get(`/analytics/predictions/${userId}`);
        PerformanceOptimizer.setCache(cacheKey, response.data);
        return response.data;
    }

    async getStudyRecommendations(userId: string): Promise<StudyRecommendations> {
        const cacheKey = `${AnalyticsApiService.CACHE_PREFIX}recommendations:${userId}`;
        const cachedData = PerformanceOptimizer.getCache<StudyRecommendations>(cacheKey);
        
        if (cachedData) {
            return cachedData;
        }

        const response = await api.get(`/analytics/recommendations/${userId}`);
        PerformanceOptimizer.setCache(cacheKey, response.data);
        return response.data;
    }

    async trackEvent(eventData: {
        userId: string;
        eventType: string;
        data: any;
        timestamp: string;
    }): Promise<void> {
        await api.post('/analytics/track', eventData);
    }

    async batchTrackEvents(events: Array<{
        userId: string;
        eventType: string;
        data: any;
        timestamp: string;
    }>): Promise<void> {
        await PerformanceOptimizer.batchProcess(events, async (event) => {
            await this.trackEvent(event);
        });
    }

    clearCache(userId: string): void {
        const keys = [
            `${AnalyticsApiService.CACHE_PREFIX}metrics:${userId}`,
            `${AnalyticsApiService.CACHE_PREFIX}patterns:${userId}`,
            `${AnalyticsApiService.CACHE_PREFIX}predictions:${userId}`,
            `${AnalyticsApiService.CACHE_PREFIX}recommendations:${userId}`,
        ];

        keys.forEach(key => PerformanceOptimizer.setCache(key, null));
    }
}

export const analyticsApi = new AnalyticsApiService(); 