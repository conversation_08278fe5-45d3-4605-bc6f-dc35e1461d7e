"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditLogService = exports.AuditEventType = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("../../entities/user.entity");
var AuditEventType;
(function (AuditEventType) {
    AuditEventType["LOGIN_SUCCESS"] = "LOGIN_SUCCESS";
    AuditEventType["LOGIN_FAILURE"] = "LOGIN_FAILURE";
    AuditEventType["LOGOUT"] = "LOGOUT";
    AuditEventType["PASSWORD_CHANGE"] = "PASSWORD_CHANGE";
    AuditEventType["PASSWORD_RESET_REQUEST"] = "PASSWORD_RESET_REQUEST";
    AuditEventType["PASSWORD_RESET_COMPLETE"] = "PASSWORD_RESET_COMPLETE";
    AuditEventType["USER_CREATED"] = "USER_CREATED";
    AuditEventType["USER_UPDATED"] = "USER_UPDATED";
    AuditEventType["USER_DELETED"] = "USER_DELETED";
    AuditEventType["ROLE_CHANGED"] = "ROLE_CHANGED";
    AuditEventType["TOKEN_REFRESH"] = "TOKEN_REFRESH";
    AuditEventType["TOKEN_REVOKED"] = "TOKEN_REVOKED";
})(AuditEventType || (exports.AuditEventType = AuditEventType = {}));
let AuditLogService = class AuditLogService {
    constructor(userRepository) {
        this.userRepository = userRepository;
        this.logger = new common_1.Logger('AuditLog');
    }
    async log(eventType, userId, ipAddress, userAgent, eventData) {
        try {
            const user = await this.userRepository.findOne({ where: { id: userId } });
            const entry = {
                id: crypto.randomUUID(),
                eventType,
                userId,
                userEmail: user?.email || 'unknown',
                ipAddress,
                userAgent,
                eventData,
                timestamp: new Date(),
            };
            this.logger.log(JSON.stringify(entry));
            if (this.isSecurityCriticalEvent(eventType)) {
                this.handleSecurityCriticalEvent(entry);
            }
        }
        catch (error) {
            this.logger.error(`Failed to log audit event: ${error.message}`, error.stack);
        }
    }
    isSecurityCriticalEvent(eventType) {
        return [
            AuditEventType.LOGIN_FAILURE,
            AuditEventType.PASSWORD_RESET_REQUEST,
            AuditEventType.ROLE_CHANGED,
            AuditEventType.USER_DELETED,
        ].includes(eventType);
    }
    handleSecurityCriticalEvent(entry) {
        this.logger.warn(`Security critical event: ${JSON.stringify(entry)}`);
    }
    async getRecentFailedLogins(userId, minutes = 15) {
        return 0;
    }
};
exports.AuditLogService = AuditLogService;
exports.AuditLogService = AuditLogService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], AuditLogService);
//# sourceMappingURL=audit-log.service.js.map