import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Base directory
BASE_DIR = Path(__file__).parent

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL")

# Analytics configuration
ANALYTICS_CONFIG = {
    "learning_styles": {
        "visual": {
            "weight": 0.3,
            "min_score": 0.6
        },
        "auditory": {
            "weight": 0.3,
            "min_score": 0.6
        },
        "reading": {
            "weight": 0.2,
            "min_score": 0.6
        },
        "kinesthetic": {
            "weight": 0.2,
            "min_score": 0.6
        }
    },
    "performance_prediction": {
        "confidence_threshold": 0.8,
        "anomaly_threshold": 2.0,
        "min_data_points": 10
    },
    "recommendations": {
        "max_recommendations": 5,
        "min_confidence": 0.7,
        "update_frequency": 3600  # seconds
    },
    "caching": {
        "ttl": int(os.getenv("CACHE_TTL", 3600)),
        "max_size": 1000
    }
}

# Logging configuration
LOG_CONFIG = {
    "level": os.getenv("LOG_LEVEL", "INFO"),
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": BASE_DIR / "logs" / "analytics.log"
}

# Create logs directory if it doesn't exist
(BASE_DIR / "logs").mkdir(exist_ok=True) 