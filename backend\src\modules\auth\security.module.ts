// src/modules/auth/security.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { SecurityService } from './security.service';
import { SecurityController } from './security.controller';
import { UserSecuritySettings, UserSession, SecurityEvent } from '../../entities/security.entity';
import { User } from '../../entities/user.entity';
import { TokenBlacklistModule } from './token-blacklist.module';
import { UserSecuritySettingsRepository } from './repositories/user-security-settings.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserSecuritySettings,
      UserSession,
      SecurityEvent,
      User,
    ]),
    TokenBlacklistModule,
    ConfigModule,
  ],
  providers: [SecurityService, UserSecuritySettingsRepository],
  controllers: [SecurityController],
  exports: [SecurityService],
})
export class SecurityModule {}