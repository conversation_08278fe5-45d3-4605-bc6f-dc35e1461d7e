from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import HTTPEx<PERSON>, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import jwt, JWTError
import os
from dotenv import load_dotenv
from pydantic import BaseModel

load_dotenv()

# Configuration
SECRET_KEY = os.getenv("JWT_SECRET_KEY")
if not SECRET_KEY:
    raise ValueError("JWT_SECRET_KEY must be set in environment variables")

ALGORITHM = "HS256"  # NestJS default algorithm
security = HTTPBearer()

class TokenPayload(BaseModel):
    sub: str  # user ID
    email: Optional[str] = None
    username: Optional[str] = None
    role: Optional[str] = None
    iat: Optional[int] = None
    exp: Optional[int] = None

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> TokenPayload:
    """
    Verify the JWT token from the authorization header and return the decoded payload
    """
    try:
        token = credentials.credentials
        
        # Verify and decode the token
        payload = jwt.decode(
            token,
            SECRET_KEY,
            algorithms=[ALGORITHM]
        )
        
        # Check if token is expired
        if "exp" in payload:
            if datetime.utcfromtimestamp(payload["exp"]) < datetime.utcnow():
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token has expired"
                )
        
        return TokenPayload(**payload)
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )

async def get_current_user(token: TokenPayload = Depends(verify_token)) -> Dict[str, Any]:
    """
    Extract user information from the verified token.
    Returns dictionary with user information.
    """
    if not token.sub:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User ID not found in token"
        )
    
    return {
        "user_id": token.sub,
        "email": token.email,
        "username": token.username,
        "role": token.role
    }