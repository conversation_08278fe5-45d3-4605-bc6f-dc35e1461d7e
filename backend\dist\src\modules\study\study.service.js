"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StudyService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const topic_entity_1 = require("../../entities/topic.entity");
const topic_progress_entity_1 = require("../../entities/topic-progress.entity");
const study_session_entity_1 = require("../../entities/study-session.entity");
let StudyService = class StudyService {
    constructor(topicRepository, topicProgressRepository, studySessionRepository) {
        this.topicRepository = topicRepository;
        this.topicProgressRepository = topicProgressRepository;
        this.studySessionRepository = studySessionRepository;
    }
    async getTopicsByCategory(category) {
        return this.topicRepository.find({
            where: { category },
            relations: ['units', 'units.materials']
        });
    }
    async getTopicProgress(userId, topicId) {
        return this.topicProgressRepository.findOne({
            where: { user: { id: userId }, topic: { id: topicId } },
            relations: ['topic']
        });
    }
    async startStudySession(userId, topicId) {
        const session = this.studySessionRepository.create({
            user: { id: userId },
            topic: { id: topicId },
            started_at: new Date(),
            duration_minutes: 0,
            focus_score: 0,
            activities: []
        });
        return this.studySessionRepository.save(session);
    }
    async endStudySession(sessionId, activities) {
        const session = await this.studySessionRepository.findOne({
            where: { id: sessionId },
            relations: ['user', 'topic']
        });
        if (!session) {
            throw new Error('Study session not found');
        }
        session.ended_at = new Date();
        session.duration_minutes = Math.round((session.ended_at.getTime() - session.started_at.getTime()) / (1000 * 60));
        session.activities = activities;
        session.focus_score = this.calculateFocusScore(activities);
        await this.updateTopicProgress(session);
        return this.studySessionRepository.save(session);
    }
    calculateFocusScore(activities) {
        if (!activities.length)
            return 0;
        const totalDuration = activities.reduce((sum, act) => sum + act.durationMinutes, 0);
        const maxDuration = Math.max(...activities.map(act => act.durationMinutes));
        return maxDuration / totalDuration;
    }
    async updateTopicProgress(session) {
        let progress = await this.topicProgressRepository.findOne({
            where: {
                user: { id: session.user.id },
                topic: { id: session.topic.id }
            }
        });
        if (!progress) {
            progress = this.topicProgressRepository.create({
                user: { id: session.user.id },
                topic: { id: session.topic.id },
                time_spent_minutes: 0,
                completion_percentage: 0,
                streak_days: 0,
                last_studied_at: new Date()
            });
        }
        progress.time_spent_minutes += session.duration_minutes;
        progress.last_studied_at = new Date();
        const quiz_scores = session.activities
            .filter(act => act.type === 'quiz')
            .map(act => ({
            type: act.type,
            duration_minutes: act.duration_minutes,
            score: act.score || 0,
            date: new Date()
        }));
        if (quiz_scores.length > 0) {
            progress.quiz_scores = [...(progress.quiz_scores || []), ...quiz_scores.map(score => ({
                    quiz_id: score.type,
                    score: score.score,
                    date: score.date
                }))];
            progress.completion_percentage = Math.min(100, progress.completion_percentage + (quiz_scores.length * 10));
        }
        const last_studied_at = progress.last_studied_at;
        const today = new Date();
        if (last_studied_at && this.isConsecutiveDay(last_studied_at, today)) {
            progress.streak_days = (progress.streak_days || 0) + 1;
        }
        await this.topicProgressRepository.save(progress);
    }
    isConsecutiveDay(lastDate, currentDate) {
        const diffTime = Math.abs(currentDate.getTime() - lastDate.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays === 1;
    }
    async getStudyStats(userId) {
        const [totalTime, totalSessions, streakDays] = await Promise.all([
            this.studySessionRepository
                .createQueryBuilder('session')
                .where('session.user.id = :userId', { userId })
                .select('SUM(session.durationMinutes)', 'total')
                .getRawOne(),
            this.studySessionRepository.count({ where: { user: { id: userId } } }),
            this.topicProgressRepository
                .createQueryBuilder('progress')
                .where('progress.user.id = :userId', { userId })
                .select('MAX(progress.streakDays)', 'maxStreak')
                .getRawOne()
        ]);
        return {
            totalStudyTime: totalTime?.total || 0,
            totalSessions,
            currentStreak: streakDays?.maxStreak || 0
        };
    }
    async trackActivity(user, activity) {
        const progress = this.topicProgressRepository.create({
            user: { id: user.id },
            topic: { id: activity.type },
            completion_percentage: 0,
            time_spent_minutes: activity.duration_minutes,
            is_completed: false,
            streak_days: 0
        });
        await this.topicProgressRepository.save(progress);
    }
};
exports.StudyService = StudyService;
exports.StudyService = StudyService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(topic_entity_1.Topic)),
    __param(1, (0, typeorm_1.InjectRepository)(topic_progress_entity_1.TopicProgress)),
    __param(2, (0, typeorm_1.InjectRepository)(study_session_entity_1.StudySession)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], StudyService);
//# sourceMappingURL=study.service.js.map