Write-Host "🔄 Starting comprehensive Docker configuration testing..." -ForegroundColor Green

# First validate the configuration
Write-Host "`nValidating configuration..." -ForegroundColor Cyan
.\scripts\validate-config.ps1
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Configuration validation failed" -ForegroundColor Red
    exit 1
}

# Test 1: Check if services start properly
Write-Host "`nTest 1: Starting services..." -ForegroundColor Cyan
docker-compose -f docker-compose.prod.yml up -d
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Services started successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Services failed to start" -ForegroundColor Red
    exit 1
}

# Test 2: Check resource limits
Write-Host "`nTest 2: Checking resource limits..." -ForegroundColor Cyan
docker stats --no-stream $(docker-compose -f docker-compose.prod.yml ps -q)

# Test 3: Test API health endpoint
Write-Host "`nTest 3: Testing API health endpoint..." -ForegroundColor Cyan
Start-Sleep -Seconds 10
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3002/api/health" -Method GET
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ API health check passed" -ForegroundColor Green
    } else {
        Write-Host "❌ API health check failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ API health check failed: $_" -ForegroundColor Red
}

# Test 4: Test Redis connection
Write-Host "`nTest 4: Testing Redis connection..." -ForegroundColor Cyan
$redisPing = docker-compose -f docker-compose.prod.yml exec -T redis redis-cli -a $env:REDIS_PASSWORD ping
if ($redisPing -eq "PONG") {
    Write-Host "✅ Redis connection successful" -ForegroundColor Green
} else {
    Write-Host "❌ Redis connection failed" -ForegroundColor Red
}

# Test 5: Validate Railway-specific environment variables
Write-Host "`nTest 5: Validating Railway environment variables..." -ForegroundColor Cyan
if (-not $env:RAILWAY_ENVIRONMENT -or -not $env:RAILWAY_PROJECT_ID -or -not $env:RAILWAY_SERVICE_NAME -or -not $env:RAILWAY_API_URL) {
    Write-Host "❌ Railway environment variables are missing or incomplete" -ForegroundColor Red
    exit 1
} else {
    Write-Host "✅ Railway environment variables are set correctly" -ForegroundColor Green
}

# Test 6: Validate Redis setup
Write-Host "`nTest 6: Validating Redis setup..." -ForegroundColor Cyan
$redisPing = docker-compose -f docker-compose.prod.yml exec -T redis redis-cli -a $env:REDIS_PASSWORD ping
if ($redisPing -eq "PONG") {
    Write-Host "✅ Redis setup validated successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Redis setup validation failed" -ForegroundColor Red
    exit 1
}

# Cleanup
Write-Host "`nCleaning up..." -ForegroundColor Cyan
docker-compose -f docker-compose.prod.yml down

Write-Host "`nTest complete!" -ForegroundColor Green
