from jose import JWTError, jwt
print("jose imported successfully")

# Test JWT functionality
secret_key = "test-secret-key"
test_data = {"sub": "test", "role": "user"}

# Create a token
token = jwt.encode(test_data, secret_key, algorithm="HS256")
print(f"Created JWT token: {token}")

# Decode the token
decoded = jwt.decode(token, secret_key, algorithms=["HS256"])
print(f"Decoded JWT data: {decoded}")
print("\nJWT functionality test completed successfully!")
