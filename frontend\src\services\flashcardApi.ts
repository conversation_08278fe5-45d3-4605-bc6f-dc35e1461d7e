import api from './api';

export interface Flashcard {
    id: string;
    userId: string;
    questionId: string;
    ef: number;
    interval: number;
    nextReview: Date;
    correctStreak: number;
    lastReview: Date;
    synced: boolean;
}

export interface CardStats {
    total: number;
    due: number;
    upcoming: number;
    averageInterval: number;
}

class FlashcardApiService {
    async createFlashcard(userId: string, questionId: string): Promise<Flashcard> {
        const response = await api.post('/flashcards/create', {
            userId,
            questionId,
        });
        return response.data;
    }

    async getDueCards(userId: string): Promise<Flashcard[]> {
        const response = await api.get(`/flashcards/due/${userId}`);
        return response.data;
    }

    async updateCard(cardId: string, quality: number): Promise<Flashcard> {
        const response = await api.post(`/flashcards/update/${cardId}`, {
            quality,
        });
        return response.data;
    }

    async getCardStats(userId: string): Promise<CardStats> {
        const response = await api.get(`/flashcards/stats/${userId}`);
        return response.data;
    }

    async syncCards(userId: string, cards: Flashcard[]): Promise<Flashcard[]> {
        const response = await api.post(`/flashcards/sync/${userId}`, {
            cards,
        });
        return response.data;
    }
}

export const flashcardApi = new FlashcardApiService(); 