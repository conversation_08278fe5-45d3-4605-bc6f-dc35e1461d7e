"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const security_service_1 = require("./security.service");
const jwt_auth_guard_1 = require("./jwt-auth.guard");
const security_dto_1 = require("../../dto/auth/security.dto");
let SecurityController = class SecurityController {
    constructor(securityService) {
        this.securityService = securityService;
    }
    async setupTwoFactor(req) {
        return this.securityService.setupTwoFactor(req.user.id);
    }
    async verifyTwoFactor(req, dto) {
        const isValid = await this.securityService.verifyTwoFactor(req.user.id, dto.token);
        return { valid: isValid };
    }
    async changePassword(req, dto) {
        await this.securityService.changePassword(req.user.id, dto);
    }
    async updateSecuritySettings(req, dto) {
        await this.securityService.updateSecuritySettings(req.user.id, dto);
        return { message: 'Security settings updated successfully' };
    }
    async getSecuritySettings(req) {
        return this.securityService.getSecuritySettings(req.user.id);
    }
    async getActiveSessions(req) {
        return this.securityService.getActiveSessions(req.user.id);
    }
    async revokeSession(req, sessionId) {
        await this.securityService.revokeSession(req.user.id, sessionId);
    }
    async getSecurityEvents(req, limit) {
        return this.securityService.getSecurityEvents(req.user.id, limit);
    }
    async generateBackupCodes(req) {
        const codes = await this.securityService.generateNewBackupCodes(req.user.id);
        return { backupCodes: codes };
    }
    async verifyEmail(dto) {
        const verified = await this.securityService.verifyEmail(dto.token);
        return { verified };
    }
    async resendVerificationEmail(req) {
        await this.securityService.resendVerificationEmail(req.user.id);
        return { message: 'Verification email sent' };
    }
    async initiateAccountRecovery(dto) {
        await this.securityService.initiateAccountRecovery(dto.email);
        return { message: 'Recovery process initiated' };
    }
    async verifyRecoveryRequest(dto) {
        const verified = await this.securityService.verifyRecoveryRequest(dto.token, dto.answers);
        return { verified };
    }
};
exports.SecurityController = SecurityController;
__decorate([
    (0, common_1.Post)('2fa/setup'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Setup two-factor authentication' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns QR code and secret for 2FA setup' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SecurityController.prototype, "setupTwoFactor", null);
__decorate([
    (0, common_1.Post)('2fa/verify'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Verify two-factor authentication token' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '2FA token verified successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, security_dto_1.VerifyTwoFactorDto]),
    __metadata("design:returntype", Promise)
], SecurityController.prototype, "verifyTwoFactor", null);
__decorate([
    (0, common_1.Put)('password'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Change user password' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Password changed successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, security_dto_1.ChangePasswordDto]),
    __metadata("design:returntype", Promise)
], SecurityController.prototype, "changePassword", null);
__decorate([
    (0, common_1.Put)('settings'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update security settings' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Security settings updated successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, security_dto_1.SecuritySettingsDto]),
    __metadata("design:returntype", Promise)
], SecurityController.prototype, "updateSecuritySettings", null);
__decorate([
    (0, common_1.Get)('settings'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get user security settings' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns user security settings' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SecurityController.prototype, "getSecuritySettings", null);
__decorate([
    (0, common_1.Get)('sessions'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get active user sessions' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns list of active sessions' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SecurityController.prototype, "getActiveSessions", null);
__decorate([
    (0, common_1.Delete)('sessions/:sessionId'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Revoke a specific session' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Session revoked successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('sessionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], SecurityController.prototype, "revokeSession", null);
__decorate([
    (0, common_1.Get)('events'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get security events for user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns list of security events' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], SecurityController.prototype, "getSecurityEvents", null);
__decorate([
    (0, common_1.Post)('backup-codes/generate'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Generate new backup codes' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns new backup codes' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SecurityController.prototype, "generateBackupCodes", null);
__decorate([
    (0, common_1.Post)('email/verify'),
    (0, swagger_1.ApiOperation)({ summary: 'Verify email address' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Email verified successfully' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [security_dto_1.EmailVerificationDto]),
    __metadata("design:returntype", Promise)
], SecurityController.prototype, "verifyEmail", null);
__decorate([
    (0, common_1.Post)('email/resend'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Resend email verification' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Verification email sent' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SecurityController.prototype, "resendVerificationEmail", null);
__decorate([
    (0, common_1.Post)('recovery/initiate'),
    (0, swagger_1.ApiOperation)({ summary: 'Initiate account recovery' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Account recovery initiated' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [security_dto_1.AccountRecoveryDto]),
    __metadata("design:returntype", Promise)
], SecurityController.prototype, "initiateAccountRecovery", null);
__decorate([
    (0, common_1.Post)('recovery/verify'),
    (0, swagger_1.ApiOperation)({ summary: 'Verify account recovery request' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Recovery request verified' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [security_dto_1.VerifyRecoveryDto]),
    __metadata("design:returntype", Promise)
], SecurityController.prototype, "verifyRecoveryRequest", null);
exports.SecurityController = SecurityController = __decorate([
    (0, swagger_1.ApiTags)('Security'),
    (0, common_1.Controller)('auth/security'),
    __metadata("design:paramtypes", [security_service_1.SecurityService])
], SecurityController);
//# sourceMappingURL=security.controller.js.map