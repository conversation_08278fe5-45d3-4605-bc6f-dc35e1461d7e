import { Test, TestingModule } from '@nestjs/testing';
import { MaterialsController } from './materials.controller';
import { MaterialsService } from './materials.service';
import { Material } from '../../entities/materials.entity';

describe('MaterialsController', () => {
  let controller: MaterialsController;

  const mockMaterialsService = {
    findAll: jest.fn().mockResolvedValue([]),
    create: jest.fn().mockImplementation((material) => Promise.resolve(material)),
    findOne: jest.fn().mockImplementation((id) => Promise.resolve({ id, title: 'Test Material' })),
    remove: jest.fn().mockResolvedValue(undefined),
    uploadFile: jest.fn().mockImplementation((file, userId, unitId, title, description, type) =>
      Promise.resolve({
        id: '1',
        title,
        description,
        type,
        uploadedBy: { id: userId },
        unit: { id: unitId }
      })
    ),
    shareMaterial: jest.fn().mockImplementation((materialId, sharedByUserId, sharedWithUserId) =>
      Promise.resolve({ materialId, sharedByUserId, sharedWithUserId })
    ),
  };

  const mockMaterial: Partial<Material> = {
    id: '1',
    title: 'Test Material',
    description: 'Test Description',
    type: 'Test Type',
    uploadedBy: { id: '1' } as any,
    unit: { id: '1' } as any,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MaterialsController],
      providers: [
        {
          provide: MaterialsService,
          useValue: mockMaterialsService,
        },
      ],
    }).compile();

    controller = module.get<MaterialsController>(MaterialsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of materials', async () => {
      const result = await controller.findAll();
      expect(result).toEqual([]);
      expect(mockMaterialsService.findAll).toHaveBeenCalled();
    });
  });

  describe('create', () => {
    it('should create a material', async () => {
      const result = await controller.create(mockMaterial);
      expect(result).toEqual(mockMaterial);
      expect(mockMaterialsService.create).toHaveBeenCalledWith(mockMaterial);
    });
  });

  describe('findOne', () => {
    it('should return a material by id', async () => {
      const result = await controller.findOne('1');
      expect(result).toEqual({ id: '1', title: 'Test Material' });
      expect(mockMaterialsService.findOne).toHaveBeenCalledWith('1');
    });
  });

  describe('remove', () => {
    it('should remove a material by id', async () => {
      await controller.remove('1');
      expect(mockMaterialsService.remove).toHaveBeenCalledWith('1');
    });
  });

  describe('uploadFile', () => {
    it('should upload a file', async () => {
      const file: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.txt',
        encoding: '7bit',
        mimetype: 'text/plain',
        size: 4,
        buffer: Buffer.from('test'),
        destination: '',
        filename: 'test.txt',
        path: '',
        stream: null as any,
      };
      const result = await controller.uploadFile(file, {
        userId: '1',
        unitId: '1',
        title: 'Test Title',
        description: 'Test Description',
        type: 'text/plain',
      });
      expect(result).toEqual({
        id: '1',
        title: 'Test Title',
        description: 'Test Description',
        type: 'text/plain',
        uploadedBy: { id: '1' },
        unit: { id: '1' }
      });
      expect(mockMaterialsService.uploadFile).toHaveBeenCalledWith(
        file,
        '1',
        '1',
        'Test Title',
        'Test Description',
        'text/plain',
      );
    });
  });

  describe('shareMaterial', () => {
    it('should share a material', async () => {
      const result = await controller.shareMaterial({
        materialId: '1',
        sharedByUserId: '1',
        sharedWithUserId: '2',
      });
      expect(result).toEqual({ materialId: '1', sharedByUserId: '1', sharedWithUserId: '2' });
      expect(mockMaterialsService.shareMaterial).toHaveBeenCalledWith('1', '1', '2');
    });
  });
});