# Medical Education Analytics Service

This service provides advanced analytics for the medical education platform, including learning pattern analysis, performance predictions, and personalized study recommendations.

## Setup Instructions

1. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Create a `.env` file in the root directory with the following variables:
```
DATABASE_URL=postgresql://user:password@localhost:5432/medical_education
JWT_SECRET_KEY=your_jwt_secret_key
FRONTEND_URL=http://localhost:3000
PYTHON_ANALYTICS_PORT=5000
```

4. Run the service:
```bash
python main.py
```

The service will be available at `http://localhost:5000`

## API Endpoints

- `GET /health` - Health check endpoint
- `GET /analytics/user/{user_id}` - Get comprehensive analytics for a user
- `GET /analytics/patterns/{user_id}` - Get learning patterns for a user
- `GET /analytics/predictions/{user_id}` - Get performance predictions for a user

All endpoints except `/health` require JWT authentication.

## Features

1. **Learning Pattern Analysis**
   - Study time patterns
   - Quiz performance analysis
   - Progress tracking
   - Learning style determination

2. **Performance Prediction**
   - Study time predictions
   - Quiz performance predictions
   - Progress predictions
   - Trend analysis

3. **Personalized Recommendations**
   - Study recommendations
   - Quiz recommendations
   - Progress recommendations
   - Priority-based suggestions

## Dependencies

- FastAPI
- SQLAlchemy
- Pandas
- NumPy
- Scikit-learn
- Plotly
- Python-Jose (JWT)
- Uvicorn 