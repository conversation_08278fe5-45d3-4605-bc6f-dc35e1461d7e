import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { TokenBlacklistService } from './token-blacklist.service';
import { v4 as uuidv4 } from 'uuid';
import { CacheService } from '../../cache/cache.service';

// Define the type for token data
interface TokenData {
  userId: string;
  createdAt: string;
}

@Injectable()
export class RefreshTokenService {
  constructor(
    private jwtService: JwtService,
    private configService: ConfigService,
    private tokenBlacklistService: TokenBlacklistService,
    private cacheService: CacheService,
  ) {}

  async createRefreshToken(userId: string): Promise<string> {
    const refreshToken = uuidv4();
    const refreshTokenExpiry = parseInt(this.configService.get('JWT_REFRESH_EXPIRATION', '604800')); // 7 days

    // Store the refresh token with user association
    const tokenData: TokenData = {
      userId,
      createdAt: new Date().toISOString()
    };

    await this.cacheService.set(
      `refresh_token:${refreshToken}`,
      tokenData,
      refreshTokenExpiry
    );

    return refreshToken;
  }

  async validateRefreshToken(refreshToken: string, userId: string): Promise<boolean> {
    const tokenData = await this.cacheService.get(`refresh_token:${refreshToken}`) as TokenData | null;

    if (!tokenData || tokenData.userId !== userId) {
      return false;
    }

    // Check if token is too old (optional additional security)
    const createdAt = new Date(tokenData.createdAt);
    const maxAge = parseInt(this.configService.get('REFRESH_TOKEN_MAX_AGE', '2592000')); // 30 days
    if (Date.now() - createdAt.getTime() > maxAge * 1000) {
      await this.revokeRefreshToken(refreshToken);
      return false;
    }

    return true;
  }

  async revokeRefreshToken(refreshToken: string): Promise<void> {
    // Remove from cache
    await this.cacheService.delete(`refresh_token:${refreshToken}`);

    // Add to blacklist for extra security
    const blacklistExpiry = parseInt(this.configService.get('JWT_REFRESH_EXPIRATION', '604800')); // 7 days
    await this.tokenBlacklistService.addToBlacklist(refreshToken, blacklistExpiry);
  }

  async revokeAllUserTokens(userId: string): Promise<void> {
    // This would require scanning all refresh tokens, which might be expensive
    // Consider maintaining a separate index of user->tokens if this is needed frequently
    const pattern = `refresh_token:*`;
    const tokens = await this.cacheService.keys(pattern);

    for (const tokenKey of tokens) {
      const tokenData = await this.cacheService.get(tokenKey) as TokenData | null;
      if (tokenData && tokenData.userId === userId) {
        const refreshToken = tokenKey.replace('refresh_token:', '');
        await this.revokeRefreshToken(refreshToken);
      }
    }
  }
}