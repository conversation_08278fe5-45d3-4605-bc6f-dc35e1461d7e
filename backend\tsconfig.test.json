{
    "extends": "./tsconfig.json",
    "compilerOptions": {
      "outDir": "./dist/test",
      "rootDir": "./test"
    },
    "include": ["test/**/*"]
  }
// Compare this snippet from backend/test/jest-integration.json:
// {
//   "moduleFileExtensions": ["js", "json", "ts"],

//   "rootDir": ".",

//   "testEnvironment": "node",
//   "testRegex": ".integration.spec.ts$",
//   "transform": {
//     "^.+\\.(t|j)s$": "ts-jest"