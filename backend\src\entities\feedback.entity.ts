import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, <PERSON>umn, ManyToOne } from "typeorm";
import { User } from "./user.entity";
import { Material } from "./materials.entity";
import { Unit } from "./unit.entity";

@Entity()
export class Feedback {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @ManyToOne(() => User, (user: User) => user.feedback)
  user: User;

  @ManyToOne(() => Material, { nullable: true })
  material: Material;

  @ManyToOne(() => Unit, { nullable: true })
  unit: Unit;

  @Column("text")
  comments: string;

  @Column({ type: "integer", nullable: true })
  rating: number;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  created_at: Date;
}