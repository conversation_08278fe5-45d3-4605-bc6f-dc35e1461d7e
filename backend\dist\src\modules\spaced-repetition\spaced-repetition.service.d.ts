import { Repository } from 'typeorm';
import { Material } from '../../entities/materials.entity';
import { Progress } from '../../entities/progress.entity';
interface FlashcardItem {
    id: number;
    content: string;
    answer: string;
    ease_factor: number;
    interval: number;
    next_review_date: Date;
}
export declare class SpacedRepetitionService {
    private progressRepository;
    private materialRepository;
    constructor(progressRepository: Repository<Progress>, materialRepository: Repository<Material>);
    calculateNextReview(quality: number, item: FlashcardItem): FlashcardItem;
    addFlashcard(userId: number, content: string, answer: string): Promise<FlashcardItem>;
    calculateNextReviewDate(userId: string, materialId: string): Promise<Date>;
    getDueReviews(userId: string): Promise<Progress[]>;
    getDueFlashcards(userId: number, limit?: number): Promise<FlashcardItem[]>;
    getFlashcardById(id: number): Promise<FlashcardItem>;
    recordFlashcardResponse(flashcardId: number, userId: number, quality: number): Promise<void>;
    getFlashcardMaterials(userId: number): Promise<Material[]>;
    addMaterial(userId: number, content: string, answer: string): Promise<Material>;
    updateMaterial(id: number, content: string, answer: string): Promise<Material>;
    deleteMaterial(id: number): Promise<void>;
    getProgress(userId: number): Promise<Progress[]>;
    addProgress(userId: number, materialId: number): Promise<Progress>;
    updateProgress(id: number, materialId: number): Promise<Progress>;
}
export {};
