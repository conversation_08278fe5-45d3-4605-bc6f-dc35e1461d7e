import { Material } from '@/types/material';
import { api } from './api';
import { mockApiService } from './mockApiService';
import { ApiError } from '@/types/api';

export interface Material {
  id: string;
  title: string;
  description?: string;
  type?: string;
  author?: string;
  uploadDate?: string;
  size?: string;
  url?: string;
}

const materialService = {
  async getMaterials(): Promise<Material[]> {
    try {
      const response = await api.get('/materials');
      return response.data;
    } catch (error) {
      console.warn('Error fetching materials from API, using mock data:', error);
      try {
        // Fallback to mock API
        return await mockApiService.getMaterials();
      } catch (mockError) {
        console.error('Error fetching materials from mock API:', mockError);
        throw new ApiError({
          code: 'MATERIALS_FETCH_ERROR',
          message: mockError instanceof Error ? mockError.message : 'Failed to fetch materials',
          status: 500
        });
      }
    }
  },

  async getMaterialById(id: string): Promise<Material> {
    try {
      const response = await api.get(`/materials/${id}`);
      return response.data;
    } catch (error) {
      console.warn('Error fetching material by ID from API, using mock data:', error);
      try {
        // Fallback to mock API
        return await mockApiService.getMaterialById(id);
      } catch (mockError) {
        console.error('Error fetching material by ID from mock API:', mockError);
        throw mockError;
      }
    }
  },

  async uploadMaterial(formData: FormData): Promise<Material> {
    try {
      const response = await api.post('/materials/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.warn('Error uploading material to API, using mock data:', error);
      try {
        // Fallback to mock API
        // Extract data from FormData
        const title = formData.get('title') as string;
        const description = formData.get('description') as string;
        const file = formData.get('file') as File;
        const type = file.name.split('.').pop() || 'unknown';

        return await mockApiService.createMaterial({
          title,
          description,
          type,
          author: 'Current User',
        });
      } catch (mockError) {
        console.error('Error uploading material to mock API:', mockError);
        throw new ApiError({
          code: 'MATERIAL_UPLOAD_ERROR',
          message: mockError instanceof Error ? mockError.message : 'Failed to upload material',
          status: 500
        });
      }
    }
  },

  async deleteMaterial(id: string): Promise<void> {
    try {
      await api.delete(`/materials/${id}`);
    } catch (error) {
      console.warn('Error deleting material from API, using mock data:', error);
      try {
        // Fallback to mock API
        await mockApiService.deleteMaterial(id);
      } catch (mockError) {
        console.error('Error deleting material from mock API:', mockError);
        throw mockError;
      }
    }
  },

  async shareMaterial(id: string, userIds: string[]): Promise<void> {
    try {
      await api.post('/materials/share', { materialId: id, userIds });
    } catch (error) {
      console.warn('Error sharing material via API, using mock data:', error);
      // Mock API doesn't have a share endpoint, so we'll just simulate success
      await new Promise(resolve => setTimeout(resolve, 500));
      console.log('Mock share material:', { id, userIds });
    }
  }
};

export default materialService;
