import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Flashcard } from '../entities/flashcard.entity';
import { QuizQuestion } from '../entities/quiz-question.entity';
import { LessThanOrEqual } from 'typeorm';

@Injectable()
export class FlashcardsService {
    constructor(
        @InjectRepository(Flashcard)
        private flashcardRepository: Repository<Flashcard>,
        @InjectRepository(QuizQuestion)
        private quizQuestionRepository: Repository<QuizQuestion>,
    ) {}

    async createFlashcard(userId: string, questionId: string): Promise<Flashcard> {
        const question = await this.quizQuestionRepository.findOne({
            where: { id: questionId }
        });

        if (!question) {
            throw new NotFoundException('Question not found');
        }

        const flashcard = this.flashcardRepository.create({
            user: { id: userId },
            question: { id: questionId },
            ease_factor: 2.5,
            interval: 1,
            next_review: new Date(),
            correct_streak: 0,
            last_review: new Date()
        });

        return this.flashcardRepository.save(flashcard);
    }

    async getDueCards(userId: string): Promise<Flashcard[]> {
        return this.flashcardRepository.find({
            where: {
                user: { id: userId },
                next_review: LessThanOrEqual(new Date())
            },
            relations: ['question'],
            order: {
                next_review: 'ASC'
            }
        });
    }

    async updateCard(cardId: string, quality: number): Promise<Flashcard> {
        const card = await this.flashcardRepository.findOne({
            where: { id: cardId }
        });

        if (!card) {
            throw new NotFoundException('Flashcard not found');
        }

        // SM-2 Algorithm
        const newEf = Math.max(
            1.3,
            card.ease_factor + (0.1 - (5 - quality) * (0.08 + (5 - quality) * 0.02))
        );

        let newInterval;
        if (quality >= 3) {
            if (card.correct_streak === 0) {
                newInterval = 1;
            } else if (card.correct_streak === 1) {
                newInterval = 6;
            } else {
                newInterval = Math.round(card.interval * newEf);
            }
            card.correct_streak += 1;
        } else {
            card.correct_streak = 0;
            newInterval = 1;
        }

        card.ease_factor = newEf;
        card.interval = newInterval;
        card.next_review = new Date(Date.now() + newInterval * 24 * 60 * 60 * 1000);
        card.last_review = new Date();

        return this.flashcardRepository.save(card);
    }

    async getCardStats(userId: string): Promise<{
        total: number;
        due: number;
        upcoming: number;
        averageInterval: number;
    }> {
        const cards = await this.flashcardRepository.find({
            where: { user: { id: userId } }
        });

        const now = new Date();
        const dueCards = cards.filter((card: Flashcard) => card.next_review <= now);
        const upcomingCards = cards.filter((card: Flashcard) => card.next_review > now);

        return {
            total: cards.length,
            due: dueCards.length,
            upcoming: upcomingCards.length,
            averageInterval: cards.reduce((sum: number, card: Flashcard) => sum + card.interval, 0) / cards.length,
        };
    }

    async syncCards(userId: string, cards: Partial<Flashcard>[]): Promise<Flashcard[]> {
        const syncedCards: Flashcard[] = [];

        for (const card of cards) {
            const existingCard = await this.flashcardRepository.findOne({
                where: {
                    user: { id: userId },
                    question: { id: card.question?.id }
                }
            });

            if (existingCard) {
                // Update existing card if server version is newer
                if (existingCard.updated_at && card.updated_at && existingCard.updated_at > card.updated_at) {
                    syncedCards.push(existingCard);
                } else {
                    const updatedCard = await this.flashcardRepository.save({
                        ...existingCard,
                        ...card,
                        user: { id: userId },
                        question: { id: card.question?.id }
                    });
                    syncedCards.push(updatedCard);
                }
            } else {
                // Create new card
                const newCard = await this.createFlashcard(userId, card.question?.id || '');
                syncedCards.push(newCard);
            }
        }

        return syncedCards;
    }
} 