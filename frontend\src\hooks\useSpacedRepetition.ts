import { useState, useEffect, useCallback } from 'react';
import { spacedRepetition } from '../services/spacedRepetition';

interface Flashcard {
  id: string;
  userId: string;
  questionId: string;
  ef: number;
  interval: number;
  nextReview: Date;
  correctStreak: number;
  lastReview: Date;
  synced: boolean;
}

interface CardStats {
  total: number;
  due: number;
  upcoming: number;
  averageInterval: number;
}

export function useSpacedRepetition(userId: string) {
  const [dueCards, setDueCards] = useState<Flashcard[]>([]);
  const [currentCard, setCurrentCard] = useState<Flashcard | null>(null);
  const [stats, setStats] = useState<CardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const loadDueCards = useCallback(async () => {
    try {
      setIsLoading(true);
      const cards = await spacedRepetition.getDueCards(userId);
      setDueCards(cards);
      if (cards.length > 0) {
        setCurrentCard(cards[0]);
      }
      const cardStats = await spacedRepetition.getCardStats(userId);
      setStats(cardStats);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to load cards'));
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    loadDueCards();
  }, [loadDueCards]);

  const rateCard = useCallback(async (quality: number) => {
    if (!currentCard) return;

    try {
      const updatedCard = await spacedRepetition.updateCard(currentCard.id, quality);
      if (updatedCard) {
        // Remove the current card from due cards
        setDueCards(prev => prev.filter(card => card.id !== currentCard.id));
        
        // Set the next card as current
        if (dueCards.length > 1) {
          setCurrentCard(dueCards[1]);
        } else {
          setCurrentCard(null);
        }

        // Update stats
        const newStats = await spacedRepetition.getCardStats(userId);
        setStats(newStats);
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to update card'));
    }
  }, [currentCard, dueCards, userId]);

  const createCard = useCallback(async (questionId: string) => {
    try {
      const card = await spacedRepetition.createFlashcard(userId, questionId);
      if (card) {
        setDueCards(prev => [...prev, card]);
        if (!currentCard) {
          setCurrentCard(card);
        }
        const newStats = await spacedRepetition.getCardStats(userId);
        setStats(newStats);
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to create card'));
    }
  }, [userId, currentCard]);

  const syncCards = useCallback(async () => {
    try {
      const unsyncedCards = await spacedRepetition.getUnsyncedCards();
      if (unsyncedCards.length > 0) {
        // Here you would typically send the cards to your backend
        // For now, we'll just mark them as synced
        await spacedRepetition.markAsSynced(unsyncedCards.map(card => card.id));
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to sync cards'));
    }
  }, []);

  return {
    dueCards,
    currentCard,
    stats,
    isLoading,
    error,
    rateCard,
    createCard,
    syncCards,
    refreshCards: loadDueCards,
  };
} 