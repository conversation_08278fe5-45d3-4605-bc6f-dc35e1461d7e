<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Medical Education Platform</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 600px;
            text-align: center;
            padding: 40px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #2563eb;
            margin-bottom: 20px;
        }

        p {
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .icon {
            font-size: 64px;
            margin-bottom: 20px;
        }

        .button {
            background-color: #2563eb;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .button:hover {
            background-color: #1d4ed8;
        }

        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            background-color: #fee2e2;
            color: #dc2626;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📶</div>
        <h1>You're Offline</h1>
        <p>Don't worry! You can still access your cached content and continue learning.</p>
        <p>Your progress will be automatically synced when you're back online.</p>
        <a href="/" class="button">Try Again</a>
        <div class="status" id="status">Checking connection...</div>
    </div>

    <script>
        function updateStatus() {
            const status = document.getElementById('status');
            if (navigator.onLine) {
                status.textContent = 'You are back online!';
                status.style.backgroundColor = '#dcfce7';
                status.style.color = '#16a34a';
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000);
            } else {
                status.textContent = 'Still offline. Please check your internet connection.';
                status.style.backgroundColor = '#fee2e2';
                status.style.color = '#dc2626';
            }
        }

        window.addEventListener('online', updateStatus);
        window.addEventListener('offline', updateStatus);
        updateStatus();
    </script>
</body>
</html> 