import { Flashcard } from './flashcardApi';
import { QuizQuestion } from '../types/quiz';

export interface StudySession {
    startTime: Date;
    endTime: Date;
    cardsReviewed: number;
    correctAnswers: number;
    incorrectAnswers: number;
    averageResponseTime: number;
}

export interface PerformanceMetrics {
    totalCards: number;
    cardsDue: number;
    averageInterval: number;
    successRate: number;
    streakDays: number;
    lastReviewDate: Date;
}

export class AnalyticsService {
    private static readonly SESSION_KEY = 'study_session';
    private static readonly METRICS_KEY = 'performance_metrics';

    static startSession(): void {
        const session: StudySession = {
            startTime: new Date(),
            endTime: new Date(),
            cardsReviewed: 0,
            correctAnswers: 0,
            incorrectAnswers: 0,
            averageResponseTime: 0,
        };
        localStorage.setItem(this.SESSION_KEY, JSON.stringify(session));
    }

    static endSession(): StudySession {
        const session = this.getCurrentSession();
        session.endTime = new Date();
        localStorage.setItem(this.SESSION_KEY, JSON.stringify(session));
        return session;
    }

    static updateSession(card: Flashcard, isCorrect: boolean, responseTime: number): void {
        const session = this.getCurrentSession();
        session.cardsReviewed++;
        if (isCorrect) {
            session.correctAnswers++;
        } else {
            session.incorrectAnswers++;
        }
        session.averageResponseTime = 
            (session.averageResponseTime * (session.cardsReviewed - 1) + responseTime) / 
            session.cardsReviewed;
        localStorage.setItem(this.SESSION_KEY, JSON.stringify(session));
    }

    static calculatePerformanceMetrics(cards: Flashcard[]): PerformanceMetrics {
        const now = new Date();
        const metrics: PerformanceMetrics = {
            totalCards: cards.length,
            cardsDue: cards.filter(card => new Date(card.nextReview) <= now).length,
            averageInterval: 0,
            successRate: 0,
            streakDays: 0,
            lastReviewDate: new Date(0),
        };

        if (cards.length > 0) {
            metrics.averageInterval = cards.reduce((sum, card) => sum + card.interval, 0) / cards.length;
            metrics.successRate = cards.reduce((sum, card) => sum + (card.correctStreak > 0 ? 1 : 0), 0) / cards.length;
            metrics.lastReviewDate = new Date(Math.max(...cards.map(card => new Date(card.lastReview).getTime())));
        }

        return metrics;
    }

    static trackQuizPerformance(questions: QuizQuestion[], responses: Record<string, boolean>): void {
        const correctCount = Object.values(responses).filter(Boolean).length;
        const totalQuestions = questions.length;
        const successRate = (correctCount / totalQuestions) * 100;

        // Send to analytics backend
        this.sendAnalyticsEvent('quiz_completed', {
            totalQuestions,
            correctCount,
            successRate,
            timestamp: new Date().toISOString(),
        });
    }

    private static getCurrentSession(): StudySession {
        const session = localStorage.getItem(this.SESSION_KEY);
        return session ? JSON.parse(session) : this.startSession();
    }

    private static sendAnalyticsEvent(eventName: string, data: any): void {
        // Implement your analytics service integration here
        // Example: Google Analytics, Mixpanel, etc.
        console.log('Analytics Event:', eventName, data);
    }
} 