import React, { useState, useEffect } from 'react';
import { useStudy } from '../../hooks/useStudy';
import {
    Box,
    Card,
    CardContent,
    Typography,
    Button,
    CircularProgress,
    IconButton,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    useTheme,
} from '@mui/material';
import {
    PlayArrow,
    Pause,
    Stop,
    Timer,
    Book,
    Quiz,
    Note,
} from '@mui/icons-material';

interface StudySessionProps {
    topicId: string;
    onSessionEnd: () => void;
}

export const StudySession: React.FC<StudySessionProps> = ({ topicId, onSessionEnd }) => {
    const { startStudySession, endStudySession } = useStudy();
    const theme = useTheme();

    const [sessionId, setSessionId] = useState<string | null>(null);
    const [isActive, setIsActive] = useState(false);
    const [elapsedTime, setElapsedTime] = useState(0);
    const [activities, setActivities] = useState<any[]>([]);
    const [showEndDialog, setShowEndDialog] = useState(false);
    const [notes, setNotes] = useState('');

    useEffect(() => {
        let timer: NodeJS.Timeout;
        if (isActive) {
            timer = setInterval(() => {
                setElapsedTime(prev => prev + 1);
            }, 1000);
        }
        return () => clearInterval(timer);
    }, [isActive]);

    const handleStart = async () => {
        try {
            const session = await startStudySession(topicId);
            setSessionId(session.id);
            setIsActive(true);
        } catch (error) {
            console.error('Error starting session:', error);
        }
    };

    const handlePause = () => {
        setIsActive(false);
    };

    const handleResume = () => {
        setIsActive(true);
    };

    const handleStop = () => {
        setShowEndDialog(true);
    };

    const handleEndSession = async () => {
        if (!sessionId) return;

        try {
            await endStudySession(sessionId, [
                ...activities,
                {
                    type: 'notes',
                    durationMinutes: Math.floor(elapsedTime / 60),
                    content: notes
                }
            ]);
            onSessionEnd();
        } catch (error) {
            console.error('Error ending session:', error);
        }
    };

    const addActivity = (type: string) => {
        setActivities(prev => [
            ...prev,
            {
                type,
                durationMinutes: Math.floor(elapsedTime / 60),
                timestamp: new Date()
            }
        ]);
    };

    const formatTime = (seconds: number) => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    return (
        <Box>
            <Card>
                <CardContent>
                    <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                        <Typography variant="h5">
                            Study Session
                        </Typography>
                        <Typography variant="h4" color="primary">
                            {formatTime(elapsedTime)}
                        </Typography>
                    </Box>

                    <Box display="flex" justifyContent="center" gap={2} mb={3}>
                        {!isActive ? (
                            <Button
                                variant="contained"
                                color="primary"
                                startIcon={<PlayArrow />}
                                onClick={handleStart}
                            >
                                Start Session
                            </Button>
                        ) : (
                            <>
                                <Button
                                    variant="outlined"
                                    startIcon={<Pause />}
                                    onClick={handlePause}
                                >
                                    Pause
                                </Button>
                                <Button
                                    variant="contained"
                                    color="error"
                                    startIcon={<Stop />}
                                    onClick={handleStop}
                                >
                                    End Session
                                </Button>
                            </>
                        )}
                    </Box>

                    <Box display="flex" justifyContent="center" gap={2}>
                        <IconButton
                            color="primary"
                            onClick={() => addActivity('reading')}
                            title="Add Reading Activity"
                        >
                            <Book />
                        </IconButton>
                        <IconButton
                            color="primary"
                            onClick={() => addActivity('quiz')}
                            title="Add Quiz Activity"
                        >
                            <Quiz />
                        </IconButton>
                        <IconButton
                            color="primary"
                            onClick={() => addActivity('notes')}
                            title="Add Notes Activity"
                        >
                            <Note />
                        </IconButton>
                    </Box>

                    {activities.length > 0 && (
                        <Box mt={3}>
                            <Typography variant="subtitle1" gutterBottom>
                                Activities
                            </Typography>
                            {activities.map((activity, index) => (
                                <Box
                                    key={index}
                                    display="flex"
                                    alignItems="center"
                                    gap={1}
                                    mb={1}
                                >
                                    {activity.type === 'reading' && <Book />}
                                    {activity.type === 'quiz' && <Quiz />}
                                    {activity.type === 'notes' && <Note />}
                                    <Typography variant="body2">
                                        {activity.type.charAt(0).toUpperCase() + activity.type.slice(1)} - {activity.durationMinutes} minutes
                                    </Typography>
                                </Box>
                            ))}
                        </Box>
                    )}
                </CardContent>
            </Card>

            <Dialog open={showEndDialog} onClose={() => setShowEndDialog(false)}>
                <DialogTitle>End Study Session</DialogTitle>
                <DialogContent>
                    <Typography variant="body1" gutterBottom>
                        Session Duration: {formatTime(elapsedTime)}
                    </Typography>
                    <TextField
                        fullWidth
                        multiline
                        rows={4}
                        label="Session Notes"
                        value={notes}
                        onChange={(e) => setNotes(e.target.value)}
                        margin="normal"
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setShowEndDialog(false)}>Cancel</Button>
                    <Button onClick={handleEndSession} color="primary">
                        End Session
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
}; 