import { Repository, DeepPartial } from 'typeorm';
import { UserSecuritySettings } from '../../../entities/security.entity';
import { BaseRepository } from '../../../common/repositories/base.repository';
export declare class UserSecuritySettingsRepository extends BaseRepository<UserSecuritySettings> {
    constructor(repository: Repository<UserSecuritySettings>);
    create(data: DeepPartial<UserSecuritySettings>): UserSecuritySettings;
    save(entity: UserSecuritySettings | DeepPartial<UserSecuritySettings>): Promise<UserSecuritySettings>;
}
