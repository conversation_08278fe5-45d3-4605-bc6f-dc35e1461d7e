import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { MaterialsService } from "./materials.service";
import { MaterialsController } from "./materials.controller";
import { Material } from "../../entities/materials.entity";
import { User } from "../../entities/user.entity";
import { MaterialShare } from "../../entities/material_shares.entity";
import { ConfigModule } from "@nestjs/config";

@Module({
  imports: [
    TypeOrmModule.forFeature([Material, User, MaterialShare]),
    ConfigModule
  ],
  providers: [MaterialsService],
  controllers: [MaterialsController],
})
export class MaterialsModule {}