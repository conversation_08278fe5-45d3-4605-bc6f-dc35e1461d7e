import httpx
import asyncio
from datetime import datetime
import json

async def test_auth_integration():
    """
    Test the JWT authentication integration between NestJS and Python analytics
    """
    # NestJS backend URL
    nest_url = "http://localhost:3000"
    # Python analytics URL
    analytics_url = "http://localhost:5000"
    
    async with httpx.AsyncClient() as client:
        try:
            # Step 1: Login to NestJS backend
            login_data = {
                "email": "<EMAIL>",
                "password": "test"
            }
            response = await client.post(f"{nest_url}/auth/login", json=login_data)
            if response.status_code != 200:
                print(f"Failed to login: {response.status_code}")
                print(response.text)
                return
            
            token = response.json().get("access_token")
            if not token:
                print("No access token received")
                return
                
            print("✓ Successfully obtained access token from NestJS")
            
            # Step 2: Test authentication status endpoint
            headers = {"Authorization": f"Bearer {token}"}
            response = await client.get(f"{analytics_url}/auth/status", headers=headers)
            if response.status_code != 200:
                print(f"Failed to verify auth status: {response.status_code}")
                print(response.text)
                return
                
            print("✓ Successfully verified authentication status")
            print(f"Current user: {json.dumps(response.json(), indent=2)}")
            
            # Step 3: Test analytics endpoint
            response = await client.get(
                f"{analytics_url}/analytics/user/test",
                headers=headers
            )
            if response.status_code != 200:
                print(f"Failed to get analytics: {response.status_code}")
                print(response.text)
                return
                
            print("✓ Successfully retrieved analytics data")
            
        except Exception as e:
            print(f"Error during testing: {str(e)}")

if __name__ == "__main__":
    print("Testing JWT authentication integration...")
    print("----------------------------------------")
    asyncio.run(test_auth_integration())
