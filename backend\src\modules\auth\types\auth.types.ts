export interface JwtPayload {
  sub: string; // user id
  email: string;
  role?: string;
  iat?: number;
  exp?: number;
}

export interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  user: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
    role?: string;
    isEmailVerified: boolean;
  };
}

export interface DeviceInfo {
  name?: string;
  type?: string;
  os?: string;
  browser?: string;
  browserVersion?: string;
  token?: string;
  userAgent?: string;
  ipAddress?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
  deviceId?: string;
  twoFactorToken?: string;
  deviceInfo?: DeviceInfo;
  rememberMe?: boolean;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  name?: string;  // For backward compatibility
  deviceId?: string;
  deviceInfo?: DeviceInfo;
  role?: string;
}

export interface TokenValidationResult {
  isValid: boolean;
  payload?: JwtPayload;
  error?: string;
}

export interface SessionInfo {
  id: string;
  userId: string;
  deviceId?: string;
  userAgent?: string;
  ipAddress?: string;
  lastAccessed: Date;
  isActive: boolean;
  createdAt: Date;
}

export interface SecurityEventType {
  LOGIN_SUCCESS: 'LOGIN_SUCCESS';
  LOGIN_FAILURE: 'LOGIN_FAILURE';
  PASSWORD_CHANGE: 'PASSWORD_CHANGE';
  TWO_FACTOR_ENABLED: 'TWO_FACTOR_ENABLED';
  TWO_FACTOR_DISABLED: 'TWO_FACTOR_DISABLED';
  SESSION_REVOKED: 'SESSION_REVOKED';
  EMAIL_VERIFIED: 'EMAIL_VERIFIED';
  SECURITY_SETTINGS_UPDATE: 'SECURITY_SETTINGS_UPDATE';
  SUSPICIOUS_ACTIVITY: 'SUSPICIOUS_ACTIVITY';
}

export const SECURITY_EVENT_TYPES: SecurityEventType = {
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  PASSWORD_CHANGE: 'PASSWORD_CHANGE',
  TWO_FACTOR_ENABLED: 'TWO_FACTOR_ENABLED',
  TWO_FACTOR_DISABLED: 'TWO_FACTOR_DISABLED',
  SESSION_REVOKED: 'SESSION_REVOKED',
  EMAIL_VERIFIED: 'EMAIL_VERIFIED',
  SECURITY_SETTINGS_UPDATE: 'SECURITY_SETTINGS_UPDATE',
  SUSPICIOUS_ACTIVITY: 'SUSPICIOUS_ACTIVITY',
};
