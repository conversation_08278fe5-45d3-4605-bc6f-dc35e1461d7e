"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SyncService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const quiz_attempt_entity_1 = require("../../entities/quiz-attempt.entity");
const progress_entity_1 = require("../../entities/progress.entity");
let SyncService = class SyncService {
    constructor(quizAttemptRepository, progressRepository) {
        this.quizAttemptRepository = quizAttemptRepository;
        this.progressRepository = progressRepository;
    }
    async syncBulk(userId, syncData) {
        const results = {
            quiz_attempts: await this.processQuizAttempts(userId, syncData.quizAttempts),
            progress: await this.processProgress(userId, syncData.progress),
        };
        return {
            success: true,
            results,
            timestamp: new Date().toISOString(),
        };
    }
    async processQuizAttempts(userId, attempts) {
        const results = {
            created: 0,
            updated: 0,
            conflicts: 0,
        };
        for (const attempt of attempts) {
            try {
                const existingAttempt = await this.quizAttemptRepository.findOne({
                    where: { id: attempt.id },
                });
                if (existingAttempt) {
                    if (existingAttempt.updated_at > attempt.updatedAt) {
                        results.conflicts++;
                        continue;
                    }
                    await this.quizAttemptRepository.update(attempt.id, {
                        ...attempt,
                        user: { id: userId },
                    });
                    results.updated++;
                }
                else {
                    await this.quizAttemptRepository.save({
                        ...attempt,
                        user: { id: userId },
                    });
                    results.created++;
                }
            }
            catch (error) {
                results.conflicts++;
            }
        }
        return results;
    }
    async processProgress(userId, progress) {
        const results = {
            created: 0,
            updated: 0,
            conflicts: 0,
        };
        for (const item of progress) {
            try {
                const existingProgress = await this.progressRepository.findOne({
                    where: {
                        user: { id: userId },
                        material: { id: item.materialId },
                    },
                });
                if (existingProgress) {
                    if (existingProgress.updated_at > item.updatedAt) {
                        results.conflicts++;
                        continue;
                    }
                    await this.progressRepository.update({ user: { id: userId }, material: { id: item.materialId } }, {
                        ...item,
                        user: { id: userId },
                        material: { id: item.materialId },
                    });
                    results.updated++;
                }
                else {
                    await this.progressRepository.save({
                        ...item,
                        user: { id: userId },
                        material: { id: item.materialId },
                    });
                    results.created++;
                }
            }
            catch (error) {
                results.conflicts++;
            }
        }
        return results;
    }
    async getSyncStatus(userId) {
        const lastQuizAttempt = await this.quizAttemptRepository.findOne({
            where: { user: { id: userId } },
            order: { updated_at: 'DESC' },
        });
        const lastProgress = await this.progressRepository.findOne({
            where: { user: { id: userId } },
            order: { updated_at: 'DESC' },
        });
        return {
            lastSync: {
                quiz_attempts: lastQuizAttempt?.updated_at || null,
                progress: lastProgress?.updated_at || null,
            },
            pendingChanges: {
                quiz_attempts: await this.quizAttemptRepository.count({
                    where: { user: { id: userId } }
                }),
                progress: await this.progressRepository.count({
                    where: { user: { id: userId } }
                }),
            },
        };
    }
    async syncUserProgress(userId) {
        const unsyncedProgress = await this.progressRepository.find({
            where: { user: { id: userId } },
            relations: ['user', 'material']
        });
        for (const progress of unsyncedProgress) {
            progress.is_completed = true;
            await this.progressRepository.save(progress);
        }
    }
};
exports.SyncService = SyncService;
exports.SyncService = SyncService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(quiz_attempt_entity_1.QuizAttempt)),
    __param(1, (0, typeorm_1.InjectRepository)(progress_entity_1.Progress)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], SyncService);
//# sourceMappingURL=sync.service.js.map