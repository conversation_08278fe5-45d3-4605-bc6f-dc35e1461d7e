import { Injectable } from '@nestjs/common';
import { CacheService } from '../../cache/cache.service';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class TokenBlacklistService {
  private readonly blacklistPrefix = 'token:blacklist:';
  private readonly defaultTTL = 24 * 60 * 60; // 24 hours in seconds

  constructor(
    private readonly cacheService: CacheService,
    private readonly jwtService: JwtService,
  ) {}

  async addToBlacklist(token: string, expires: number): Promise<void> {
    await this.cacheService.set(`${this.blacklistPrefix}${token}`, true, expires);
  }

  async blacklistToken(token: string): Promise<void> {
    try {
      // Decode token to get expiration
      const decoded = this.jwtService.decode(token);
      if (!decoded || typeof decoded === 'string') {
        throw new Error('Invalid token format');
      }

      // Calculate TTL based on token expiration
      const exp = decoded.exp;
      const now = Math.floor(Date.now() / 1000);
      const ttl = exp ? exp - now : this.defaultTTL;

      // Add to blacklist with expiration
      await this.cacheService.set(
        `${this.blacklistPrefix}${token}`,
        true,
        ttl
      );
    } catch (error) {
      // If token is invalid, blacklist it for default period
      await this.cacheService.set(
        `${this.blacklistPrefix}${token}`,
        true,
        this.defaultTTL
      );
    }
  }

  async isBlacklisted(token: string): Promise<boolean> {
    return await this.cacheService.get(`${this.blacklistPrefix}${token}`) === true;
  }

  async clearExpiredTokens(): Promise<void> {
    // This is handled automatically by Redis TTL
    return;
  }
}