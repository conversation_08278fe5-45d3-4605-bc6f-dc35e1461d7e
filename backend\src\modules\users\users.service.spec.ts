import { Test, TestingModule } from '@nestjs/testing';
import { UsersService } from './users.service';
import { UsersRepository } from './repositories/users.repository';
import { CacheService } from '../../cache/cache.service';

describe('UsersService', () => {
  let service: UsersService;
  let mockUsersRepository: Partial<UsersRepository>;
  let mockCacheService: Partial<CacheService>;

  beforeEach(async () => {
    // Create mock implementations with jest.fn()
    const findByUsername = jest.fn();
    const findById = jest.fn();
    const findAll = jest.fn();
    const findByEmail = jest.fn();
    const create = jest.fn();
    const update = jest.fn();
    const deleteUser = jest.fn();

    // Create mock repository
    mockUsersRepository = {
      findByUsername,
      findById,
      findAll,
      findByEmail,
      create,
      update,
      delete: deleteUser
    };

    // Create mock cache service
    const getCacheFn = jest.fn();
    const setCacheFn = jest.fn();
    const deleteCacheFn = jest.fn();
    const clearCacheFn = jest.fn();
    const mgetCacheFn = jest.fn();
    const resetCacheFn = jest.fn();
    const generateKeyFn = jest.fn((prefix, params) =>
      `${prefix}:${Object.values(params).join(':')}`
    );

    mockCacheService = {
      get: getCacheFn,
      set: setCacheFn,
      delete: deleteCacheFn,
      clear: clearCacheFn,
      mget: mgetCacheFn,
      reset: resetCacheFn,
      generateKey: generateKeyFn
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: UsersRepository,
          useValue: mockUsersRepository,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});